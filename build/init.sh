#!/bin/sh
set -e
 
echo "ls /app"
ls /app -a
 
echo "ls /app/blobenv"
ls /app/blobenv -a
 
if [ -d "/app/blobenv" ]; then
  env_file=$(ls /app/blobenv | grep "env.sh")
 
  if [ -n "$env_file" ]; then
    echo "env.sh found in /app/blobenv"
    source /app/blobenv/$env_file
    echo "Variable PLATFORM_> from env.sh: $PLATFORM"
    echo "Variable RELEASE_TYPE_> from env.sh: $RELEASE_TYPE"
  else
    echo "env.sh not found in /app/blobenv"
  fi
else
  echo "/app/blobenv directory not found"
fi

echo "Starting SSH ..."
/usr/sbin/sshd

export NODE_TLS_REJECT_UNAUTHORIZED=0

pnpm exec cross-env PLATFORM=$PLATFORM ENV=$RELEASE_TYPE pnpm start