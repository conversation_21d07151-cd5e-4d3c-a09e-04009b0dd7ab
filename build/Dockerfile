FROM node:lts-alpine AS base
ENV PNPM_HOME="/pnpm"
ENV PATH="$PNPM_HOME:$PATH"
RUN apk add --update --no-cache python3 build-base gcc && ln -sf /usr/bin/python3 /usr/bin/python
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN corepack enable
RUN corepack prepare pnpm@latest-9 --activate

# FROM base AS build
# COPY . /usr/src/app
# WORKDIR /usr/src/app
# RUN --mount=type=cache,id=pnpm,target=/pnpm/store pnpm install --frozen-lockfile
# RUN pnpm --filter=main-nextjs-app build
# RUN pnpm deploy --filter=main-nextjs-app --prod /prod/app1
# RUN cp -r ./apps/main-nextjs-app/.next /prod/app1 
# RUN ls -a

FROM base AS runner
COPY ./src/prod/app /app
COPY ./build/init.sh /app
WORKDIR /app

# change password and Install opensshSSH
RUN  sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories \
     && apk update \
     && apk add --no-cache openssh tzdata \
     && echo "root:Docker!" | chpasswd \
     && chmod +x ./init.sh \
     && cd /etc/ssh/ \
     && ssh-keygen -A

COPY ./build/sshd_config /etc/ssh/

RUN pnpm --version
RUN ls -a
ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# COPY "./config/docker/init.sh" "./init.sh"

USER root

EXPOSE 3000 2222

ENV PORT 3000
# set hostname to localhost
ENV HOSTNAME "0.0.0.0"
ENV PROFILE "/etc/profile"

# CMD ["pnpm", "start"]
ENTRYPOINT "./init.sh" "${PROFILE}"

