{"root": true, "ignorePatterns": ["**/main-asia-app/**", "**/main-react-app/**", "**/libs/asia/**", "**/libs/domains/**"], "plugins": ["@nx", "react-prefer-function-component", "react-refresh", "simple-import-sort"], "extends": ["airbnb", "prettier", "plugin:@nx/react"], "rules": {"@nx/enforce-module-boundaries": "off", "react-hooks/exhaustive-deps": "off", "react/react-in-jsx-scope": "off", "react/jsx-filename-extension": "off", "import/prefer-default-export": "off", "import/extensions": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "jsx-a11y/anchor-is-valid": "off", "react/prop-types": "off", "react/no-unstable-nested-components": "off", "jsx-a11y/label-has-associated-control": "off", "react/require-default-props": "off", "react/no-unused-prop-types": "off", "react/function-component-definition": "off", "import/no-extraneous-dependencies": "off", "import/no-unresolved": "off", "consistent-return": "off", "no-unreachable": "off", "no-console": "warn", "react/jsx-props-no-spreading": "off", "camelcase": "off", "no-param-reassign": "off", "global-require": "off", "no-use-before-define": "off", "default-param-last": "off", "no-underscore-dangle": "off", "no-shadow": "off", "simple-import-sort/imports": "warn", "import/newline-after-import": "off"}, "overrides": [{"files": ["*.ts", "*.tsx"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["plugin:@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/no-shadow": "off", "@typescript-eslint/ban-ts-comment": "off", "@typescript-eslint/no-explicit-any": "off"}}]}