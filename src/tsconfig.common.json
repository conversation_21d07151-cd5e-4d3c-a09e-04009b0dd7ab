{
    "compilerOptions": {
        "baseUrl": "./",
        "target": "ES2020",
        "useDefineForClassFields": true,
        "module": "ESNext",
        "lib": ["ES2020", "DOM", "DOM.Iterable"],
        "skipLibCheck": true,

        /* 添加 JSX 支援 */
        "jsx": "react-jsx",
        "jsxImportSource": "react",
        "moduleResolution": "bundler",

        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true
    },
    "include": [
        "apps/**/*.ts",
        "apps/**/*.d.ts",
        "apps/**/*.tsx",
        "apps/**/*.vue",
        "packages/**/*.ts",
        "packages/**/*.d.ts",
        "packages/**/*.tsx",
        "packages/**/*.vue",
        "libs/**/*.ts",
        "libs/**/*.d.ts",
        "libs/**/*.tsx",
        "libs/**/*.vue"
    ],
    "exclude": ["dist", "node_modules", "**/*.spec.ts"]
}
