{"workspaceLayout": {"appsDir": "apps", "libsDir": "packages"}, "tasksRunnerOptions": {"default": {"runner": "nx/tasks-runners/default", "options": {"parallel": 5, "cacheableOperations": ["dev", "build", "lint", "preview"]}}}, "targetDefaults": {"dev": {"dependsOn": ["^build"]}, "build": {"dependsOn": ["^build"], "outputs": ["{projectRoot}/dist", "{projectRoot}/.next"]}, "lint": {"dependsOn": ["^lint"]}, "preview": {"dependsOn": ["^preview"]}}, "affected": {"defaultBase": "main"}, "plugins": [{"plugin": "@nx/react/router-plugin", "options": {"buildTargetName": "build", "devTargetName": "dev", "startTargetName": "start", "watchDepsTargetName": "watch-deps", "buildDepsTargetName": "build-deps", "typecheckTargetName": "typecheck"}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "eslint:lint"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "vite:build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "vite:dev", "previewTargetName": "vite:preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/next/plugin", "options": {"startTargetName": "next:start", "buildTargetName": "next:build", "devTargetName": "next:dev", "serveStaticTargetName": "serve-static", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "generators": {"@nx/react": {"library": {"unitTestRunner": "vitest"}}}}