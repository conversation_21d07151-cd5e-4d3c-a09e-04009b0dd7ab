{"name": "xe-frontend-v2", "private": true, "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "npm-run-all --parallel start-child:* start:main-nextjs", "start:main": "nx dev main-react-app", "start:main-nextjs:xe": "cross-env PLATFORM=xe ENV=dev nx dev main-nextjs-app", "start:asia": "cross-env PLATFORM=asia ENV=dev nx dev main-asia-app", "start:main-nextjs": "cross-env PLATFORM=$PLATFORM ENV=$ENV nx dev main-nextjs-app", "build:main": "nx build main-react-app", "build-app": "nx build $APP_PATH", "deploy-app": "rm -rf prod && pnpm deploy --filter=$APP_PATH --prod ./prod/app", "prepare-docker-resources": "npm-run-all build-app deploy-app", "prepare": "husky"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-react": "^7.27.1", "@commitlint/config-conventional": "^19.8.1", "@hookform/resolvers": "4.1.3", "@next/eslint-plugin-next": "^15.3.3", "@nx/eslint": "20.8.0", "@nx/eslint-plugin": "20.8.0", "@nx/js": "20.8.0", "@nx/next": "^20.8.2", "@nx/react": "^20.8.2", "@nx/vite": "20.7.1", "@nx/web": "20.7.1", "@reduxjs/toolkit": "^2.8.2", "@svgr/webpack": "^8.1.0", "@swc-node/register": "~1.9.2", "@swc/core": "~1.5.29", "@swc/helpers": "~0.5.17", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.17", "@types/node": "^20.19.0", "@types/post-robot": "^10.0.6", "@types/qs": "^6.14.0", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-react": "^4.5.2", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "autoprefixer": "^10.4.21", "classnames": "^2.5.1", "dayjs": "^1.11.13", "eslint": "~8.57.1", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-react-prefer-function-component": "^3.4.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-simple-import-sort": "^12.1.1", "husky": "^9.1.7", "jiti": "2.4.2", "jsdom": "~22.1.0", "lint-staged": "^15.5.2", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "nx": "^20.8.2", "postcss": "^8.5.4", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "qs": "^6.14.0", "react-redux": "^9.2.0", "react-hook-form": "^7.57.0", "tailwind": "link:@nx/react/tailwind", "tslib": "^2.8.1", "typescript": "~5.7.3", "vite": "^6.3.5", "vite-plugin-dts": "~4.5.4", "vitest": "^3.2.3"}, "engines": {"node": ">=18", "pnpm": "9.x"}, "packageManager": "pnpm@9.4.0+sha256.b6fd0bfda555e7e584ad7e56b30c68b01d5a04f9ee93989f4b93ca8473c49c74", "dependencies": {"@mayo/mayo-ui-beta": "^2.1.13", "@sentry/nextjs": "^8.55.0", "axios": "^1.9.0", "cross-env": "^7.0.3", "immer": "^10.1.1", "jotai": "2.9.0", "js-cookie": "^3.0.5", "next": "~14.2.29", "next-build-id": "^3.0.0", "next-intl": "3.15.3", "nextjs-toploader": "^3.8.16", "npm-run-all": "^4.1.5", "post-robot": "^8.0.32", "react": "18.3.1", "react-dom": "18.3.1", "react-error-boundary": "^4.1.2", "swr": "^2.3.3", "tailwindcss": "^3.4.17", "usehooks-ts": "3.1.0", "utils": "workspace:^", "zod": "^3.25.57", "zustand": "4.5.2"}}