{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "bundler", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "allowJs": true, "resolveJsonModule": true, "jsx": "react-jsx", "jsxImportSource": "react", "paths": {"xe/*": ["./libs/xe/src/lib/*"], "asia/*": ["./libs/asia/src/lib/*"]}}, "include": ["apps/**/*.ts", "apps/**/*.d.ts", "apps/**/*.tsx", "apps/**/*.vue", "packages/**/*.ts", "packages/**/*.d.ts", "packages/**/*.tsx", "packages/**/*.vue", "libs/**/*.ts", "libs/**/*.d.ts", "libs/**/*.tsx", "libs/**/*.vue"], "exclude": ["node_modules", "tmp"]}