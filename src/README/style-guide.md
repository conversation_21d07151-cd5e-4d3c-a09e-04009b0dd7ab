# style guide

## JavaScript guide

### 變數命名方式

使用駝峰式命名法 (Camel Case)：

```js
const userName = 'xxx';
const fileName = 'ooo';
const userList = ['111', '222', '333'];
const userMap = {
    111: {},
    222: {},
    333: {},
};
```

Flag 命名方式使用 is 前綴：

```js
const isOpen = true;
const isLogin = false;
```

Config 命名方式：全部以大寫方式並用底線做區隔 (Screaming Case)：

```js
const SCRIPT_TYPE_MAP = {
    ES6: '',
    Vue: '',
    VueSFC: 'type="module"',
    React: 'type="text/babel"',
    Angular: '',
    Solidts: 'type="module"',
    Rxts: 'type="module"',
} as const;

const MAX_EXECUTION_TIME = 60;
```

函式命名
以動詞 + 名詞的命名方式：

```js
const validateForm = () => {
    // do something
};

const fetchUser = () => {
    // do something
};

const convertSecondsToHMS = () => {
    // do something
};
```

事件命名使用 handle 前綴

```ts
const handleAccountFormChange = (event: Event) => {
    // do something
};
```

函式參數命名
參數最多 3 個，超過可以使用物件的方式傳入：

```ts
const validateForm = (name: string, rules: Array<string>) => {
    // do something
};

interface FetchUserData {
    id: number;
    account: string;
    phone: string;
}

const fetchUser = (fetchData: FetchUserData, url: string) => {
    // do something
};
```

其他注意事項

- 變數命名由多個形容詞、名詞組成，避免使用單一動詞當作變數名稱，如 create = 1
- 變數和參數長度最多不要超過 50 字元
- 單頁最多 150 ～ 200 行之間，超過要拆出部分邏輯
- import 套件統一擺在最上面（套件 > 專案檔案 > type）

## TypeScript Guide

### 枚舉命名方式

以開頭大寫 Enum 後綴的方式命名，常數以 Screaming Case 命名：

```ts
enum LevelEnum {
    LOW,
    MEDIUM,
    HIGH,
}
```

### Type 命名方式

Primitive type 或未知物件裡面屬性皆使用 type 定義型別：

```ts
type CodeModel = 'HTML' | 'CSS' | 'JS';
type ImportMap = Record<'imports', Record<string, string>>;
```

物件以 interface 定義型別：

```ts
interface CodeMap {
    language: string;
    content: string;
    resources: string[];
}
```

## React Style Guide

### 每一個檔案以 `export default` 的形式導出元件

每個元件只通過 `export default` 導出一個元件，其他元件皆定義為內部元件(不導出)。

```tsx
// good
const InternalComponent = () => {
    return <div>Internal</div>;
};

const MyComponent = () => {
    return (
        <div>
            <InternalComponent />
        </div>
    );
};

export default MyComponent;
```

```tsx
// bad
export const MyComponent = () => {
    return (
        <div>
            <InternalComponent />
        </div>
    );
};

export const InternalComponent = () => {
    return <div>Internal</div>;
};
```

### 元件宣告資料夾時，以 index.{js,ts,tsx} 導出所有元件

檔案結構

```bash
/components
  ├── Header.jsx
  ├── Footer.jsx
  ├── Sidebar.jsx
  ├── index.js
```

index.js 文件內容：

```js
export { default as Header } from './Header';
export { default as Footer } from './Footer';
export { default as Sidebar } from './Sidebar';
```

內部引用元件：

```js
// components/Layout.jsx
import Header from './Header';
```

外部引用元件：

```js
// pages/Home.jsx
import { Header, Footer } from '../components';
```

### 使用事件處理

以 `handle` 開頭命名後面加上事件名稱，當 `props` 往下使用時以 `on` 連接, `onClick={handleClick}`, `onMouseEnter={handleMouseEnter}` 等

```js
const Button = () => {
    function handleClick() {
        alert('You clicked me!');
    }

    return <button onClick={handleClick}>Click me</button>;
};
```

在 JSX 內嵌事件處理，直接透過箭頭函式使用

```js
<button onClick={() => {
  alert('You clicked me!');
}}>
```
