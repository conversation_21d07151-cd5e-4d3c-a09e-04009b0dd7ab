## 在 VSCode 中顯示 Tailwind CSS 的 `className` 快捷提示

### 1. 安裝 Tailwind CSS IntelliSense 插件

- 在 VSCode 擴充市場中搜尋並安裝 [Tailwind CSS IntelliSense](https://marketplace.visualstudio.com/items?itemName=bradlc.vscode-tailwindcss) 插件。
- 這個插件可以為 Tailwind CSS 提供自動完成提示、類別名稱建議和樣式提示。
- 安裝後，當你輸入 `className` 時，會自動顯示可用的 Tailwind 類別建議。

![Tailwind CSS IntelliSense](https://raw.githubusercontent.com/tailwindlabs/tailwindcss-intellisense/main/packages/vscode-tailwindcss/.github/banner.png)

### 2. 設定 VSCode 使用 Tailwind 配置

```json
{
    "editor.quickSuggestions": {
        "strings": true
    }
}
```
