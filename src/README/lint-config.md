以下指令安裝了一些常用的 ESLint 與 Prettier 配置及插件，這些工具可以幫助維護程式碼風格和一致性。

列出所有使用到的工具，可以在這些連結中找到文件

- [eslint.org](https://eslint.org/docs/latest/use/getting-started)
- [prettier](https://prettier.io/docs/en/install)
- [commitlint.js](https://commitlint.js.org/guides/getting-started.html)
- [husky](https://typicode.github.io/husky/)

## 需要做的事

### 使用

1. 按下 (CMD/CTRL + Shift + P) > Preferences: Open Default Settings 開啟 user settings 設定
2. 在 user settings 中加入以下設定開啟修復功能

```json
{
    "editor.formatOnSave": true, // 儲存時自動格式化
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "always" // 保存時自動修復所有 ESLint 可以解決的問題，包括語法錯誤和風格問題。
    }
}
```

3. 當 import 順序不符合預期時，按下儲存應自動修正

## ESLint 相關配置

> 注意：.eslintrc.json 配置在 apps/\* 專案內，隔絕各專案的配置

```bash
pnpm add -D eslint-config-airbnb eslint-plugin-simple-import-sort eslint-plugin-react-prefer-function-component
```

```bash
pnpm add -D prettier prettier-plugin-tailwindcss
```

- **[eslint-config-airbnb](https://www.npmjs.com/package/eslint-config-airbnb)**

提供 Airbnb 的 ESLint 配置作為可擴展的共享配置。

- **[eslint-config-prettier](https://github.com/prettier/eslint-config-prettier#installation)**

    關閉所有不必要或可能與 Prettier 衝突的 ESLint 規則，確保兩者可以順利合作。

- **[eslint-plugin-simple-import-sort](https://www.npmjs.com/package/eslint-plugin-simple-import-sort)**

    對 JavaScript 的 import 語句進行排序，從而使程式碼結構更清晰及增強一致性。

- **[eslint-plugin-react-prefer-function-component](https://www.npmjs.com/package/eslint-plugin-react-prefer-function-component)**

    對保持一致的 React 開發風格，進行檢查及修正

在 `.eslintrc.json` 文件中，配置 Prettier 來確保程式碼風格的一致性：

```json
{
    "plugins": ["simple-import-sort"],
    "extends": ["next", "next/core-web-vitals", "airbnb", "prettier"],
    "overrides": [
        // * 默認的 parser of ESLint，JS檔案 不使用 next/core-web-vitals，而是使用 espree 來避免錯誤
        // * https://github.com/vercel/next.js/issues/40687#issuecomment-1378845989
        {
            "files": ["*.js"],
            "parser": "espree",
            "parserOptions": {
                "ecmaVersion": 2020
            }
        }
    ],
    "rules": {
        "simple-import-sort/imports": "warn", // 對 import 語句進行排序
        "simple-import-sort/exports": "warn", // 對 export 語句進行排序
        "import/first": "error", // 確保所有 import 語句出現在文件的最頂部
        "import/newline-after-import": "error", // 在 import 語句之後強制添加一行空行
        "import/no-duplicates": "error", // 禁止同一模塊的多次 import
        "import/extensions": "off", // 關閉對文件擴展名的檢查
        "import/no-unresolved": "off", // 關閉對未解析模塊的檢查
        "no-unused-vars": "off", // 關閉對未使用變量的警告
        "no-underscore-dangle": "off", // 關閉對變量名稱中使用下劃線的限制
        "import/prefer-default-export": "off", // 關閉強制使用默認導出的規則，允許使用命名導出
        "react/jsx-filename-extension": [1, { "extensions": [".js", ".jsx", ".ts", ".tsx"] }], // 允許在 .js, .jsx, .ts, .tsx 文件中使用 JSX 語法
        "react/jsx-props-no-spreading": "off", // 關閉禁止在 JSX 中使用 props 展開語法的規則
        "camelcase": "off", // 關閉對變量使用駝峰命名法的強制要求
        "react/require-default-props": "off", // 關閉強制為所有非必要 props 定義默認值的規則
        "no-param-reassign": "off", // 關閉對函數參數進行重新賦值的限制
        "global-require": "off", // 關閉在非頂層作用域中使用 require 語句的限制
        "react/react-in-jsx-scope": "off", // 關閉在 JSX 中強制要求引入 React 的規則
        "no-use-before-define": "off", // 關閉在變量定義前使用該變量的限制
        "default-param-last": "off" // 關閉在函數參數列表中要求默認參數位於最後的位置的規則
    }
}
```

## Prettier 相關配置

- **[prettier-plugin-tailwindcss](https://www.npmjs.com/package/prettier-plugin-tailwindcss)**

    專門針對 Tailwind CSS classes 的排序，確保在多行 CSS classes中，classes 的順序按照官方推薦的順序排列，從而增強程式碼的可讀性和一致性。

在 `.prettierrc` 文件中，配置 Prettier 來確保程式碼風格的一致性：

> 注意：.`prettierrc` 配置在根目錄層級，全部專案皆共享配置

```json
{
    "plugins": ["prettier-plugin-tailwindcss"],
    "singleQuote": true,
    "semi": true,
    "tabWidth": 4,
    "printWidth": 120
}
```

#### 配置說明

- **plugins**:
    - `prettier-plugin-tailwindcss`: 針對 Tailwind CSS classes 進行排序。
- **singleQuote**: 使用單引號代替雙引號。
- **semi**: 在語句末尾強制使用分號。
- **tabWidth**: 設置縮進為 4 個空格。
- **printWidth**: 設置每行最大寬度為 120 字符。

## Husky 配置

Husky 是一個 Git hooks 工具，可以在 Git 提交或其他 Git 事件發生時執行腳本。我們可以利用它來自動化程式碼檢查或格式化等任務，確保在程式碼提交之前符合團隊的標準。

`.husky` 目錄包含兩個主要的 Git hooks 腳本：

- **commit-msg**: 用於配置 commit 信息檢查，使用 commitlint 對所有提交 commit 進行規範性檢查，確保提交的 commit 符合團隊規範。

> commitlint 遵循 [@commitlint/config-conventional](https://www.npmjs.com/package/@commitlint/config-conventional) 但 'subject-case' 允許 'sentence-case'

```bash
echo "feat: add message" # passes

//'subject-case': [0, 'always', ['sentence-case']
echo "feat: Add message" # passes too
```

````javascript
```javascript
// .husky/commit-msg
pnpm dlx commitlint --edit $1

// commitlint.config.js
// 使用共用配置
module.exports = {
    extends: ['@commitlint/config-conventional'],
    rules: {
      'subject-case': [0, 'always', ['sentence-case']
};
````

````

- **pre-commit**: 在提交之前運行 lint-staged，這樣可以在程式碼進入版本控制系統之前檢查和修復程式碼格式。

```javascript
// .husky/pre-commit
npx lint-staged
````

### lint-staged

因為如果專案是使用 `monorepo` ，在 `monorepo` 根層級安裝 `_lint-staged_` ，並在每個app中新增單獨的設定檔。執行時，`_lint-staged_` 將始終使用最接近暫存檔案的配置，因此擁有單獨的設定檔可確保 linter 不會「洩漏」到其他套件中。[如何在多包 Monorepo 中使用 lint-staged？](https://github.com/lint-staged/lint-staged?tab=readme-ov-file#how-to-use-lint-staged-in-a-multi-package-monorepo)

在 `apps/main-nextjs-app/.lintstagedrc.json` 中，配置了 lint-staged 來在提交時自動格式化程式碼並檢查和修復程式碼：

```javascript
{ "*.tsx": ["prettier --write", "eslint --fix --max-warnings=0"] }
```

> 需注意的是 eslint 除了*檢查 **錯誤** ，當檢查到 **警告** 時也會中斷並彈出訊息，等待開發人員檢查後再次提交(以前端而言，大多情況是防止console進入專案、或是會要求再次檢查 useEffect 的 dependcy 邏輯)*

如下圖範例：

會跳出兩項錯誤

- useEffect 的 dependcy 有其他依賴項，會要求開發人員再次進行檢查，解決此警告
- iframe 缺少參數因此引發 error

![image](https://res.craft.do/user/full/5da315db-7cae-ec09-2db3-850c835db3c0/doc/97D4EF62-A699-422F-81F5-26D1CF464269/A60A01C8-A28D-4CB1-95DB-952A7D4E705E_2/tqvKn9aQxVL2nuReUY4tg1kr5yj4Hi7Q41wkqx3wPV8z/Xnapper-2024-09-03-11.47.58.png)

## 範例

- 當提交含有錯誤的程式碼時，擋住提交

![image](https://res.craft.do/user/full/5da315db-7cae-ec09-2db3-850c835db3c0/doc/97D4EF62-A699-422F-81F5-26D1CF464269/44847E2C-524C-4E80-B844-08369B0612CA_2/s2fybElHxaCGnV3W2BB4CtTpzfoIgyNFgwQHAwhJ7qYz/Image.png)

- 當提交訊息不符合規定時，擋住提交

![image](https://res.craft.do/user/full/5da315db-7cae-ec09-2db3-850c835db3c0/doc/97D4EF62-A699-422F-81F5-26D1CF464269/BE59FB67-CB0B-436D-8BC8-21377AF94FC4_2/pOpnJGIBYhyB3r6ZKatVbRzxkW1rl2zj9uy5FEgmzTcz/Image.png)

- 當提交訊息不符合規定時，擋住提交

![image](https://res.craft.do/user/full/5da315db-7cae-ec09-2db3-850c835db3c0/doc/97D4EF62-A699-422F-81F5-26D1CF464269/3DE2B460-3F94-42B4-904D-BD0790F08FFF_2/ouHUBqb1nTdz1Vu7KxvuUQ3VhTyPyx2flqZJTUw09a8z/Image.png)
