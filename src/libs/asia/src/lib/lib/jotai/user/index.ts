import { atom } from 'jotai';

import { PersonalPictureData, UserInfoData, UserPermissionList } from '../../../types/interface/user/index';

export const userAtom = atom<{
    loading: boolean;
    isLogin: boolean;
    userInfo: UserInfoData | null;
    personalPicture: PersonalPictureData | null;
    permissionList: UserPermissionList | null;
}>({
    loading: false,
    isLogin: false,
    userInfo: null,
    personalPicture: null,
    permissionList: null,
});
