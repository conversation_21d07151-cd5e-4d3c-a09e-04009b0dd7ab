import { Button, CompoundDropdownMenu, CompoundDropdownMenuItem } from '@mayo/mayo-ui-beta/v2';
import axios from 'axios';
import Cookies from 'js-cookie';
import { Globe } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useState, useTransition } from 'react';

import { COOKIE_NAMES, defaultLocale, Locale } from '../../../../../utils/config';
import { deleteCookie } from '../../../../../utils/cookies';
import { setUserLocale } from '../../../../../utils/locale';

const LanguageDropdown = () => {
    const t = useTranslations();
    // 從 cookie 中讀取當前語系
    const [locale, setLocale] = useState<Locale>((Cookies.get(COOKIE_NAMES.LOCALE) as Locale) || defaultLocale);

    const [, startTransition] = useTransition();

    const handleLanguageChange = async (newLocale: string) => {
        startTransition(() => {
            // 清除 form auth cookie
            deleteCookie(COOKIE_NAMES.AUTH, '.mayohr.com');
            deleteCookie(COOKIE_NAMES.REFRESH_TOKEN, '.mayohr.com');

            setLocale(newLocale as Locale);
            setUserLocale(newLocale as Locale);
            axios.defaults.headers.common['Accept-Language'] = newLocale;
        });
    };

    const menuItems: CompoundDropdownMenuItem[] = [
        { id: 'label-language', type: 'label', label: t('SH_LanguageFamily') },
        { id: 'sep-1', type: 'separator' },
        {
            id: 'radio-language',
            type: 'radioGroup',
            label: 'languageSelection',
            value: locale,
            onValueChange: handleLanguageChange,
            items: [
                { id: 'radio-en', label: 'English', value: 'en-us' },
                { id: 'radio-zh-TW', label: '繁體中文', value: 'zh-tw' },
                { id: 'radio-zh-CN', label: '简体中文', value: 'zh-cn' },
            ],
        },
    ];

    return (
        <CompoundDropdownMenu
            menuItems={menuItems}
            triggerLabel="open"
            dropdownMenuContentProps={{
                className: 'border-sidebar-border',
            }}
            placement="bottom-end"
            triggerProps={{ variant: 'outline', className: 'w-52' }}
            trigger={
                <Button size="icon" variant="default" className="bg-foreground hover:bg-accent">
                    <Globe height={18} width={18} />
                </Button>
            }
        />
    );
};

export default LanguageDropdown;
