import { SidebarInset, SidebarProvider } from '@mayo/mayo-ui-beta/v2';
import { usePathname } from 'next/navigation';

import { WHITE_LIST } from '../../../../utils/config';
import { AppSidebar } from './app-sidebar';
import { SiteHeader } from './site-header';

function Sidebar({ children }: { children: React.ReactNode }) {
    const pathname = usePathname();
    // 只要路徑包含白名單的話，就不顯示側邊欄
    const hideSidebar = [...WHITE_LIST, '/mayo-form', '/custom-report'].some((path) => pathname.includes(path));

    return (
        <SidebarProvider className="">
            <SiteHeader />
            <div className="flex flex-1">
                {!hideSidebar && <AppSidebar />}
                <SidebarInset>
                    <div className="h-[calc(100vh-56px)] bg-gray-50">
                        {children}

                        <footer className="h-[56px] w-full py-4">
                            <p className="text-end text-sm text-gray-500">
                                Copyright © 2024 MAYO Human Capital Inc. All rights reserved.
                            </p>
                        </footer>
                    </div>
                </SidebarInset>
            </div>
        </SidebarProvider>
    );
}

export default Sidebar;
