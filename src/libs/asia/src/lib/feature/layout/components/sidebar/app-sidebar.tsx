import { Sidebar, SidebarContent } from '@mayo/mayo-ui-beta/v2';
import prodPath from 'asia/utils/prodPath';
import { Building2 } from 'lucide-react';
// import { Building2, UserCircle, UserCog, Users } from 'lucide-react';
import * as React from 'react';

import { isDev } from '../../../../utils/config';
import ProcessMain from './process-main';

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
    const data = React.useMemo(
        () => ({
            devPage: isDev
                ? [
                      {
                          title: '測試頁面',
                          url: '',
                          icon: Building2,
                          isActive: true,
                          items: [
                              {
                                  title: '表單測試',
                                  url: `${prodPath}/demo/form`,
                              },
                              {
                                  title: '狀態管理測試',
                                  url: '/demo',
                              },
                          ],
                      },
                  ]
                : [],
            // processMain: [
            //     {
            //         title: '人事異動',
            //         url: '#',
            //         icon: Users,
            //         isActive: true,
            //         items: [
            //             {
            //                 title: '新進人員',
            //                 url: `${prodPath}/foundation/hire`,
            //             },

            //             // {
            //             //     title: '留職停薪',
            //             //     url: '#',
            //             // },
            //             // {
            //             //     title: '離職流程',
            //             //     url: '#',
            //             // },
            //         ],
            //     },
            //     // {
            //     //     title: '組織調整',
            //     //     url: '#',
            //     //     icon: Building2,
            //     //     items: [],
            //     // },
            //     // {
            //     //     title: '知識管理',
            //     //     url: '#',
            //     //     icon: GraduationCap,
            //     //     items: [],
            //     // },
            // ],
            // sections: [
            //     {
            //         title: '個人專區',
            //         items: [
            //             {
            //                 title: '個人資訊',
            //                 url: '#',
            //                 icon: UserCircle,
            //                 items: [],
            //             },
            //         ],
            //     },
            //     {
            //         title: '管理專區',
            //         items: [
            //             {
            //                 title: '組織管理',
            //                 url: '#',
            //                 icon: Building2,
            //                 items: [
            //                     {
            //                         title: '新進人員',
            //                         url: '#',
            //                     },
            //                 ],
            //             },
            //             {
            //                 title: '人事管理',
            //                 url: '#',
            //                 icon: UserCog,
            //                 items: [
            //                     {
            //                         title: 'Hire',
            //                         url: '#',
            //                     },
            //                     {
            //                         title: 'People+',
            //                         url: '#',
            //                     },
            //                     {
            //                         title: '到期查詢',
            //                         url: '#',
            //                     },
            //                 ],
            //             },
            //         ],
            //     },
            // ],
        }),
        [isDev],
    );

    return (
        <Sidebar collapsible="icon" className="top-[--header-height] !h-[calc(100svh-var(--header-height))]" {...props}>
            <SidebarContent>
                <div className="space-y-6">
                    <div>
                        <ProcessMain groupTitle="展示頁" items={data.devPage} />
                        {/* <ProcessMain groupTitle="流程專區" items={data.processMain} /> */}
                    </div>

                    {/* {data.sections.map((section, index) => (
                        <div key={index}>
                            <h2 className="px-4 text-lg font-semibold text-gray-200">{section.title}</h2>
                            <NavMain items={section.items} />
                        </div>
                    ))} */}
                </div>
            </SidebarContent>
        </Sidebar>
    );
}
