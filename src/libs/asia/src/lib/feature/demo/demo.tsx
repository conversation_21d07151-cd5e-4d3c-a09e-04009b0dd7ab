/* eslint-disable no-console */

import { Button } from '@mayo/mayo-ui-beta/v2';
import useCountStore from 'asia/lib/zustand/count';
import { useAtom } from 'jotai';
import { useTranslations } from 'next-intl';

import { envsAtom } from '../../lib/jotai/env/index';
import { jotaiStore } from '../../lib/jotai/store';

export function Demo() {
    const t = useTranslations();

    // Jotai state
    const [envs, setEnvs] = useAtom(envsAtom, { store: jotaiStore });

    // SWR data fetching

    // Zustand state
    const count = useCountStore((state: any) => state.count);
    const total = useCountStore((state: any) => state.total);
    const increment = useCountStore((state: any) => state.increment);
    const decrement = useCountStore((state: any) => state.decrement);
    const setCount = useCountStore((state: any) => state.setCount);
    const setTotal = useCountStore((state: any) => state.setTotal);

    return (
        <div className="space-y-8 p-4">
            {/* Welcome Section */}
            <section>
                <h1 className="mb-4 text-2xl font-bold">{t('TEST_WELCOME_MESSAGE')}</h1>
                <Button>{t('AH_ForgetPassword')}</Button>
            </section>

            {/* Jotai Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">Jotai Demo</h1>
                <pre>
                    <p>Jotai Store - envsAtom:</p>
                    <code>{JSON.stringify(envs, null, 4)}</code>
                </pre>
                <button
                    type="button"
                    className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                    onClick={() => setEnvs({ PLATFORM: 'zh-TW' })}
                >
                    Set Envs
                </button>
            </section>

            {/* Zustand Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">Zustand Demo</h1>
                <p>Count: {count}</p>
                <p>Total: {total}</p>
                <div className="space-x-2">
                    <button
                        type="button"
                        className="rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
                        onClick={() => increment()}
                    >
                        Increment
                    </button>
                    <button
                        type="button"
                        className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
                        onClick={() => decrement()}
                    >
                        Decrement
                    </button>
                    <button
                        type="button"
                        className="rounded bg-yellow-500 px-4 py-2 text-white hover:bg-yellow-600"
                        onClick={() => setCount(count + 1)}
                    >
                        Set Count
                    </button>
                    <button
                        type="button"
                        className="rounded bg-purple-500 px-4 py-2 text-white hover:bg-purple-600"
                        onClick={() => setTotal(total + 1)}
                    >
                        Set Total
                    </button>
                </div>
            </section>
        </div>
    );
}

export default Demo;
