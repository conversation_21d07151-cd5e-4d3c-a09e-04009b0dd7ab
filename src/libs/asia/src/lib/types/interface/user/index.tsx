// 使用者資訊
export interface UserInfoData {
    isVerify: boolean;
    userModule: string[];
    userName: string;
    userRole: string[];
    IsSupervisor: boolean;
    IsSecretary: boolean;
    EmployeeNumber: string;
    GroupId: string;
    Gender: string;
    Language: string;
    EmployeeId: string;
    EmployeeName: string;
    NickName: string;
    EnglishName: string;
    IDType: number;
    IDTypeNameByUserLanguage: string;
    CompanyCode: string;
    CompanyId: string;
    CompanyName: string;
    CompanyTimeZone: string;
    DateFormat: number;
}

// 使用者個人圖片
export type PersonalPictureData = string;

// 使用者權限
type UserPermission = {
    moduleCode: string;
    functionCode: string;
    actionCode: string;
    parentFunctionCode: string;
};
export type UserPermissionList = UserPermission[];
