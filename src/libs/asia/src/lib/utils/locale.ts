'use server';

import { cookies } from 'next/headers';

import { COOKIE_NAMES, defaultLocale, Locale, locales } from './config';

// In this example the locale is read from a cookie. You could alternatively
// also read it from a database, backend service, or any other source.

export async function getUserLocale() {
    // * 從 cookie 讀取 locale（一開始從 middleware 中設置）
    const locale = cookies().get(COOKIE_NAMES.LOCALE)?.value || defaultLocale;
    const isSupported = locales.includes(locale as Locale);
    return isSupported ? locale : defaultLocale;
}

export async function setUserLocale(locale: Locale) {
    cookies().set(COOKIE_NAMES.LOCALE, locale);
}
