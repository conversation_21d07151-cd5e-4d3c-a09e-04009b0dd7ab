import prodPath from './prodPath';

const isDev = process.env.NODE_ENV === 'development';

type Locale = (typeof locales)[number];
const locales = ['zh-tw', 'en-us', 'zh-cn'] as const;
const defaultLocale: Locale = 'zh-tw';

const COOKIE_NAMES = {
    LOCALE: 'locale',
    AUTH: 'auth',
    // I18next: 'i18next',
    REFRESH_TOKEN: 'refreshToken',
    BACKEND_SERVER: 'backendServer',
    BPM_BACKEND_SERVER: 'bpmBackendServer',
    TOKEN_COOKIE: '__ModuleSessionCookie',
};

const WHITE_LIST = ['/login', `${prodPath}/error/403`, `${prodPath}/error/404`, `${prodPath}/error/500`];
const TEST_LIST = ['demo', 'demo/form'];

export type { Locale };
export { locales, defaultLocale, COOKIE_NAMES, WHITE_LIST, TEST_LIST, isDev };
