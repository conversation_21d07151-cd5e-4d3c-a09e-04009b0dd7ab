import ClientService from 'asia/utils/service/clientService';
import useSWR, { SWRConfiguration } from 'swr';

import { UserInfoData } from '../../../types/interface/user/index';

const useGetUserInfo = (isLogin = true, config?: SWRConfiguration) => {
    const { data, isLoading, error } = useSWR(isLogin ? '/fd/api/userInfo' : null, ClientService.GET<UserInfoData>, {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        ...config,
    });

    return {
        data: data?.Data,
        isLoading,
        isError: error,
    };
};

export { useGetUserInfo };
