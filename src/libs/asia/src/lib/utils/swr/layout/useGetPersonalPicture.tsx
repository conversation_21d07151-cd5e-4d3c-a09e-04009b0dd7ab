import ClientService from 'asia/utils/service/clientService';
import useSWR, { SWRConfiguration } from 'swr';

import { PersonalPictureData } from '../../../types/interface/user/index';

const useGetPersonalPicture = (config?: SWRConfiguration) => {
    const { data, isLoading, error } = useSWR('/fd/api/users/personalPicture', ClientService.GET<PersonalPictureData>, {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        ...config,
    });

    return {
        data: data?.Data,
        isLoading,
        isError: error,
    };
};

export { useGetPersonalPicture };
