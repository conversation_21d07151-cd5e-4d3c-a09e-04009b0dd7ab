import ClientService from 'asia/utils/service/clientService';
import useSWR, { SWRConfiguration } from 'swr';

import { UserPermissionList } from '../../../types/interface/user/index';

const useGetUserPermission = (config?: SWRConfiguration) => {
    const { data, isLoading, error } = useSWR(
        'fd/api/Authorization/GetAuthorized',
        ClientService.GET<UserPermissionList>,
        {
            revalidateOnFocus: false,
            revalidateOnReconnect: false,
            ...config,
        },
    );

    return {
        data: data?.Data,
        isLoading,
        isError: error,
    };
};

export { useGetUserPermission };
