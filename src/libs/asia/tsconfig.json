{
    // "compilerOptions": {
    //     "jsx": "react-jsx",
    //     "allowJs": false,
    //     "esModuleInterop": false,
    //     "allowSyntheticDefaultImports": true,
    //     "strict": true,
    //     "lib": ["dom", "dom.iterable"],
    //     "types": ["vite/client", "vitest", "next", "node"],
    //     "moduleResolution": "bundler",
    //     // "baseUrl": ".",
    //     // "paths": {
    //     //     "@asia/*": ["./src/lib/*"],
    //     //     "@utils/*": ["./src/lib/utils/*"]
    //     // }
    // },
    "compilerOptions": {
        "jsx": "react-jsx",
        "allowJs": false,
        "esModuleInterop": false,
        "allowSyntheticDefaultImports": true,
        "strict": true,
        "types": ["vite/client", "vitest"]
    },
    "files": [],
    "include": [],
    "references": [
        {
            "path": "./tsconfig.lib.json"
        },
        {
            "path": "./tsconfig.spec.json"
        }
    ],
    "extends": "../../tsconfig.base.json"
}
