{"extends": ["plugin:@nx/react", "../../.eslintrc.json"], "ignorePatterns": ["!**/*", "**/vite.config.*.timestamp*", "**/vitest.config.*.timestamp*"], "overrides": [{"files": ["*.ts", "*.tsx", "*.js", "*.jsx"], "rules": {"@nx/enforce-module-boundaries": "off", "react-hooks/exhaustive-deps": "off", "react/react-in-jsx-scope": "off", "react/jsx-filename-extension": "off", "import/prefer-default-export": "off", "import/extensions": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "jsx-a11y/anchor-is-valid": "off", "react/prop-types": "off", "react/no-unstable-nested-components": "off", "jsx-a11y/label-has-associated-control": "off", "react/require-default-props": "off", "react/no-unused-prop-types": "off", "react/function-component-definition": "off", "import/no-unresolved": "off", "@typescript-eslint/no-explicit-any": "off", "consistent-return": "off", "no-unreachable": "off", "no-console": "warn", "react/jsx-props-no-spreading": "off", "camelcase": "off", "no-param-reassign": "off", "global-require": "off", "no-use-before-define": "off", "default-param-last": "off", "no-underscore-dangle": "off", "no-shadow": "off", "import/newline-after-import": "off"}}, {"files": ["*.ts", "*.tsx"], "rules": {}}, {"files": ["*.js", "*.jsx"], "rules": {}}]}