import { useEffect, useState } from 'react';

type VisibilityState = 'visible' | 'hidden';

const usePageVisibility = () => {
    const [visibility, setVisibility] = useState<VisibilityState>(document.visibilityState as VisibilityState);

    useEffect(() => {
        const handleVisibilityChange = () => {
            setVisibility(document.visibilityState as VisibilityState);
        };

        document.addEventListener('visibilitychange', handleVisibilityChange);

        return () => {
            document.removeEventListener('visibilitychange', handleVisibilityChange);
        };
    }, []);

    return visibility;
};

export default usePageVisibility;
