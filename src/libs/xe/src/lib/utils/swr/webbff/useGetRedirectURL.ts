import Cookies from 'js-cookie';
import { useRouter } from 'nextjs-toploader/app';
import useSWR from 'swr';
import usePageVisibility from 'xe/utils/hooks/usePageVisibility';
import prodPath from 'xe/utils/prodPath';

import { TOKEN_COOKIE } from '../../const';
import { get } from '../../service/clientService';

interface RedirectUrlData {
    data: string;
    code: number;
    message: string;
}

export function useGetRedirectURL() {
    const router = useRouter();
    const visibility = usePageVisibility();
    // 說明: visibility === 'visible' 時才去 fetch 及 revalidateOnFocus
    const { data, error, isLoading, mutate } = useSWR<RedirectUrlData>(
        visibility === 'visible' ? 'webbff/api/CustomizedReport/RedirectUrl' : null,
        get,
        {
            suspense: true,
            refreshInterval: 1000 * 60 * 15,
            shouldRetryOnError: false,
            onError: (err) => {
                if (/403|401/i.test(err?.response?.status)) {
                    Cookies.remove(TOKEN_COOKIE, { path: '/', domain: '.mayohr.com' });
                    router.push(`${window.location.origin}${prodPath}/error/403`);
                }
            },
        },
    );

    return { data, error, isLoading, mutate };
}
