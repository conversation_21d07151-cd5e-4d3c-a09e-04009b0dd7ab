import useSWR, { SWRConfiguration } from 'swr';
import ClientService from 'xe/utils/service/clientService';

export interface User {
    Mata: {
        HttpStatusCode: string;
    };
    Data: {
        isVerify: boolean;
        userModule: string[];
        userName: string;
        userRole: string[];
        IsSupervisor: boolean;
        IsSecretary: boolean;
        PersonalPicture: string;
        EmployeeId: string;
        CompanyId: string;
    };
}

const useGetFdUserInfo = (config?: SWRConfiguration) => {
    const { data, isLoading, error } = useSWR<User>('/fd/api/userInfo', ClientService.GET, {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        ...config,
    });

    return {
        userInfo: data,
        isLoading,
        isError: error,
    };
};

export { useGetFdUserInfo };
