/* eslint-disable no-useless-catch */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';

import { COOKIE_NAMES, defaultLocale } from '../../utils/config';

export const axiosInstance: AxiosInstance = axios.create({
    withCredentials: true,
    responseType: 'json',
    headers: {
        Authorization: Cookies.get(COOKIE_NAMES.TOKEN_COOKIE) || '',
        'Accept-Language': Cookies.get(COOKIE_NAMES.LOCALE) || defaultLocale,
        'Content-Type': 'application/json',
    },
});

axiosInstance.defaults.headers.post['Content-Type'] = 'application/json';
axiosInstance.defaults.headers.put['Content-Type'] = 'application/json';
axiosInstance.defaults.headers.delete['Content-Type'] = 'application/json';

// 請求攔截器
axiosInstance.interceptors.request.use((config) => {
    // doc: 在 client side run time 取得 backend server
    if (typeof window !== 'undefined' && !config.baseURL) {
        const clientBaseURL = Cookies.get(COOKIE_NAMES.BACKEND_SERVER) || undefined;
        config.baseURL = clientBaseURL;
    }

    return config;
});

// 回應攔截器
axiosInstance.interceptors.response.use(
    (response) => response,
    (error: unknown) => Promise.reject(error),
);

export const get = async <T>(url: string): Promise<T> => {
    const response: AxiosResponse<T> = await axiosInstance.get<T>(url);

    return response?.data;
};

const ClientService = {
    GET: async <T>(url: string, config?: AxiosRequestConfig): Promise<T> => {
        try {
            const response: AxiosResponse<T> = await axiosInstance.get<T>(url, config);
            return response.data;
        } catch (error) {
            throw error;
        }
    },
    POST: async <T, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig): Promise<T> => {
        try {
            const response: AxiosResponse<T> = await axiosInstance.post<T>(url, data, config);
            return response.data;
        } catch (error) {
            throw error;
        }
    },
    PUT: async <T, D = unknown>(url: string, data?: D, config?: AxiosRequestConfig): Promise<T> => {
        try {
            const response: AxiosResponse<T> = await axiosInstance.put<T>(url, data, config);
            return response.data;
        } catch (error) {
            throw error;
        }
    },
};

export default ClientService;
