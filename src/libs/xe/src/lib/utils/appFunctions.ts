/* eslint-disable @typescript-eslint/no-explicit-any */
import { callAppFunction } from './appBridge';

/**
 * 1. 取得 App 使用者資訊
 */
export function getAppUser(): Promise<any> {
    return new Promise((resolve) => {
        callAppFunction('get_app_user', [], (res: any) => {
            console.log('getAppUser', res);
            resolve(res);
        });
    });
}

/**
 * 2. 路由導向
 */
export function routerGoto(url: string, presentType: 'pop' | 'push' = 'push'): Promise<any> {
    return new Promise((resolve) => {
        callAppFunction('router_goto', [url, presentType], (res: any) => {
            console.log('routerGoto', res);
            resolve(res);
        });
    });
}

/**
 * 3. 返回
 */
export function routerBack(backType: 'page' | 'back_to_home' = 'page', count?: number): Promise<any> {
    return new Promise((resolve) => {
        callAppFunction('router_back', [backType, count], (res: any) => {
            console.log('routerBack', res);
            resolve(res);
        });
    });
}

/**
 * 4. 設定 NavBar
 * name: 標題或名稱
 * tools: 工具列配置，可以是一個陣列或物件，依實際需求
 */
// export function setNavbar(name: string, tools?: any): Promise<any> {
//     return new Promise((resolve, reject) => {
//         callAppFunction(
//             'set_navbar',
//             { name, tools },
//             (res: CallAppFunctionResponse) => {
//                 if (res.success) {
//                     resolve(res.data);
//                 } else {
//                     reject(res.error);
//                 }
//             }
//         );
//     });
// }

/**
 * 5. log_console
 */
export function logConsole(message: string, consoleCategory?: string): void {
    console.log('logConsole', message, consoleCategory);
    callAppFunction('log_console', [message, consoleCategory]);
}

/**
 * 6. log_track
 */
export function logTrack(message: string, consoleCategory?: string): void {
    console.log('logTrack', message, consoleCategory);
    callAppFunction('log_track', [message, consoleCategory]);
}

/**
 * 7. alert
 * @param title 標題
 * @param content 內容
 * @param buttonActions 按鈕配置，例如 [{text: '確定', value: 'ok'}, {text: '取消', value: 'cancel'}]
 */

export function alertApp(title: string, content: string, buttonActions: string[] = []): Promise<any> {
    return new Promise((resolve) => {
        callAppFunction('alert_app', [title, content, ...buttonActions], (buttonIndex: number | null) => {
            console.log('buttonIndex', buttonIndex);
            resolve(buttonIndex);
        });
    });
}

/**
 * 8. refresh
 */
export function refresh(): Promise<any> {
    return new Promise((resolve) => {
        callAppFunction('refresh', [], (res: any) => {
            console.log('refresh', res);
            resolve(res);
        });
    });
}

/**
 * 9. 取得 App Token
 */
export function getAppToken(): Promise<string> {
    return new Promise((resolve) => {
        callAppFunction('get_app_token', [], (res: any) => {
            console.log('getAppToken', res);
            resolve(res);
        });
    });
}
