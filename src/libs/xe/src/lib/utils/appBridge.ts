/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-unused-vars */
// 定義全局類型
declare global {
    interface Window {
        flutter_inappwebview?: {
            callHandler: (handlerName: string, ...args: any[]) => Promise<any>;
            registerHandler: (handlerName: string, handler: (...args: any[]) => any) => void;
        };
    }
}

export const callAppFunction = async (fnName: string, args?: any[], callback?: (res: any) => void): Promise<void> => {
    if (typeof window !== 'undefined' && window.flutter_inappwebview?.callHandler) {
        try {
            const result = await window.flutter_inappwebview.callHandler(fnName, ...(args || []));
            callback?.(result);
        } catch (error) {
            callback?.(error);
        }
    } else {
        // Mock 實作 (web 環境下沒有原生 App 時使用)
        console.warn(`[Mock] callHandler: ${fnName}`, args);
        setTimeout(() => {
            callback?.(`Mock response for ${fnName}`);
        }, 500);
    }
};
