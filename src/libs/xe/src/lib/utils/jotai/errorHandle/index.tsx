import { atom } from 'jotai';

export type ErrorModal = {
    errorTitle?: string;
    errorMsg?: string;
    errorDetail?: any;
    confirmText?: string;
    cancelText?: string;
    onConfirm?: () => void;
    onCancel?: () => void;
    onClose?: () => void;
    // TODO: 暫時添加
    confirmAction?: () => void;
};

export const errorModalAtom = atom<ErrorModal>({});
export const openErrorModalAtom = atom<boolean>(false);
export const isReportingAtom = atom<boolean>(false);

export const handleErrorAtom = atom(
    (get) => get(errorModalAtom),
    (_, set, update?: ErrorModal) => {
        if (update) {
            set(errorModalAtom, update);
            set(openErrorModalAtom, true);
        }
    },
);

export const handleAxiosErrorAtom = atom(
    (get) => get(errorModalAtom),
    (_, set, update?: ErrorModal) => {
        if (update) {
            set(errorModal<PERSON>tom, update);
            set(openErrorModalAtom, true);
        }
    },
);
