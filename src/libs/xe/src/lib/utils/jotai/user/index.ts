import { atom } from 'jotai';

export enum ModulePathEnum {
    自定義報表 = 'custom-report',
    自定義表單 = 'mayo-form',
}

export interface FunctionDisableList {
    data: {
        functionDisableList: FunctionDisableData[];
    };
}

export interface EnabledFunctions {
    [ModulePathEnum.自定義報表]: boolean;
    [ModulePathEnum.自定義表單]: boolean;
}

export enum FunctionCodeEnum {
    PeopleSearch = 'PeopleSearch',
    GoogleCalendar = 'GoogleCalendar',
    MayoForm = 'MAYOForm',
}

export interface FunctionDisableData {
    functionCode: FunctionCodeEnum;
    functionName: string;
    moduleName: string;
    moduleCode: string | null;
    enable: boolean;
    displayOrder: number;
}

export interface UserInfo {
    Meta: {
        HttpStatusCode: number;
    };
    Data: {
        isVerify: boolean;
        userModule: string[];
        userName: string;
        userRole: string[];
        IsSupervisor: boolean;
        IsSecretary: boolean;
        PersonalPicture: string;
        EmployeeId: string;
        CompanyId: string;
        companyName: string;
        companyLogoUrl: string;
        companyLogoUpdatedTime: string;
        Language?: string;
    } | null;
}

export const userAtom = atom<{
    loading: boolean;
    isLogin: boolean;
    userInfo: UserInfo | null;
    functionDisableList: FunctionDisableData[];
    enabledFunctions: EnabledFunctions;
}>({
    loading: false,
    isLogin: false,
    userInfo: null,
    functionDisableList: [],
    enabledFunctions: {
        [ModulePathEnum.自定義報表]: false,
        [ModulePathEnum.自定義表單]: false,
    },
});
