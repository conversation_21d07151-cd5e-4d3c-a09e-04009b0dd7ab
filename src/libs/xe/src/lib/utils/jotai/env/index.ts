'use client';

import { atom } from 'jotai';

export type Envs = {
    PLATFORM?: string;
    SERVER_ENV_DEVELOPMENT_ENV_VARIABLE?: string;
    SERVER_ENV_BACKEND_SERVER?: string;
    SERVER_ENV_WEB_SITE?: string;
    SERVER_ENV_AUTH_WEB_SITE?: string;
    SERVER_ENV_TUBE_BACKEND_SERVER?: string;
    SERVER_ENV_WEBBFF_BACKEND_SERVER?: string;
    SERVER_ENV_BPM_BACKEND_SERVER?: string;
    SERVER_ENV_SENTRY_DSN?: string;
};

const envsAtom = atom<Envs>({});

export { envsAtom };
