export enum HireProcessStatus {
    NotHired = 'NotHired',
    Hired = 'Hired',
    GiveUp = 'GiveUp',
}

export const HIRE_PROCESS_STATUS_OPTIONS = [
    { value: HireProcessStatus.NotHired, label: '未報到' },
    { value: HireProcessStatus.GiveUp, label: '放棄報到' },
    { value: HireProcessStatus.Hired, label: '已報到' },
] as const;

export enum HireProcessKeywordType {
    EmployeeName = 'EmployeeName',
    Department = 'Department',
    Supervisor = 'Supervisor',
    EmployeeGroupName = 'EmployeeGroupName',
    WorkCountryName = 'WorkCountryName',
}

export const HIRE_PROCESS_KEYWORD_TYPE_OPTIONS = [
    { value: HireProcessKeywordType.EmployeeName, label: '姓名' },
    { value: HireProcessKeywordType.Department, label: '單位' },
    { value: HireProcessKeywordType.Supervisor, label: '直屬主管' },
    { value: HireProcessKeywordType.EmployeeGroupName, label: '單位' },
    { value: HireProcessKeywordType.WorkCountryName, label: '地區' },
] as const;
