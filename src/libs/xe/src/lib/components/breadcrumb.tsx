import {
    Bread<PERSON>rumb,
    B<PERSON><PERSON>rumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbPage,
    BreadcrumbSeparator,
} from '@mayo/mayo-ui-beta/v2';
import classNames from 'classnames';
import { ChevronRight } from 'lucide-react';
import { useRouter } from 'nextjs-toploader/app';
import React from 'react';

export interface BreadcrumbItem {
    label: string;
    href?: string;
    isCurrent?: boolean;
}

interface BreadcrumbProps {
    items: BreadcrumbItem[];
}

export function Breadcrumbs({ items }: BreadcrumbProps) {
    const router = useRouter();

    return (
        <Breadcrumb>
            <BreadcrumbList>
                {items.map((item, index) => (
                    <React.Fragment key={item.label}>
                        <BreadcrumbItem>
                            {item.isCurrent ? (
                                <BreadcrumbPage>{item.label}</BreadcrumbPage>
                            ) : (
                                <BreadcrumbLink
                                    className={classNames({
                                        'cursor-pointer': !!item.href,
                                    })}
                                    onClick={() => {
                                        if (item.href) {
                                            router.push(item.href);
                                        }
                                    }}
                                >
                                    {item.label}
                                </BreadcrumbLink>
                            )}
                        </BreadcrumbItem>
                        {index < items.length - 1 && (
                            <BreadcrumbSeparator>
                                <ChevronRight size={16} />
                            </BreadcrumbSeparator>
                        )}
                    </React.Fragment>
                ))}
            </BreadcrumbList>
        </Breadcrumb>
    );
}
