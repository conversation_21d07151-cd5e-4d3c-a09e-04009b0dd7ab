'use client';

import { <PERSON><PERSON>, Card, CardContent, Progress } from '@mayo/mayo-ui-beta/v2';
import cn from 'classnames';
import { Check } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import React from 'react';
import prodPath from 'xe/utils/prodPath';

interface Step {
    label: string;
    displayNumber?: string;
    pagePath: string; // A unique part of the URL path for this step
}

const steps: Step[] = [
    { label: '個人資料', displayNumber: '1', pagePath: '/foundation/hire/' },
    { label: '出缺勤', displayNumber: '2', pagePath: '/ta/manager/staffbasicinfo/' },
    { label: '薪資設定', displayNumber: '3', pagePath: '/payroll/masterdata/salary/' },
    { label: '稅與保險', displayNumber: '4', pagePath: '/payroll/masterdata/insurance/' },
    { label: '檢視', displayNumber: '5', pagePath: '' },
];

interface ProgressBarProps {
    onSave: () => void;
    onboardingId?: string;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ onSave, onboardingId }) => {
    const pathname = usePathname();
    const router = useRouter();
    // Find the current step, defaulting to the first step if no match is found
    const currentStepIndex = Math.max(
        0,
        steps.findIndex((step) => pathname.includes(step.pagePath)),
    );

    const handleRouterPush = (pagePath: string, onboardingId?: string) => {
        if (!pagePath || !onboardingId) {
            return;
        }

        const path = `${prodPath}${pagePath}${onboardingId}`;
        router.push(path);
    };

    return (
        <Card className="scrollbar-none w-full overflow-x-auto">
            <CardContent className="">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:flex lg:flex-row lg:items-center lg:gap-6">
                    {steps.map((step, index) => {
                        const isCompleted = index < currentStepIndex;
                        const isCurrent = index === currentStepIndex;

                        return (
                            <div
                                key={step.label}
                                className="flex h-14 cursor-pointer flex-col justify-between lg:flex-1"
                                onClick={() => handleRouterPush(step.pagePath, onboardingId)}
                            >
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-2">
                                        <span className={cn('h-8 w-3 text-2xl', 'text-primary')}>
                                            {step.displayNumber}
                                        </span>
                                        <span
                                            className={cn('h-[22px] text-lg', {
                                                'text-dark-900': !isCompleted && !isCurrent,
                                                'text-gray-400': isCompleted,
                                            })}
                                        >
                                            {step.label}
                                        </span>
                                    </div>
                                    {isCompleted && (
                                        <div className="flex h-6 w-6 items-center justify-center rounded-full bg-[#10b981]/20">
                                            <Check className="h-4 w-4 text-[#10b981]" />
                                        </div>
                                    )}
                                </div>
                                <Progress
                                    value={isCurrent || isCompleted ? 100 : 0}
                                    className={cn({
                                        'bg-gray-200 [&>div]:bg-[#bee6f9]': isCompleted,
                                        'bg-gray-200 [&>div]:bg-[#26abe3]': isCurrent,
                                        'bg-gray-200': !isCompleted && !isCurrent,
                                    })}
                                />
                            </div>
                        );
                    })}
                    <div className="flex items-center justify-center md:col-span-2 lg:col-span-1 lg:ml-6">
                        <Button className="w-20" color="primary" onClick={onSave} type="button">
                            <div className="w-full text-center">暫存</div>
                        </Button>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default ProgressBar;
