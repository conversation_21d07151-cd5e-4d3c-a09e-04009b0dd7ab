import { Button } from '@mayo/mayo-ui-beta/v2';
import { useAtomValue } from 'jotai';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';

import { envsAtom } from '../../../utils/jotai/env';
import prodPath from '../../../utils/prodPath';

const Error403Page = () => {
    const t = useTranslations();

    const envs = useAtomValue(envsAtom);

    const searchParams = useSearchParams();

    const originalTarget = searchParams?.get('original_target') || '';

    const handleRedirectLoginPage = () => {
        const href = `${envs.SERVER_ENV_AUTH_WEB_SITE}/Account/Login?original_target=${encodeURIComponent(window.location.origin + prodPath + originalTarget)}`;

        window.location.href = href;
    };

    return (
        <div className="flex h-[calc(100vh-109px)] w-screen items-center justify-center">
            <div className="flex h-[360px] gap-[120px]">
                <div className="flex flex-col">
                    <img
                        src={`${prodPath}/images/xe-logo-blue.svg`}
                        width={180}
                        height={45}
                        alt="ApolloXE"
                        className="mb-[40px] mt-[50px]"
                    />
                    <div className="mb-[12px] text-[30px] font-medium leading-[30px]">{t('SH_NotAuthorized')}</div>
                    <div className="border-primary mb-[30px] border-l-[2px] pl-[12px]">{t('SH_PlsLoginAgain')}</div>
                    <div>
                        <Button variant="outline" color="primary" onClick={handleRedirectLoginPage}>
                            {t('SH_Confirm')}
                        </Button>
                    </div>
                </div>
                <img src={`${prodPath}/images/unauthorized.svg`} width={360} height={360} alt="unauthorized" />
            </div>
        </div>
    );
};

export default Error403Page;
