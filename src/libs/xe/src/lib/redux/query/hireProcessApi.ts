import { groupBy } from 'lodash';
import qs from 'qs';

import { rootApi } from './rootApi';
import {
    CreateHireProcessPersonalPayload,
    CreateHireProcessPersonalResponse,
    GetHireListResponse,
    HireProcessOptionsPayload,
    HireProcessOptionsResponse,
    HireProcessPayload,
    HireProcessPersonalResponse,
    RequiredDocumentResponse,
    ValidateIdNumberPayload,
} from './types';

export const hireProcessApi = rootApi.injectEndpoints({
    endpoints: (build) => ({
        // 取得個人資料 /v2/platform-api/api/HireProcess/Personal/${personalId}`
        getHireProcessPersonal: build.query<HireProcessPersonalResponse, string>({
            query: (personalId) => ({
                url: `/v2/platform-api/api/HireProcess/Personal/${personalId}`,
                method: 'GET',
            }),
        }),
        // 新增個人資料 /v2/platform-api/api/HireProcess/Personal
        createHireProcessPersonal: build.mutation<{ onboardingId: string }, CreateHireProcessPersonalPayload>({
            query: (data) => ({
                url: `/v2/platform-api/api/HireProcess/Personal`,
                method: 'POST',
                data,
            }),
            transformResponse: (response: CreateHireProcessPersonalResponse) => response.data,
            invalidatesTags: ['GET_HIRE_PROCESS'],
        }),
        // Hire 列表 /api/HireProcess
        getHireProcess: build.query<GetHireListResponse, HireProcessPayload>({
            query: (data) => {
                const qsString = qs.stringify(data);
                return {
                    url: `/v2/platform-api/api/HireProcess?${qsString}`,
                    method: 'GET',
                };
            },
            providesTags: ['GET_HIRE_PROCESS'],
        }),
        getHireProcessOptions: build.query<HireProcessOptionsResponse, HireProcessOptionsPayload>({
            query: (data) => {
                const qsString = qs.stringify(data);
                return {
                    url: `/v2/platform-api/api/HireProcess/Options?${qsString}`,
                    method: 'GET',
                };
            },
            transformResponse: (response: HireProcessOptionsResponse) => {
                const groupOptions = groupBy(response.data.baseSettingOptions, 'type');

                // 把分組結果轉成 { [type]: options[] } 的物件
                const baseSettingOptionsMap = Object.entries(groupOptions).reduce(
                    (acc, [key, options]) => {
                        acc[key] = options;
                        return acc;
                    },
                    {} as Record<string, typeof response.data.baseSettingOptions>,
                );
                return { ...response, data: { ...response.data, baseSettingOptionsMap } };
            },
        }),
        // 驗證身分證字號 /v2/platform-api/api/HireProcess/Validation/IdNumber
        validateIdNumber: build.mutation<any, ValidateIdNumberPayload>({
            query: (data) => ({
                url: '/v2/platform-api/api/HireProcess/Validation/IdNumber',
                method: 'POST',
                data,
            }),
        }),
        // 確認報到文件 /api/HireProcess/{onboardingId}/RequiredDocument
        getHireProcessRequiredDocument: build.query<RequiredDocumentResponse, string>({
            query: (onboardingId) => ({
                url: `/v2/platform-api/api/HireProcess/${onboardingId}/RequiredDocument`,
                method: 'GET',
            }),
            providesTags: ['GET_HIRE_PROCESS_REQUIRED_DOCUMENT'],
        }),
        // 更新報到文件 put /api/HireProcess/{onboardingId}/RequiredDocument
        updateHireProcessRequiredDocument: build.mutation<any, { onboardingId: string; requiredDocuments: any[] }>({
            query: ({ onboardingId, ...data }) => ({
                url: `/v2/platform-api/api/HireProcess/${onboardingId}/RequiredDocument`,
                method: 'PUT',
                data,
            }),
            invalidatesTags: ['GET_HIRE_PROCESS_REQUIRED_DOCUMENT'],
        }),
        // 放棄報到post /api/HireProcess/{onboardingId}/GiveUp
        giveUpHireProcess: build.mutation<
            any,
            { onboardingId: string; onboardingStatus: number; reasonCode: string; reasons: string }
        >({
            query: ({ onboardingId, ...data }) => ({
                url: `/v2/platform-api/api/HireProcess/${onboardingId}/GiveUp`,
                method: 'POST',
                data,
            }),
            invalidatesTags: ['GET_HIRE_PROCESS'],
        }),
        // 驗證企業 Email HireProcess/Validation/BusinessEmail
        validateBusinessEmail: build.mutation<any, { onboardingId: string; businessEmail: string }>({
            query: ({ onboardingId, businessEmail }) => ({
                url: `/v2/platform-api/api/HireProcess/Validation/BusinessEmail`,
                method: 'POST',
                data: { onboardingId, businessEmail },
            }),
        }),
    }),
});

export const {
    useGetHireProcessPersonalQuery,
    useCreateHireProcessPersonalMutation,
    useLazyGetHireProcessPersonalQuery,
    useGetHireProcessQuery,
    useLazyGetHireProcessQuery,
    useLazyGetHireProcessOptionsQuery,
    useGetHireProcessOptionsQuery,
    useValidateIdNumberMutation,
    useGetHireProcessRequiredDocumentQuery,
    useUpdateHireProcessRequiredDocumentMutation,
    useGiveUpHireProcessMutation,
    useValidateBusinessEmailMutation,
} = hireProcessApi;
