export interface AttendanceRuleOption {
    key: string;
    value: string;
}

export interface BankOption {
    bankId: string;
    bankName: string;
    isRequiredAccountValidation: boolean;
    bankAccountLengthLimit: number;
    isRequiredBranchCodeValidation: boolean;
    branchCodeLengthLimit: number;
}

export interface BaseSettingOption {
    id: string;
    value: string;
    text: string;
    type: string;
    sort: number;
}

export interface MealAllowanceExemption {
    hnCode: string;
    limit: number;
}

export interface PersonalEffectiveDepOption {
    departmentId: string;
    effectiveDate: string;
    departmentCode: string;
    departmentName: string;
    positions: null | { positionNumber: string; supervisorName: string }[];
}

export interface PersonalFieldItem {
    key: string;
    value: string;
    upperFieldCode: string | null;
    upperKey: string | null;
}

export interface PersonalFieldOption {
    fieldCode: string;
    items: PersonalFieldItem[];
}

export interface PersonalField {
    fieldId: string;
    fieldCode: string;
    isDisplay: boolean;
    isRequired: boolean;
    language: string;
    displayText: string;
}

export interface SalaryAccountItem {
    addReductionItem: string;
    hnCode: string | null;
    value: string;
    key: string;
}

export interface SalaryAccountOptions {
    hourly: SalaryAccountItem[];
    month: SalaryAccountItem[];
    other: SalaryAccountItem[];
}

export interface SalaryRangeOption {
    key: string;
    value: string;
}

export interface ShiftOption {
    key: string;
    value: string;
}

export interface HireProcessOptionsResponse {
    data: {
        attendanceRuleOptions: AttendanceRuleOption[];
        bankOptions: BankOption[];
        baseSettingOptions: BaseSettingOption[];
        mealAllowanceExemption: MealAllowanceExemption;
        personalEffectiveDepOptions: PersonalEffectiveDepOption[];
        personalFieldOptions: PersonalFieldOption[];
        personalFields: PersonalField[];
        salaryAccountOptions: SalaryAccountOptions;
        salaryRangeOptions: SalaryRangeOption[];
        shiftOptions: ShiftOption[];
        baseSettingOptionsMap: Record<string, BaseSettingOption[]>;
    };
    meta: {
        httpStatusCode: number;
    };
}

export type HireProcessOptionsPayload = {
    onboardingDate: string;
};

export type ValidateIdNumberPayload = {
    onboardingDate: string;
};

export enum HireProcessStatus {
    NotHired = 'NotHired',
    Hired = 'Hired',
    GiveUp = 'GiveUp',
}

export type HireProcessPayload = {
    Status: HireProcessStatus | null;
    StartDate?: string | null;
    EndDate?: string | null;
    Type?: string | null;
    Keyword?: string | null;
};

enum ConfirmEmployeeDataEnum {
    Hire確認 = 1,
    Hire暫存 = 2,
    Rms暫存 = 3,
    部門沒有異動單 = 4,
    部門已失效 = 5,
    部門未來失效 = 6,
    部門職位未填 = 7,
    部門職位不存在 = 8,
    部門職位未來不存在 = 9,
    放棄報到修改回未報到要再確認一次 = 10,
    未報到 = 11,
}

export interface HireProcessItem {
    onboardingId: string;
    onboardingDate: string;
    onboardingStatus: number;
    reasonCode: string | null;
    reasons: string | null;
    companyId: string;
    personalId: string;
    lastName: string;
    firstName: string;
    englishName: string;
    employeeId: string;
    employeeGroupName: string;
    deptFactoryCountryName: string | null;
    deptFactoryCountyName: string | null;
    workCountryName: string;
    workCountyName: string;
    deptFactoryCode: string;
    deptFactoryName: string;
    deptName: string;
    supervisorEmployeeId: string | null;
    supervisorEmployeeNumber: string | null;
    supervisorCName: string;
    supervisorEName: string;
    isProvided: boolean;
    isReHired: boolean;
    isNoRequiredDocument: boolean;
    isNeedToCheckHireDate: boolean;
    confirmEmployeeData: ConfirmEmployeeDataEnum;
    originallyEmployeeNumber: string | null;
    totalCompanyRecognizedSeniorityDays: number;
    poolId: string | null;
    hopeOnboardingDate: string | null;
    jrFormNumber: string | null;
    jrJobTitle: string | null;
}

export interface GetHireListResponse {
    data: HireProcessItem[];
    meta: {
        httpStatusCode: number;
    };
}

export interface RequiredDocumentResponse {
    data: {
        onboardingId: string;
        requiredDocumentId: string;
        requiredDocumentName: string;
        isProvided: boolean;
    }[];
    meta: {
        httpStatusCode: number;
    };
}

export interface HireProcessPersonalResponse {
    data: {
        onboardingId: string;
        onboardingDate: string;
        onboardingStatus: number;
        reasonCode: string;
        reasons: string;
        personalPictureUrl: string;
        isReHired: boolean;
        totalCompanyRecognizedSeniorityDays: number;
        basicInfo: {
            createOn: string;
            creater: string;
            updateOn: string;
            updater: string;
            personalId: string;
            lastName: string;
            firstName: string;
            chineseName: string;
            englishName: string;
            gender: string;
            genderText: string;
            birthday: string;
            nation: string;
            nationText: string;
            armyStatus: string;
            armyStatusText: string;
            armyType: string;
            armyTypeText: string;
            armyStart: string;
            armyEnd: string;
            exemptReason: string;
            maritalStatus: string;
            maritalStatusText: string;
            entryTime: string;
            idType: number;
            idTypeText: string;
            idNumber: string;
            idExpiryDate: string;
            idType2: string;
            idNumber2: string;
            idExpiryDate2: string;
            idType3: string;
            idNumber3: string;
            idExpiryDate3: string;
            personalPicture: string;
        };
        hireContact: {
            createOn: string;
            creater: string;
            updateOn: string;
            updater: string;
            contactId: string;
            businessEmail: string;
            businessMobileNumber: string;
            contactAddress: string;
            emergency: string;
            emergencyLandlineNumber: string;
            emergencyMobileNumber: string;
            emergencyRelationship: string;
            extensionNumber: string;
            landlineNumber: string;
            mobileNumber: string;
            permanentAddress: string;
            personalEmail: string;
        };
        educationDatas: Array<{
            createOn: string;
            creater: string;
            updateOn: string;
            updater: string;
            educationId: string;
            academicDegree: string;
            academicDegreeText: string;
            isHighestDegree: boolean;
            academyName: string;
            academicDeptName: string;
            educationCategory: string;
            educationCategoryText: string;
            completionStatus: string;
            completionStatusText: string;
            attendanceStart: string;
            attendanceEnd: string;
            academyLocation: string;
            academicDeptCategory: string;
            academicDeptCategoryText: string;
            filePath: string;
            displayFileName: string;
            FileId: string;
        }>;
        certificaties: Array<{
            certificateId: string;
            certificateName: string;
            certificateIssuingUnit: string;
            certificateIssuingDate: string;
            notes: string;
            certificateExpiryDate: string;
            filePath: string;
            displayFileName: string;
            FileId: string;
            createOn: string;
            creater: string;
            updateOn: string;
            updater: string;
        }>;
        workExperience: Array<{
            workingId: string;
            companyName: string;
            industryCategory: string;
            industryCategoryName: string;
            industryName: string;
            jobTitle: string;
            jobCategory: string;
            jobCategoryName: string;
            departmentName: string;
            directReport: string;
            employmentStart: string;
            employmentEnd: string;
            jobDuties: string;
            seperationReason: string;
            createOn: string;
            creater: string;
            updateOn: string;
            updater: string;
        }>;
        employeeInfo: {
            departmentEffectiveOn: string;
            employeeId: string;
            businessTitleId: string;
            corporationId: string;
            workCountryId: string;
            workCountryName: string;
            workCountyId: string;
            workCountyName: string;
            departmentId: string;
            deptFactoryId: string;
            employeeSubGroupId: string;
            jobFamilyId: string;
            jobGradeId: string;
            jobGroupId: string;
            jobLevelId: string;
            jobStreamId: string;
            jobTitleId: string;
            deptPositionPositionNumber: string;
            employeeGroupId: string;
            employeeGroupName: string;
            employeeNumber: string;
            originallyEmployeeNumber: string;
            jobGradeCode: string;
            jobTitleName: string;
            deptName: string;
        };
        seniorityInfo: {
            actionCategoryCode: string;
            careerRecognizedSeniority: number;
            careerSeniority: number;
            reHireRecognizedJobGradeSeniority: number;
            reHireRecognizedSeniority: number;
        };
    };
    meta: {
        httpStatusCode: number;
    };
}

export interface CreateHireProcessPersonalPayload {
    firstName: string;
    lastName: string;
    onboardingDate: string;
    gender: number;
    idType: number;
    idNumber: string;
}

export interface CreateHireProcessPersonalResponse {
    data: {
        onboardingId: string;
    };
    meta: {
        httpStatusCode: number;
    };
}

// updateHireProcessPersonal
export interface HireProcessPersonalBasicInfo {
    createOn: string;
    creater: string;
    updateOn: string;
    updater: string;
    personalId: string;
    lastName: string;
    firstName: string;
    chineseName: string;
    englishName: string;
    gender: string;
    genderText: string;
    birthday: string;
    nation: string;
    nationText: string;
    armyStatus: string;
    armyStatusText: string;
    armyType: string;
    armyTypeText: string;
    armyStart: string;
    armyEnd: string;
    exemptReason: string;
    maritalStatus: string;
    maritalStatusText: string;
    entryTime: string;
    idType: number;
    idTypeText: string;
    idNumber: string;
    idExpiryDate: string;
    idType2: string;
    idNumber2: string;
    idExpiryDate2: string;
    idType3: string;
    idNumber3: string;
    idExpiryDate3: string;
    personalPicture: string;
}

export interface HireProcessPersonalEducation {
    educationId: string;
    academicDegree: string;
    academicDegreeText: string;
    isHighestDegree: boolean;
    academyName: string;
    academicDeptName: string;
    educationCategory: string;
    educationCategoryText: string;
    completionStatus: string;
    completionStatusText: string;
    attendanceStart: string;
    attendanceEnd: string;
    academyLocation: string;
    academicDeptCategory: string;
    academicDeptCategoryText: string;
    filePath: string;
    displayFileName: string;
    FileId: string;
    createOn: string;
    creater: string;
    updateOn: string;
    updater: string;
}

export interface HireProcessPersonalCertificate {
    certificateId: string;
    certificateName: string;
    certificateIssuingUnit: string;
    certificateIssuingDate: string;
    notes: string;
    certificateExpiryDate: string;
    filePath: string;
    displayFileName: string;
    FileId: string;
    createOn: string;
    creater: string;
    updateOn: string;
    updater: string;
}

export interface HireProcessPersonalData {
    onboardingDate: string;
    onboardingStatus: number;
    isUpdateByRMS: boolean;
    poolId: string;
    jrFormNumber: string;
    jrJobTitle: string;
    hopeOnboardingDate: string;
    isNeedToCheckHireDate: boolean;
    isGiveUp: boolean;
    basicInfo: HireProcessPersonalBasicInfo;
    contact: HireProcessPersonalContact;
    educations: HireProcessPersonalEducation[];
    certificates: HireProcessPersonalCertificate[];
    workExperiences: HireProcessPersonalWorkExperience[];
    employeeInfo: HireProcessPersonalEmployeeInfo;
    seniorityInfo: HireProcessPersonalSeniorityInfo;
    personalPictureUrl: string;
}

export interface HireProcessPersonalWorkExperience {
    workingId: string;
    companyName: string;
    industryCategory: string;
    industryCategoryName: string;
    industryName: string;
    jobTitle: string;
    jobCategory: string;
    jobCategoryName: string;
    departmentName: string;
    directReport: string;
    employmentStart: string;
    employmentEnd: string;
    jobDuties: string;
    seperationReason: string;
    createOn: string;
    creater: string;
    updateOn: string;
    updater: string;
}

export interface HireProcessPersonalContact {
    createOn: string;
    creater: string;
    updateOn: string;
    updater: string;
    contactId: string;
    businessEmail: string;
    businessMobileNumber: string;
    contactAddress: string;
    emergency: string;
    emergencyLandlineNumber: string;
    emergencyMobileNumber: string;
    emergencyRelationship: string;
    extensionNumber: string;
    landlineNumber: string;
    mobileNumber: string;
    permanentAddress: string;
    personalEmail: string;
}

export interface HireProcessPersonalEmployeeInfo {
    departmentEffectiveOn: string;
    employeeId: string;
    businessTitleId: string;
    corporationId: string;
    workCountryId: string;
    workCountryName: string;
    workCountyId: string;
    workCountyName: string;
    departmentId: string;
    deptFactoryId: string;
    employeeSubGroupId: string;
    jobFamilyId: string;
    jobGradeId: string;
    jobGroupId: string;
    jobLevelId: string;
    jobStreamId: string;
    jobTitleId: string;
    deptPositionPositionNumber: string;
    employeeGroupId: string;
    employeeGroupName: string;
    employeeNumber: string;
    originallyEmployeeNumber: string;
    jobGradeCode: string;
    jobTitleName: string;
    deptName: string;
}

export interface HireProcessPersonalSeniorityInfo {
    actionCategoryCode: string;
    careerRecognizedSeniority: number;
    careerSeniority: number;
    reHireRecognizedJobGradeSeniority: number;
    reHireRecognizedSeniority: number;
}

export interface UpdateHireProcessPersonalParams {
    onboardingId: string;
    onboardingDate?: string;
    onboardingStatus?: number;
    isUpdateByRMS?: boolean;
    poolId?: string;
    jrFormNumber?: string;
    jrJobTitle?: string;
    hopeOnboardingDate?: string;
    isNeedToCheckHireDate?: boolean;
    isGiveUp?: boolean;
    basicInfo?: Partial<HireProcessPersonalBasicInfo>;
    contact?: Partial<HireProcessPersonalContact>;
    educations?: Partial<HireProcessPersonalEducation>[];
    certificates?: Partial<HireProcessPersonalCertificate>[];
    workExperiences?: Partial<HireProcessPersonalWorkExperience>[];
    employeeInfo?: Partial<HireProcessPersonalEmployeeInfo>;
    seniorityInfo?: Partial<HireProcessPersonalSeniorityInfo>;
    personalPictureUrl?: string;
    base64?: string;
}

export interface AttendanceData {
    onboardingId: string;
    onboardingDate: string;
    changeRecord: {
        shiftId: string;
        shiftName: string;
        shiftChangeReason: string;
        attendanceRuleId: string;
        attendanceRuleName: string;
        attendanceRuleChangeReason: string;
    };
    individualLeaveTypes: Array<{
        individualLeaveTypeId: string;
        leaveCode: string;
        leaveName: string;
        usedMinutes: number;
        leaveSourceType: string;
        leaveSourceTypeName: string;
        seniority: number;
        availableMinutes: number;
        startDate: string;
        endDate: string;
        restrictedDate: string;
        leaveClass: string;
    }>;
    individualLeaveYear: number;
}

export interface AttendanceGetResponse {
    data: AttendanceData;
}

export type UpdateAttendancePayload = Omit<AttendanceData, 'onboardingId'>;
