import { BaseQueryFn, createApi } from '@reduxjs/toolkit/query/react';
import { AxiosError, AxiosRequestConfig } from 'axios';

import { axiosInstance } from '../../utils/service/clientService';

const axiosBaseQuery =
    (): BaseQueryFn<
        {
            url: string;
            method: AxiosRequestConfig['method'];
            data?: AxiosRequestConfig['data'];
            params?: AxiosRequestConfig['params'];
            signal?: AbortSignal;
        },
        unknown,
        unknown
    > =>
    async (request) => {
        try {
            const response = axiosInstance(request);
            return { data: (await response).data };
        } catch (axiosError) {
            const err = axiosError as AxiosError;
            return {
                error: {
                    status: err.response?.status,
                    data: err.response?.data || err.message,
                },
            };
        }
    };

export const rootApi = createApi({
    reducerPath: 'rootApi',
    baseQuery: axiosBaseQuery(),
    endpoints: () => ({}),
    tagTypes: ['GET_HIRE_PROCESS', 'GET_HIRE_PROCESS_REQUIRED_DOCUMENT'],
});
