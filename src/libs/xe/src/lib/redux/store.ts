import './query/hireProcessApi';

import { combineReducers, configureStore, Reducer } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';

import { rootApi } from './query';
import commonReducer from './slice/commonSlice';
const middlewares = [rootApi.middleware];

const baseReducers = {
    common: commonReducer,
    [rootApi.reducerPath]: rootApi.reducer,
};

const rootReducer = combineReducers(baseReducers);

export const store = configureStore({
    reducer: rootReducer,
    devTools: process.env.NODE_ENV === 'development',
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({}).concat(middlewares),
});

// 說明：
// 1. 使用 setupListeners 設定 API 的 listeners
// 2. 使用 useAppDispatch 和 useAppSelector 來 dispatch 和 selector
setupListeners(store.dispatch);

// Extend the store type to include asyncReducers
interface ExtendedStore extends ReturnType<typeof configureStore> {
    asyncReducers?: Record<string, Reducer>;
}

const extendedStore = store as ExtendedStore;
extendedStore.asyncReducers = {};

// inject reducers
export function injectReducers(newReducers: Record<string, Reducer>) {
    const updatedAsyncReducers = { ...extendedStore.asyncReducers, ...newReducers };
    extendedStore.asyncReducers = updatedAsyncReducers;

    const combinedReducers = combineReducers({
        ...baseReducers,
        ...updatedAsyncReducers,
    });

    store.replaceReducer(combinedReducers);
}

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
