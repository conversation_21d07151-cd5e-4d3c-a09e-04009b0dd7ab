import { zodResolver } from '@hookform/resolvers/zod';
import {
    Button,
    Checkbox,
    DatePicker,
    Form,
    FormControl,
    FormField,
    FormItem,
    FormItemContent,
    Input,
    RadioGroupComponent as RadioGroup,
    Select,
    Switch,
    Textarea,
} from '@mayo/mayo-ui-beta/v2';
import React from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

import { SelectOption } from '../../utils/enum/select';

const CustomFormSchema = z.object({
    username: z.string().min(1, 'You have to input correct username'),
    birthday: z.string().min(1, { message: 'You have to choose birthday' }),
    gender: z.string().optional(),
    openProfile: z.boolean().optional(),
    hobby: z.array(z.string()).refine((value) => value.some((item) => item), {
        message: 'You have to select at least one item.',
    }),
    id: z.string().min(1, { message: 'You have to upload the id file' }),
    remark: z.string().optional(),
    company: z.array(
        z.object({
            label: z.string(),
            value: z.string(),
        }),
    ),
});

const DemoForm = () => {
    const form = useForm<z.infer<typeof CustomFormSchema>>({
        resolver: zodResolver(CustomFormSchema),
        defaultValues: {
            username: '',
            birthday: new Date().toDateString(),
            gender: 'male',
            openProfile: true,
            hobby: [],
            id: '',
            company: [
                {
                    label: '54510058 鼎恆數位科技股份有限公司',
                    value: '54510058 鼎恆數位科技股份有限公司',
                },
            ],
        },
    });

    const hobbyItems = [
        {
            value: 'cooking',
            label: 'Cooking',
        },
        {
            value: 'singing',
            label: 'Singing',
        },
        {
            value: 'dancing',
            label: 'Dancing',
        },
        {
            value: 'baseball',
            label: 'Baseball',
        },
        {
            value: 'basketball',
            label: 'Basketball',
        },
        {
            value: 'boxing',
            label: 'Boxing',
        },
        {
            value: 'fishing',
            label: 'Fishing',
        },
        {
            value: 'hiking',
            label: 'Hiking',
        },
        {
            value: 'swimming',
            label: 'Swimming',
        },
        {
            value: 'surfing',
            label: 'Surfing',
        },
        {
            value: 'tennis',
            label: 'Tennis',
        },
        {
            value: 'volleyball',
            label: 'Volleyball',
        },
        {
            value: 'workout',
            label: 'Work Out',
        },
        {
            value: 'blogging',
            label: 'Blogging',
        },
        {
            value: 'photography',
            label: 'Photography',
        },
    ];

    const onSubmit = (data: z.infer<typeof CustomFormSchema>) => {
        console.log('submitData: ', data);
    };

    return (
        <Form {...form}>
            <form
                className="border-border-checkbox grid gap-6 rounded-lg border p-4 sm:p-6"
                onSubmit={form.handleSubmit(onSubmit)}
            >
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <FormItemContent
                        name="username"
                        description="Enter your username to display in the custom form."
                        fieldClassName="w-full"
                        label="Username"
                        control={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="username"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            showMaxLength
                                            maxLength={30}
                                            value={field.value}
                                            onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(event.target.value)
                                            }
                                            className="w-full"
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />

                    <FormItemContent
                        name="username"
                        description="Enter your username to display in the custom form."
                        fieldClassName="w-full"
                        label="Select company"
                        control={form.control}
                        renderField={({ field }) => (
                            <Select
                                options={[
                                    {
                                        label: '54510058 鼎恆數位科技股份有限公司',
                                        value: '54510058 鼎恆數位科技股份有限公司',
                                    },
                                ]}
                                value={field.value ? { label: field.value, value: field.value } : null}
                                onChange={(option: SelectOption | null) => field.onChange(option ? option.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="w-full"
                            />
                        )}
                    />

                    <FormItemContent
                        name="username"
                        description="Enter your username to display in the custom form."
                        fieldClassName="w-full"
                        label="Username"
                        control={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="username"
                                render={({ field }) => (
                                    <FormControl>
                                        <Select
                                            options={[
                                                {
                                                    label: '54510058 鼎恆數位科技股份有限公司',
                                                    value: '54510058 鼎恆數位科技股份有限公司',
                                                },
                                            ]}
                                            value={field.value ? { label: field.value, value: field.value } : null}
                                            onChange={(option: SelectOption | null) =>
                                                field.onChange(option ? option.value : '')
                                            }
                                            placeholder="請選擇"
                                            isClearable={false}
                                            className="w-full"
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />

                    <FormItemContent
                        name="birthday"
                        description="Select your birthday to display in the custom form."
                        fieldClassName="w-full"
                        label="Birthday"
                        control={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="birthday"
                                render={({ field }) => (
                                    <FormControl>
                                        <DatePicker
                                            className="w-full"
                                            value={field.value}
                                            onChange={(date) => field.onChange(date)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                    <FormItemContent
                        name="gender"
                        description="Pick your gender to display in the custom form."
                        fieldClassName="w-full"
                        label="Gender"
                        control={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="gender"
                                render={({ field }) => (
                                    <FormControl>
                                        <RadioGroup
                                            isHorizontal
                                            radioOptions={[
                                                { value: 'male', label: 'Male' },
                                                { value: 'female', label: 'Female' },
                                                { value: 'unknown', label: 'Prefer not to say' },
                                            ]}
                                            onValueChange={(value) => field.onChange(value)}
                                            className="flex-wrap"
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        name="openProfile"
                        fieldClassName="w-full"
                        label="Open Profile"
                        control={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="openProfile"
                                render={({ field }) => (
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={(check) => field.onChange(check)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div className="w-full">
                    <FormItemContent
                        name="hobby"
                        description="Pick your hobby to display in the custom form."
                        fieldClassName="w-full"
                        label="Hobby"
                        control={form.control}
                        itemComponent={
                            <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5">
                                {hobbyItems.map((item) => (
                                    <FormField
                                        key={item.value}
                                        control={form.control}
                                        name="hobby"
                                        render={({ field }) => (
                                            <FormItem
                                                key={item.value}
                                                className="flex items-center space-x-1 space-y-0"
                                            >
                                                <FormControl>
                                                    <Checkbox
                                                        checked={
                                                            field.value.length > 0 && field.value.includes(item.value)
                                                        }
                                                        label={item.label}
                                                        onValueChange={(checked) =>
                                                            checked
                                                                ? field.onChange([...field.value, item.value])
                                                                : field.onChange(
                                                                      field.value?.filter(
                                                                          (value) => value !== item.value,
                                                                      ),
                                                                  )
                                                        }
                                                    />
                                                </FormControl>
                                            </FormItem>
                                        )}
                                    />
                                ))}
                            </div>
                        }
                    />
                </div>
                <div className="w-full">
                    <FormItemContent
                        name="id"
                        description="Select your id file to display in the custom form."
                        fieldClassName="w-full"
                        label="ID"
                        control={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="id"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            accept=".jpg, .jpeg, .png, .gif"
                                            type="file"
                                            filename={field.value}
                                            className="w-full"
                                            onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                                                const { files } = event.target;
                                                if (files && files.length > 0) {
                                                    field.onChange(files[0].name);
                                                } else {
                                                    field.onChange('');
                                                }
                                            }}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div className="w-full">
                    <FormItemContent
                        name="remark"
                        description="Enter the remark if you want to display in the custom form."
                        fieldClassName="w-full"
                        label="Remark"
                        control={form.control}
                        required={false}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="remark"
                                render={({ field }) => (
                                    <FormControl>
                                        <Textarea
                                            className="min-h-[100px] w-full"
                                            value={field.value}
                                            onChange={(event: React.ChangeEvent<HTMLTextAreaElement>) =>
                                                field.onChange(event.target.value)
                                            }
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div className="mt-2 flex justify-end">
                    <Button>Submit</Button>
                </div>
            </form>
        </Form>
    );
};

export default DemoForm;
