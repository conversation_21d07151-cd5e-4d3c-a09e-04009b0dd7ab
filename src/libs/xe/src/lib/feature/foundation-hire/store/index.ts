import { combineReducers } from '@reduxjs/toolkit';
import type { TypedUseSelectorHook } from 'react-redux';
import { useSelector } from 'react-redux';
import { RootState } from 'xe/redux/store';

import reducers, { FoundationHireState,SLICE_NAME } from './foundationHireSlice';

const reducer = combineReducers({
    data: reducers,
});

export const useAppSelector: TypedUseSelectorHook<
    RootState & {
        [SLICE_NAME]: {
            data: FoundationHireState;
        };
    }
> = useSelector;

export * from './foundationHireSlice';
export { useAppDispatch } from 'xe/redux/store';
export default reducer;
