import { createSlice } from '@reduxjs/toolkit';
import { HireProcessItem } from 'xe/redux/query/types';
import { HireProcessStatus } from 'xe/utils/constants/hireProcess';

interface HireProcessFilter {
    dateRange: [null | string, null | string];
    status: HireProcessStatus | null;
    keywordType?: string;
    keyword?: string;
}

export const initialHireProcessFilter: HireProcessFilter = {
    dateRange: [null, null],
    status: HireProcessStatus.NotHired,
    keywordType: '',
    keyword: '',
};

export type FoundationHireState = {
    editOnboardData: HireProcessItem | undefined;
    editConfirmOnBoardData: HireProcessItem | undefined;
    newHireDialogOpen: boolean;
    cancelOnboardDialogOpen: boolean;
    confirmOnboardDialogOpen: boolean;
    hireProcessFilter: HireProcessFilter;
    selectedRowIds: string[];
};

export const SLICE_NAME = 'foundationHire';

const initialState: FoundationHireState = {
    editOnboardData: undefined,
    editConfirmOnBoardData: undefined,
    newHireDialogOpen: false,
    cancelOnboardDialogOpen: false,
    confirmOnboardDialogOpen: false,
    hireProcessFilter: initialHireProcessFilter,
    selectedRowIds: [],
};

const foundationHireSlice = createSlice({
    name: `${SLICE_NAME}/state`,
    initialState,
    reducers: {
        setEditOnboardData: (state, action) => {
            state.editOnboardData = action.payload;
        },
        setEditConfirmOnBoardData: (state, action) => {
            state.editConfirmOnBoardData = action.payload;
        },
        setNewHireDialogOpen: (state, action) => {
            state.newHireDialogOpen = action.payload;
        },
        setCancelOnboardDialogOpen: (state, action) => {
            state.cancelOnboardDialogOpen = action.payload;
        },
        setConfirmOnboardDialogOpen: (state, action) => {
            state.confirmOnboardDialogOpen = action.payload;
        },
        setHireProcessFilter: (state, action) => {
            state.hireProcessFilter = action.payload;
        },
        resetHireProcessFilter: (state) => {
            state.hireProcessFilter = initialHireProcessFilter;
        },
        selectRow: (state, action) => {
            if (!state.selectedRowIds.includes(action.payload)) {
                state.selectedRowIds.push(action.payload);
            }
        },
        deselectRow: (state, action) => {
            state.selectedRowIds = state.selectedRowIds.filter((id) => id !== action.payload);
        },
        selectAllRows: (state, action) => {
            const existingIds = new Set(state.selectedRowIds);
            action.payload.forEach((id: string) => {
                if (!existingIds.has(id)) {
                    state.selectedRowIds.push(id);
                }
            });
        },
        deselectAllRows: (state) => {
            state.selectedRowIds = [];
        },
    },
});

export const {
    setEditOnboardData,
    setEditConfirmOnBoardData,
    setNewHireDialogOpen,
    setCancelOnboardDialogOpen,
    setConfirmOnboardDialogOpen,
    setHireProcessFilter,
    resetHireProcessFilter,
    selectRow,
    deselectRow,
    selectAllRows,
    deselectAllRows,
} = foundationHireSlice.actions;
export default foundationHireSlice.reducer;
