import {
    ActionBarCloseTrigger,
    ActionBarContent,
    ActionBarPositioner,
    ActionBarRoot,
    ActionBarSelectionTrigger,
    ActionBarSeparator,
    Button,
} from '@mayo/mayo-ui-beta/v2';
import { UserCheck, X } from 'lucide-react';
import { useAppDispatch } from 'xe/redux/store';

import { deselectAllRows, useAppSelector } from './store';

const ActionBar = () => {
    const dispatch = useAppDispatch();
    const selectedRowIds = useAppSelector((state) => state.foundationHire.data.selectedRowIds);
    const selectedCount = selectedRowIds.length;

    const handleClearSelection = () => {
        dispatch(deselectAllRows());
    };

    const handleConfirmOnboard = () => {
        // TODO: Implement confirm onboard functionality
        console.log('Confirm onboard for:', {
            selectedRowIds: Array.from(selectedRowIds),
            selectedCount,
        });
    };

    return (
        <ActionBarRoot open={selectedCount > 0}>
            <ActionBarPositioner dark>
                <ActionBarContent>
                    <ActionBarSelectionTrigger dark>
                        <div className="flex gap-1 whitespace-nowrap">
                            已勾選 <span className="font-bold text-blue-500">{selectedCount}</span> 筆資料
                        </div>
                    </ActionBarSelectionTrigger>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="default"
                            size="sm"
                            color="primary"
                            className="text-primary-foreground flex items-center"
                            onClick={handleConfirmOnboard}
                        >
                            <UserCheck className="mr-2 h-4 w-4" />
                            報到
                        </Button>
                        <ActionBarSeparator dark />
                        <ActionBarCloseTrigger dark onClick={handleClearSelection}>
                            <X className="h-4 w-4" />
                        </ActionBarCloseTrigger>
                    </div>
                </ActionBarContent>
            </ActionBarPositioner>
        </ActionBarRoot>
    );
};

export default ActionBar;
