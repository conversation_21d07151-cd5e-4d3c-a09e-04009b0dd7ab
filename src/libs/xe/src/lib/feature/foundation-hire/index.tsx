import { Card, CardContent } from '@mayo/mayo-ui-beta/v2';
import { injectReducers } from 'xe/redux/store';

import { BreadcrumbItem, Breadcrumbs } from '../../components/breadcrumb';
import { useGetHireProcessQuery } from '../../redux/query/hireProcessApi';
import CancelOnboardDialog from './components/cancel-onboard-dialog';
import ConfirmOnboardDialog from './components/confirm-onboard-dialog';
import HireActions from './components/hire-actions';
import NewOnboardDialog from './components/new-onboard-dialog';
import Filter from './filter';
import HireTable from './hire-table';
import reducer, { useAppSelector } from './store';

const breadcrumbItems: BreadcrumbItem[] = [
    { label: '流程專區', href: '' },
    { label: '人事異動', href: '' },
    { label: '新進人員', href: '/foundation/hire', isCurrent: true },
];

injectReducers({ foundationHire: reducer });

function FoundationHire() {
    const hireProcessFilter = useAppSelector((state) => state.foundationHire.data.hireProcessFilter);
    const { editOnboardData } = useAppSelector((state) => state.foundationHire.data);

    const {
        data: { data: hireProcessData } = {},
        isFetching: isGetHireListFetching,
        refetch: refetchHireProcess,
    } = useGetHireProcessQuery({
        Status: hireProcessFilter.status,
        StartDate: hireProcessFilter.dateRange[0],
        EndDate: hireProcessFilter.dateRange[1],
        Type: hireProcessFilter.keywordType,
        Keyword: hireProcessFilter.keyword,
    });

    return (
        <div className="flex flex-col gap-4">
            <Breadcrumbs items={breadcrumbItems} />
            <div className="flex flex-col gap-4">
                <h1 className="text-xl font-bold">新進人員</h1>

                <Card>
                    <CardContent className="flex flex-col gap-4">
                        <div className="flex flex-col gap-4">
                            {/* 頂部操作列 */}
                            <div className="flex items-center justify-between">
                                <Filter
                                    hireProcessData={hireProcessData}
                                    isGetHireListLoading={isGetHireListFetching}
                                    refetchHireProcess={refetchHireProcess}
                                />

                                <HireActions />
                            </div>
                        </div>
                        <HireTable loading={isGetHireListFetching} />
                    </CardContent>
                </Card>
            </div>

            <NewOnboardDialog />

            <CancelOnboardDialog onboardingDate={editOnboardData?.onboardingDate} />

            <ConfirmOnboardDialog />
        </div>
    );
}

export default FoundationHire;
