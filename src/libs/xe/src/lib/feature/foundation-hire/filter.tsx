import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    DateRangePicker,
    Input,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Select,
} from '@mayo/mayo-ui-beta/v2';
import { find, isUndefined } from 'lodash';
import { Settings2, X } from 'lucide-react';
import { Controller,useForm } from 'react-hook-form';
import { HireProcessItem } from 'xe/redux/query/types';

import {
    HIRE_PROCESS_KEYWORD_TYPE_OPTIONS,
    HIRE_PROCESS_STATUS_OPTIONS,
    HireProcessStatus,
} from '../../utils/constants/hireProcess';
import { SelectOption } from '../../utils/enum/select';
import { resetHireProcessFilter, setHireProcessFilter, useAppDispatch, useAppSelector } from './store';

interface FormValues {
    dateRange: [string | null, string | null];
    status: HireProcessStatus | null;
    keywordType?: string;
    keyword: string;
}

const Filter = ({
    isGetHireListLoading,
    refetchHireProcess,
    hireProcessData,
}: {
    isGetHireListLoading: boolean;
    refetchHireProcess: () => void;
    hireProcessData: HireProcessItem[] | undefined;
}) => {
    const dispatch = useAppDispatch();

    const hireProcessFilter = useAppSelector((state) => state.foundationHire.data.hireProcessFilter);

    const {
        control,
        handleSubmit,
        watch,
        formState: { isDirty },
    } = useForm<FormValues>({
        defaultValues: hireProcessFilter,
    });

    const onSubmit = (data: FormValues) => {
        if (isDirty) {
            dispatch(setHireProcessFilter(data));
        } else {
            refetchHireProcess();
        }
    };

    const handleDateRangeChange = (dateRange: [string, string]) => {
        if ((dateRange[0] && dateRange[1]) || (!dateRange[0] && !dateRange[1])) {
            dispatch(setHireProcessFilter({ ...hireProcessFilter, dateRange }));
        }
    };

    const handleClear = () => {
        // setHireList(undefined);
        dispatch(resetHireProcessFilter());
    };

    return (
        <div className="flex items-center gap-2">
            <DateRangePicker value={hireProcessFilter.dateRange} onChange={handleDateRangeChange} />
            <Popover>
                <PopoverTrigger asChild>
                    <Button size="md" variant="ghost" color="secondary" disabled={isGetHireListLoading}>
                        <div className="flex items-center">
                            <Settings2 className="mr-2" height={16} width={16} />
                            <span className="whitespace-nowrap">篩選</span>
                        </div>
                    </Button>
                </PopoverTrigger>

                <PopoverContent className="w-[320px] p-4">
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="space-y-4">
                            <h3 className="text-base font-medium">篩選資料</h3>

                            <div className="space-y-4">
                                <div>
                                    <label className="mb-1.5 block text-sm text-gray-600">狀態</label>
                                    <Controller
                                        name="status"
                                        control={control}
                                        render={({ field }) => (
                                            <Select
                                                {...field}
                                                options={HIRE_PROCESS_STATUS_OPTIONS}
                                                value={find(HIRE_PROCESS_STATUS_OPTIONS, { value: field.value })}
                                                onChange={(option: SelectOption) => field.onChange(option?.value)}
                                                isClearable={false}
                                                placeholder="未報到"
                                                className="w-full"
                                            />
                                        )}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <label className="mb-1.5 block text-sm text-gray-600">關鍵字</label>
                                    <Controller
                                        name="keywordType"
                                        control={control}
                                        render={({ field }) => (
                                            <Select
                                                {...field}
                                                options={HIRE_PROCESS_KEYWORD_TYPE_OPTIONS}
                                                value={
                                                    find(HIRE_PROCESS_KEYWORD_TYPE_OPTIONS, { value: field.value }) ||
                                                    null
                                                }
                                                onChange={(option: SelectOption) => field.onChange(option?.value)}
                                                placeholder="請選擇"
                                                className="w-full"
                                            />
                                        )}
                                    />
                                    <Controller
                                        name="keyword"
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                value={field.value}
                                                placeholder="請先選擇類別，再輸入關鍵字"
                                                className="w-full bg-gray-50"
                                                disabled={!watch('keywordType')}
                                                onChange={field.onChange}
                                            />
                                        )}
                                    />
                                </div>
                            </div>

                            <div className="flex justify-end gap-2 pt-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="min-w-[80px] justify-center"
                                    onClick={handleClear}
                                    isLoading={isGetHireListLoading}
                                    type="button"
                                >
                                    清空
                                </Button>
                                <Button
                                    size="sm"
                                    className="min-w-[80px] justify-center"
                                    isLoading={isGetHireListLoading}
                                    type="submit"
                                >
                                    搜尋
                                </Button>
                            </div>
                        </div>
                    </form>
                </PopoverContent>
            </Popover>

            {!isUndefined(hireProcessData) && !isGetHireListLoading && (
                <div>
                    <Badge color="grey" variant="default">
                        <div className="flex items-center gap-2 whitespace-nowrap">
                            <span>{hireProcessData?.length || 0} 筆結果</span>
                            <X className="cursor-pointer" size={14} onClick={handleClear} />
                        </div>
                    </Badge>
                </div>
            )}
        </div>
    );
};

export default Filter;
