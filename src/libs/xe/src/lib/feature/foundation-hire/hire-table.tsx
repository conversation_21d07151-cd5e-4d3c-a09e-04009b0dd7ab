import { Button, Checkbox, ColumnDef, DataTable, Tooltip } from '@mayo/mayo-ui-beta/v2';
import classNames from 'classnames';
import { toLower } from 'lodash';
import { ArrowUpDown, Edit, FileUser, Info, UserX } from 'lucide-react';
import { useLocale } from 'next-intl';
import { useRouter } from 'nextjs-toploader/app';
import { useEffect, useState } from 'react';
import { HireProcessItem } from 'xe/redux/query/types';
import prodPath from 'xe/utils/prodPath';

import { useGetHireProcessQuery } from '../../redux/query/hireProcessApi';
import { ConfirmEmployeeDataEnum } from '../../utils/enum';
import ActionBar from './action-bar';
import {
    deselectAllRows,
    deselectRow,
    selectAllRows,
    selectRow,
    setCancelOnboardDialogOpen,
    setConfirmOnboardDialogOpen,
    setEditConfirmOnBoardData,
    setEditOnboardData,
    useAppDispatch,
    useAppSelector,
} from './store';

const HireTable = ({ loading = false }: { loading?: boolean }) => {
    const locale = useLocale();
    const router = useRouter();
    const dispatch = useAppDispatch();
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const hireProcessFilter = useAppSelector((state) => state.foundationHire.data.hireProcessFilter);
    const selectedRowIds = useAppSelector((state) => state.foundationHire.data.selectedRowIds);

    // Reset page to 1 when filter changes
    useEffect(() => {
        setPage(1);
    }, [hireProcessFilter]);

    const { data: hireProcessData } = useGetHireProcessQuery({
        Status: hireProcessFilter.status,
        StartDate: hireProcessFilter.dateRange[0],
        EndDate: hireProcessFilter.dateRange[1],
        Type: hireProcessFilter.keywordType,
        Keyword: hireProcessFilter.keyword,
    });

    // 全部已確認資料
    const allConfirmedList = hireProcessData?.data?.filter(
        (row) => row.confirmEmployeeData === ConfirmEmployeeDataEnum.Hire確認,
    );

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            // 只選取前 25 筆資料
            const selectableIds = allConfirmedList?.slice(0, 25).map((row) => row.onboardingId) || [];
            dispatch(selectAllRows(selectableIds));
        } else {
            dispatch(deselectAllRows());
        }
    };

    const handleSelectRow = (id: string, checked: boolean) => {
        if (checked) {
            // 檢查是否已經選取了 25 筆
            if (selectedRowIds.length >= 25) {
                return;
            }
            dispatch(selectRow(id));
        } else {
            dispatch(deselectRow(id));
        }
    };

    const handleOpenCancelOnboardDialog = (original: HireProcessItem) => {
        dispatch(setEditOnboardData(original));
        dispatch(setCancelOnboardDialogOpen(true));
    };

    const handleOpenConfirmOnboardDialog = (original: HireProcessItem) => {
        dispatch(setEditConfirmOnBoardData(original));
        dispatch(setConfirmOnboardDialogOpen(true));
    };

    const columns: ColumnDef<HireProcessItem>[] = [
        {
            id: 'select',
            enableResizing: false,
            size: 40,
            header: () => {
                if (!allConfirmedList || allConfirmedList.length <= 0) {
                    return null;
                }

                return (
                    <Checkbox
                        checked={selectedRowIds.length >= (allConfirmedList?.length || 0)}
                        onCheckedChange={handleSelectAll}
                        aria-label="Select all"
                    />
                );
            },
            cell: ({ row }) => {
                const { confirmEmployeeData } = row.original;

                if (confirmEmployeeData !== ConfirmEmployeeDataEnum.Hire確認) {
                    return null;
                }

                return (
                    <Checkbox
                        checked={selectedRowIds.includes(row.original.onboardingId)}
                        onCheckedChange={(checked: boolean) => handleSelectRow(row.original.onboardingId, checked)}
                        aria-label="Select row"
                    />
                );
            },

            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: 'onboardingDate',
            enableResizing: false,
            size: 100,
            header: ({ column }) => (
                <div
                    className="flex cursor-pointer items-center whitespace-nowrap"
                    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                >
                    <span>報到日期</span>
                    <ArrowUpDown className="ml-1" size={15} />
                </div>
            ),
            cell: ({ row }) => <div className="whitespace-nowrap">{row.getValue('onboardingDate')}</div>,
        },
        {
            id: 'name',
            enableResizing: false,
            size: 100,
            header: ({ column }) => (
                <div
                    className="flex cursor-pointer items-center whitespace-nowrap"
                    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                >
                    <span>姓名</span>
                    <ArrowUpDown className="ml-1" size={15} />
                </div>
            ),
            accessorFn: (row) => `${row.lastName}${row.firstName}${row.englishName || ''}`,
            cell: ({ row }) => {
                const { isReHired, lastName, firstName } = row.original;
                return (
                    <div className="flex items-center gap-1 whitespace-nowrap">
                        {isReHired && (
                            <Tooltip content="再雇用">
                                <Info className="text-primary size-4" />
                            </Tooltip>
                        )}

                        {lastName}
                        {firstName}
                    </div>
                );
            },
        },
        {
            id: 'deptName',
            enableResizing: false,
            size: 150,
            header: () => (
                <div className="flex cursor-pointer items-center whitespace-nowrap">
                    <span>單位</span>
                </div>
            ),
            cell: ({ row }) => {
                const { confirmEmployeeData } = row.original;

                const isInvalid = confirmEmployeeData === ConfirmEmployeeDataEnum.部門已失效;

                const text = isInvalid ? '此單位不存在' : row.original.deptName;

                return (
                    <div
                        className={classNames('whitespace-nowrap', {
                            'text-red-500': isInvalid,
                        })}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            id: 'supervisor',
            enableResizing: false,
            size: 100,
            header: () => (
                <div className="flex cursor-pointer items-center whitespace-nowrap">
                    <span>直屬主管</span>
                </div>
            ),
            // ConfirmEmployeeData
            cell: ({ row }) => {
                const { confirmEmployeeData, supervisorCName, supervisorEName } = row.original;

                if (confirmEmployeeData === ConfirmEmployeeDataEnum.部門職位不存在) {
                    return <div className="whitespace-nowrap text-red-500">此人員不存在</div>;
                }

                return (
                    <div className="whitespace-nowrap">
                        {toLower(locale) === 'zh-tw' ? supervisorCName : supervisorEName}
                    </div>
                );
            },
        },
        {
            id: 'employeeGroup',
            enableResizing: false,
            size: 100,
            header: ({ column }) => (
                <div
                    className="flex cursor-pointer items-center whitespace-nowrap"
                    onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                >
                    <span>身份類別</span>
                </div>
            ),
            cell: ({ row }) => <div className="whitespace-nowrap">{row.original.employeeGroupName}</div>,
        },
        {
            id: 'location',
            enableResizing: false,
            size: 100,
            header: () => (
                <div className="flex cursor-pointer items-center whitespace-nowrap">
                    <span>地區</span>
                </div>
            ),
            cell: ({ row }) => <div className="whitespace-nowrap">{row.original.workCountyName}</div>,
        },
        // TODO: 等待後端資料完成
        // {
        //     accessorKey: 'confirmEmployeeData',
        //     enableResizing: false,
        //     size: 200,
        //     header: ({ column }) => (
        //         <div
        //             className="flex cursor-pointer items-center whitespace-nowrap"
        //             onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        //         >
        //             <span>資料完成度</span>
        //             <ArrowUpDown className="ml-1" size={15} />
        //         </div>
        //     ),
        //     cell: ({ row }) => (
        //         <div className="h-2 w-full rounded-full bg-gray-200">
        //             <div
        //                 className="h-2 rounded-full bg-blue-500"
        //                 style={{ width: `${row.getValue('confirmEmployeeData')}%` }}
        //             />
        //         </div>
        //     ),
        // },
        {
            // 放棄報到原因
            id: 'reason',
            enableResizing: false,
            size: 100,
            header: () => (
                <div className="flex cursor-pointer items-center whitespace-nowrap">
                    <span>放棄報到原因</span>
                </div>
            ),
            cell: ({ row }) => (
                // ReasonCode
                <div className="whitespace-nowrap">{row.original?.reasons}</div>
            ),
        },
        {
            id: 'actions',
            header: () => <div className="ml-2 whitespace-nowrap">功能</div>,
            enableResizing: false,
            size: 120,
            meta: {
                pin: 'right',
            },
            cell: ({ row }) => {
                const { confirmEmployeeData } = row.original;

                return (
                    <div className="flex items-center gap-1">
                        <Tooltip content="編輯">
                            <Button
                                variant="ghost"
                                color="secondary"
                                size="sm"
                                onClick={() => {
                                    router.push(`${prodPath}/foundation/hire/${row.original.onboardingId}`);
                                }}
                            >
                                <Edit className="h-4 w-4" />
                            </Button>
                        </Tooltip>
                        <Tooltip content="確認報到">
                            <Button
                                variant="ghost"
                                color="secondary"
                                size="sm"
                                onClick={() => {
                                    handleOpenConfirmOnboardDialog(row.original);
                                }}
                            >
                                <FileUser
                                    className={classNames('h-4 w-4', {
                                        'text-primary': confirmEmployeeData === ConfirmEmployeeDataEnum.Hire確認,
                                    })}
                                />
                            </Button>
                        </Tooltip>
                        <Tooltip content="取消報到">
                            <Button
                                variant="ghost"
                                color="secondary"
                                size="sm"
                                onClick={() => {
                                    handleOpenCancelOnboardDialog(row.original);
                                }}
                            >
                                <UserX className="h-4 w-4" />
                            </Button>
                        </Tooltip>
                    </div>
                );
            },
        },
    ];

    return (
        <div className="min-h-[500px] w-full">
            <DataTable
                loading={loading}
                columns={columns}
                data={hireProcessData?.data || []}
                page={page}
                pageSize={pageSize}
                totalItems={hireProcessData?.data?.length}
                onPageChange={setPage}
                onPageSizeChange={setPageSize}
            />

            <div className="mt-2 text-sm text-gray-500">備註：最多可同時選取 25 筆資料</div>

            <ActionBar />
        </div>
    );
};

export default HireTable;
