import { zodResolver } from '@hookform/resolvers/zod';
import {
    DialogComponent,
    Form,
    FormControl,
    FormField,
    FormItemContent,
    Input,
    Select,
    Textarea,
    toast,
} from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import { get } from 'lodash';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { useGetHireProcessOptionsQuery, useGiveUpHireProcessMutation } from 'xe/redux/query';
import { z } from 'zod';

import { setCancelOnboardDialogOpen, useAppDispatch, useAppSelector } from '../store';

const CancelOnboardSchema = z.object({
    name: z.string().min(1, { message: '請輸入姓名' }),
    reason: z.string().min(1, { message: '請選擇原因' }),
    description: z.string().max(250, { message: '最多250字' }).optional(),
});

const CancelOnboardDialog = ({ onboardingDate }: { onboardingDate?: string }) => {
    const dispatch = useAppDispatch();
    const { editOnboardData } = useAppSelector((state) => state.foundationHire.data);
    const cancelOnboardDialogOpen = useAppSelector((state) => state.foundationHire.data.cancelOnboardDialogOpen);

    const [giveUpHireProcess, { isLoading: isGiveUpHireProcessLoading }] = useGiveUpHireProcessMutation();

    const { data, isFetching: isGetHireProcessOptionsFetching } = useGetHireProcessOptionsQuery(
        {
            onboardingDate: dayjs(onboardingDate).format('YYYY-MM-DD'),
        },
        {
            skip: !onboardingDate,
        },
    );
    const hireProcessOptions = data?.data;
    const baseSettingOptionsMap = hireProcessOptions?.baseSettingOptionsMap;

    // RmsRejectReason
    const rmsRejectReasonOptions = get(baseSettingOptionsMap, 'RmsRejectReason', []);

    const form = useForm<z.infer<typeof CancelOnboardSchema>>({
        resolver: zodResolver(CancelOnboardSchema),
        defaultValues: {
            name: '',
            reason: '',
            description: '',
        },
        mode: 'onChange',
        reValidateMode: 'onChange',
    });

    const handleCancelOnboardDialogChange = (open: boolean) => {
        form.reset();
        dispatch(setCancelOnboardDialogOpen(open));
    };

    const onSubmit = async (values: z.infer<typeof CancelOnboardSchema>) => {
        if (!editOnboardData?.onboardingId) {
            toast.error('報到ID不存在');
            return;
        }

        try {
            const payload = {
                onboardingId: editOnboardData?.onboardingId,
                onboardingStatus: editOnboardData?.onboardingStatus,
                reasonCode: values.reason,
                reasons: values.description || '',
            };
            await giveUpHireProcess(payload).unwrap();
            toast.success('取消報到成功');

            handleCancelOnboardDialogChange(false);
        } catch (error) {
            toast.error('取消報到失敗');
        } finally {
            handleCancelOnboardDialogChange(false);
            form.reset();
        }
    };

    useEffect(() => {
        if (cancelOnboardDialogOpen && editOnboardData) {
            form.setValue('name', `${editOnboardData?.lastName || ''}${editOnboardData?.firstName || ''}`);
        }
    }, [cancelOnboardDialogOpen, editOnboardData]);

    return (
        <DialogComponent
            open={cancelOnboardDialogOpen}
            onOpenChange={handleCancelOnboardDialogChange}
            size="md"
            title="放棄報到"
            confirmLabel="確定"
            cancelLabel="取消"
            isLoading={isGiveUpHireProcessLoading}
            onConfirmClick={() => {
                form.trigger().then((isValid) => {
                    if (isValid) {
                        onSubmit(form.getValues());
                    }
                });
            }}
            onCancelClick={() => {
                handleCancelOnboardDialogChange(false);
            }}
        >
            <Form {...form}>
                <form className="flex w-full flex-col space-y-4 px-4" onSubmit={form.handleSubmit(onSubmit)}>
                    {/* 姓名 */}
                    <FormItemContent
                        name="name"
                        label="姓名"
                        control={form.control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                    <Input
                                        disabled
                                        value={field.value}
                                        onChange={(event: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(event.target.value)
                                        }
                                    />
                                )}
                            />
                        }
                    />

                    {/* 原因欄位 */}
                    <FormItemContent
                        name="reason"
                        label="原因"
                        control={form.control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="reason"
                                render={({ field }) => (
                                    <Select
                                        isLoading={isGetHireProcessOptionsFetching}
                                        options={rmsRejectReasonOptions}
                                        value={rmsRejectReasonOptions?.find(
                                            (option: any) => String(option.value) === String(field.value),
                                        )}
                                        onChange={(option: any) => {
                                            field.onChange(option.value);
                                            (document?.activeElement as HTMLElement)?.blur();
                                        }}
                                        formatOptionLabel={(option: any) => option.text}
                                    />
                                )}
                            />
                        }
                    />
                    {/* 說明欄位 */}
                    <FormItemContent
                        name="description"
                        label="說明"
                        control={form.control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="description"
                                render={({ field }) => (
                                    <FormControl>
                                        <Textarea
                                            maxLength="250"
                                            className="w-full"
                                            value={field.value}
                                            onChange={(event: React.ChangeEvent<HTMLTextAreaElement>) =>
                                                field.onChange(event.target.value)
                                            }
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </form>
            </Form>
        </DialogComponent>
    );
};

export default CancelOnboardDialog;
