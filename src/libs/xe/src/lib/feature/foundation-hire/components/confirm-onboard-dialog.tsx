import { Card, DialogComponent, Form, ParentCenterLoading, toast } from '@mayo/mayo-ui-beta/v2';
import classNames from 'classnames';
import { Check } from 'lucide-react';
import { useEffect, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import { useGetHireProcessRequiredDocumentQuery, useUpdateHireProcessRequiredDocumentMutation } from 'xe/redux/query';

import { setConfirmOnboardDialogOpen, setEditConfirmOnBoardData, useAppDispatch, useAppSelector } from '../store';
interface RequiredDocumentItem {
    onboardingId: string;
    requiredDocumentId: string;
    requiredDocumentName: string;
    isProvided: boolean;
}

interface ConfirmOnboardFormValues {
    name: string;
    requiredDocuments: RequiredDocumentItem[];
}

const ConfirmOnboardDialog = () => {
    const dispatch = useAppDispatch();
    const confirmOnboardDialogOpen = useAppSelector((state) => state.foundationHire.data.confirmOnboardDialogOpen);
    const { editConfirmOnBoardData } = useAppSelector((state) => state.foundationHire.data);

    const name = `${editConfirmOnBoardData?.lastName || ''}${editConfirmOnBoardData?.firstName || ''}`;

    const { data: requiredDocument, isFetching: isFetchingRequiredDocument } = useGetHireProcessRequiredDocumentQuery(
        editConfirmOnBoardData?.onboardingId || '',
        {
            skip: !editConfirmOnBoardData?.onboardingId || !confirmOnboardDialogOpen,
        },
    );

    const [updateHireProcessRequiredDocument, { isLoading: isLoadingUpdateRequiredDocument }] =
        useUpdateHireProcessRequiredDocumentMutation();

    const form = useForm<ConfirmOnboardFormValues>({
        defaultValues: {
            requiredDocuments: [],
        },
        mode: 'onChange',
        reValidateMode: 'onChange',
    });

    // Effect to reset form when data is fetched or dialog visibility changes
    useEffect(() => {
        if (
            !isFetchingRequiredDocument &&
            editConfirmOnBoardData?.onboardingId &&
            requiredDocument &&
            confirmOnboardDialogOpen
        ) {
            // Data is fetched and dialog is open
            form.reset({
                requiredDocuments: requiredDocument?.data?.map((doc: any) => ({
                    onboardingId: doc.onboardingId,
                    requiredDocumentId: doc.requiredDocumentId,
                    requiredDocumentName: doc.requiredDocumentName,
                    isProvided: doc.isProvided || false, // Ensure isProvided exists, default to false
                })),
            });
        } else if (!confirmOnboardDialogOpen) {
            // Dialog is closed, reset related state
            form.reset({ name: '', requiredDocuments: [] }); // Reset form to initial state
        }
    }, [isFetchingRequiredDocument, editConfirmOnBoardData?.onboardingId, confirmOnboardDialogOpen]);

    const onSubmit = async (values: ConfirmOnboardFormValues) => {
        const payload = {
            requiredDocuments: values.requiredDocuments,
            onboardingId: editConfirmOnBoardData?.onboardingId || '',
        };
        try {
            await updateHireProcessRequiredDocument(payload).unwrap();
            toast.success('更新成功');
        } catch (error) {
            toast.error('更新失敗');
        } finally {
            dispatch(setConfirmOnboardDialogOpen(false));
            form.reset();
        }
    };

    const watchedDocs = form.watch('requiredDocuments');

    const allSelected = useMemo(
        () => watchedDocs && watchedDocs.length > 0 && watchedDocs.every((doc) => doc.isProvided),
        [watchedDocs],
    );

    const toggleFile = (fileId: string) => {
        const currentDocs = form.getValues('requiredDocuments');
        const updatedDocs = currentDocs.map((doc) =>
            doc.requiredDocumentId === fileId ? { ...doc, isProvided: !doc.isProvided } : doc,
        );
        form.setValue('requiredDocuments', updatedDocs, { shouldDirty: true, shouldValidate: true });
    };

    const toggleAll = () => {
        const currentDocs = form.getValues('requiredDocuments');
        const newIsProvidedState = !allSelected;
        const updatedDocs = currentDocs.map((doc) => ({
            ...doc,
            isProvided: newIsProvidedState,
        }));
        form.setValue('requiredDocuments', updatedDocs, { shouldDirty: true, shouldValidate: true });
    };

    const handleConfirmOnboardDialogChange = (open: boolean) => {
        if (!open) {
            dispatch(setEditConfirmOnBoardData(undefined));
            form.reset();
        }
        dispatch(setConfirmOnboardDialogOpen(open));
    };

    return (
        <DialogComponent
            open={confirmOnboardDialogOpen}
            onOpenChange={handleConfirmOnboardDialogChange}
            size="md"
            title="確認報到文件"
            confirmLabel="確定"
            cancelLabel="取消"
            isLoading={isLoadingUpdateRequiredDocument}
            onConfirmClick={() => {
                form.trigger().then((isValid) => {
                    if (isValid) {
                        onSubmit(form.getValues());
                    }
                });
            }}
            onCancelClick={() => {
                handleConfirmOnboardDialogChange(false);
            }}
        >
            {isFetchingRequiredDocument ? (
                <ParentCenterLoading />
            ) : (
                <Form {...form}>
                    <form className="flex w-full flex-col space-y-4 px-4" onSubmit={form.handleSubmit(onSubmit)}>
                        {/* 姓名 */}
                        <div className="mb-2 flex items-center">
                            <span className="text-md font-medium">姓名：</span>
                            <span className="text-base font-medium">{name}</span>
                        </div>
                        {/* 應繳文件區塊 */}
                        <div>
                            <div className="mb-2 flex items-center justify-between">
                                <span className="text-md font-medium">應繳文件</span>
                                {watchedDocs && watchedDocs.length > 0 && (
                                    <button
                                        type="button"
                                        className="text-primary text-sm underline"
                                        onClick={toggleAll}
                                    >
                                        {allSelected ? '取消全選' : '全選'}
                                    </button>
                                )}
                            </div>
                            <div className="grid grid-cols-2 gap-3">
                                {watchedDocs?.map((doc) => {
                                    const checked = doc.isProvided;
                                    return (
                                        <Card
                                            key={doc.requiredDocumentId}
                                            bordered
                                            className={classNames(
                                                'flex cursor-pointer items-center justify-between rounded-md border px-4 py-2 text-left transition-colors',
                                                {
                                                    'border-primary': checked,
                                                    'hover:bg-neutral-50': !checked,
                                                },
                                            )}
                                            onClick={() => toggleFile(doc.requiredDocumentId)}
                                        >
                                            <span className="text-sm">{doc.requiredDocumentName}</span>
                                            {checked && <Check className="text-primary size-4" />}
                                        </Card>
                                    );
                                })}
                            </div>
                        </div>
                    </form>
                </Form>
            )}
        </DialogComponent>
    );
};

export default ConfirmOnboardDialog;
