import { zodResolver } from '@hookform/resolvers/zod';
import {
    Alert,
    Avatar,
    Button,
    Card,
    CardContent,
    DatePicker,
    DialogComponent,
    Form,
    FormControl,
    FormField,
    FormItemContent,
    Input,
    ParentCenterLoading,
    Select,
    TabsComponent,
    toast,
} from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import { find, get } from 'lodash';
import { SearchIcon } from 'lucide-react';
import { useRouter } from 'nextjs-toploader/app';
import React, { useEffect, useState } from 'react';
import { Control, FormProvider, useForm, useFormContext } from 'react-hook-form';
import prodPath from 'xe/utils/prodPath';
import ClientService from 'xe/utils/service/clientService';
import { z } from 'zod';

import {
    useCreateHireProcessPersonalMutation,
    useGetHireProcessOptionsQuery,
    useValidateIdNumberMutation,
} from '../../../redux/query/hireProcessApi';
import { setNewHireDialogOpen, useAppDispatch, useAppSelector } from '../store';

const NewHireSchema = z.object({
    onboardingDate: z.string().min(1, { message: '請選擇到職日期' }),
    lastName: z.string().min(1, { message: '請輸入姓氏' }),
    firstName: z.string().min(1, { message: '請輸入名字' }),
    gender: z.string().min(1, { message: '請選擇性別' }),
    idNumber: z.string().min(1, { message: '請輸入身分證字號' }),
    idType: z.string().min(1, { message: '請選擇身分證類型' }),
    searchText: z.string().optional(),
});

type ManualNewHireProps = {
    control: Control<z.infer<typeof NewHireSchema>>;
    onValidateIdNumber: () => void;
    isValidateIdNumberLoading: boolean;
};

const ManualNewHire = ({ control, onValidateIdNumber, isValidateIdNumberLoading }: ManualNewHireProps) => {
    const { getValues, clearErrors } = useFormContext();
    const { data } = useGetHireProcessOptionsQuery({
        onboardingDate: dayjs(getValues('onboardingDate')).format('YYYY-MM-DD'),
    });
    const baseSettingOptionsMap = data?.data?.baseSettingOptionsMap;

    const idTypeOptions = get(baseSettingOptionsMap, 'IDType', []);

    const sexOptions = get(baseSettingOptionsMap, 'Sex', []);

    return (
        <>
            <div className="col-span-2">
                <FormItemContent
                    name="onboardingDate"
                    label="到職日"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="onboardingDate"
                            render={({ field }) => (
                                <FormControl>
                                    <DatePicker
                                        className="w-full"
                                        value={field.value}
                                        onChange={(date) => field.onChange(date)}
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <div className="col-span-3 flex items-end">
                    <Alert
                        alertDesc="到職日變更後，部分設定可能需要重新調整。"
                        alertIcon={null}
                        showActionButton={false}
                        variant="warning"
                        className="mt-2 w-full max-w-full"
                    />
                </div>
            </div>

            <div className="col-span-1">
                <FormItemContent
                    name="lastName"
                    label="姓"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="lastName"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            <div className="col-span-1">
                <FormItemContent
                    name="firstName"
                    label="名"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="firstName"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            <div className="col-span-1">
                <FormItemContent
                    name="idType"
                    label="身分證類型"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="idType"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={find(idTypeOptions, { value: field?.value }) || null}
                                        onChange={(option: any) => {
                                            field.onChange(option?.value || '');
                                        }}
                                        options={idTypeOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            <div className="col-span-1">
                <FormItemContent
                    name="idNumber"
                    label="身分證件號碼"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="idNumber"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        isLoading={isValidateIdNumberLoading}
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                            field.onChange(e.target.value);
                                            clearErrors('idNumber');
                                        }}
                                        onBlur={onValidateIdNumber}
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            <div className="col-span-1">
                <FormItemContent
                    name="gender"
                    label="性別"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="gender"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={find(sexOptions, { value: field?.value }) || null}
                                        onChange={(option: any) => {
                                            field.onChange(option?.value || '');
                                        }}
                                        options={sexOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>
        </>
    );
};

const NewOnboardDialog = () => {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState('1');
    const dispatch = useAppDispatch();

    const newHireDialogOpen = useAppSelector((state) => state.foundationHire.data.newHireDialogOpen);

    const [validateIdNumber, { isLoading: isValidateIdNumberLoading }] = useValidateIdNumberMutation();
    const [createHireProcessPersonal] = useCreateHireProcessPersonalMutation();
    const [submitLoading, setSubmitLoading] = useState(false);

    const [talentPoolList, setTalentPoolList] = useState<any[]>([]);

    const [selectedTalent, setSelectedTalent] = useState<any>(null);

    const form = useForm<z.infer<typeof NewHireSchema>>({
        resolver: zodResolver(NewHireSchema),
        defaultValues: {
            onboardingDate: new Date().toDateString(),
            lastName: '',
            firstName: '',
            gender: '',
            idType: '1',
            idNumber: '',
            searchText: '',
        },
        mode: 'onChange',
        reValidateMode: 'onChange',
    });

    const values = form.watch();

    useEffect(() => {
        if (values.onboardingDate) {
            form.setValue('lastName', '');
            form.setValue('firstName', '');
            form.setValue('gender', '');
            form.setValue('idType', '1');
            form.setValue('idNumber', '');
        }
    }, [values.onboardingDate]);

    const handleSearchKeyDown = async (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (submitLoading) return;

        if (e.key === 'Enter') {
            const keyword = form.getValues('searchText');
            if (!keyword) return;
            const res = await ClientService.GET<{
                data: {
                    cvId: string;
                    name: string;
                    personalPictureFileUrl: string | null;
                    mobileNumber: string | null;
                    email: string;
                    academicDegreeName: string;
                    academyName: string | null;
                    companyName: string;
                    jobTitle: string;
                    seniority: number;
                }[];
                meta: {
                    httpStatusCode: number | string;
                };
            }>(`/v2/platform-api/api/HireProcess/Rms/Cv?keyword=${keyword}`);
            setTalentPoolList(res?.data || []);
        }
    };

    const onSubmit = async (values: z.infer<typeof NewHireSchema>) => {
        setSubmitLoading(true);

        try {
            if (activeTab === '1') {
                const isIdNumberValid = await handleValidateIdNumber();
                if (!isIdNumberValid) {
                    return;
                }

                const payload = {
                    firstName: values.firstName,
                    lastName: values.lastName,
                    onboardingDate: dayjs(values.onboardingDate).format('YYYY-MM-DD'),
                    gender: Number(values.gender),
                    idType: 1,
                    idNumber: values.idNumber,
                };

                const res = await createHireProcessPersonal(payload);

                router.push(`${prodPath}/foundation/hire/${res?.data?.onboardingId}`);
                // 驗證身分證
            } else {
                // 自人才庫匯入

                console.log('選中的人才:', selectedTalent);
                const res = await ClientService.GET(`/v2/platform-api/api/HireProcess/Rms/Cv/${selectedTalent?.cvId}`);
                console.log(res);
            }
        } catch (error) {
            toast.error('新增人員失敗');
        } finally {
            setSubmitLoading(false);
            // dispatch(setNewHireDialogOpen(false));
            // form.reset();
        }
    };

    const handleValidateIdNumber = async () => {
        if (!values.idNumber || !values.idType) {
            return;
        }

        const payload = {
            idType: values.idType,
            idNumber: values.idNumber,
            onboardingDate: dayjs(values.onboardingDate).format('YYYY-MM-DD'),
        };
        try {
            await validateIdNumber(payload).unwrap();

            return true;
        } catch (error: any) {
            const errorMessage = error?.response?.data?.message || '身分證號碼驗證失敗';
            form.setError('idNumber', { message: errorMessage });
            return false;
        }
    };

    const loading = submitLoading;

    return (
        <DialogComponent
            open={newHireDialogOpen}
            onOpenChange={() => dispatch(setNewHireDialogOpen(false))}
            size="lg"
            title="新增人員"
            confirmLabel="確定"
            cancelLabel="取消"
            onConfirmClick={form.handleSubmit(onSubmit)}
            onCancelClick={() => {
                form.reset();
                dispatch(setNewHireDialogOpen(false));
            }}
        >
            <FormProvider {...form}>
                <Form {...form}>
                    {loading && <ParentCenterLoading />}
                    <form className="flex w-full flex-col space-y-4 p-4" onSubmit={form.handleSubmit(onSubmit)}>
                        <TabsComponent
                            value={activeTab}
                            onValueChange={setActiveTab}
                            tabsOption={[
                                {
                                    disabled: false,
                                    label: '手動新增人員',
                                    value: '1',
                                    children: activeTab === '1' && (
                                        <div className="mt-4 grid grid-cols-2 gap-4">
                                            <ManualNewHire
                                                control={form.control}
                                                onValidateIdNumber={handleValidateIdNumber}
                                                isValidateIdNumberLoading={isValidateIdNumberLoading}
                                            />
                                        </div>
                                    ),
                                },
                                {
                                    disabled: false,
                                    label: '自人才庫匯入',
                                    value: '2',
                                    children: activeTab === '2' && (
                                        <div className="mt-4 grid grid-cols-2 gap-4">
                                            <div className="xs:col-span-2 col-span-1">
                                                <FormItemContent
                                                    name="onboardingDate"
                                                    label="到職日"
                                                    required
                                                    control={form.control}
                                                    fieldClassName="w-full"
                                                    itemComponent={
                                                        <FormField
                                                            control={form.control}
                                                            name="onboardingDate"
                                                            render={({ field }) => (
                                                                <FormControl>
                                                                    <DatePicker
                                                                        disabled={loading}
                                                                        className="w-full"
                                                                        value={field.value}
                                                                        onChange={(date) => {
                                                                            field.onChange(date);
                                                                        }}
                                                                    />
                                                                </FormControl>
                                                            )}
                                                        />
                                                    }
                                                />
                                            </div>

                                            <div className="xs:col-span-2 col-span-1">
                                                <FormItemContent
                                                    name="searchText"
                                                    label="搜尋"
                                                    control={form.control}
                                                    fieldClassName="w-full"
                                                    itemComponent={
                                                        <FormField
                                                            control={form.control}
                                                            name="searchText"
                                                            render={({ field }) => (
                                                                <FormControl>
                                                                    <Input
                                                                        placeholder="搜尋身分證編號 / 姓名"
                                                                        className="pl-10"
                                                                        suffixIcon={<SearchIcon size={16} />}
                                                                        showSuffixIcon
                                                                        value={field.value}
                                                                        onChange={(
                                                                            e: React.ChangeEvent<HTMLInputElement>,
                                                                        ) => field.onChange(e.target.value)}
                                                                        onKeyDown={handleSearchKeyDown}
                                                                    />
                                                                </FormControl>
                                                            )}
                                                        />
                                                    }
                                                />
                                            </div>

                                            <div className="col-span-2">
                                                <div className="scrollbar-none grid grid-cols-2 gap-4 overflow-y-auto">
                                                    {talentPoolList.map((item) => (
                                                        <Card
                                                            bordered
                                                            key={item.cvId || item.email}
                                                            className={`hover:border-primary-lighter cursor-pointer ${selectedTalent?.cvId === item.cvId ? 'border-primary' : ''}`}
                                                            onClick={() => setSelectedTalent(item)}
                                                        >
                                                            <CardContent>
                                                                <div className="flex flex-col gap-2">
                                                                    <div className="flex items-start justify-between">
                                                                        <div>
                                                                            <Avatar
                                                                                alt="avatar"
                                                                                src={
                                                                                    item.personalPictureFileUrl ||
                                                                                    'https://i.pravatar.cc/300'
                                                                                }
                                                                                size="lg"
                                                                            />
                                                                        </div>
                                                                        <div className="mt-1 flex flex-col">
                                                                            <div className="text-md font-medium">
                                                                                {item.name}
                                                                            </div>
                                                                            <div className="text-sm text-gray-500">
                                                                                {item.mobileNumber}
                                                                            </div>
                                                                            <div className="text-sm text-gray-500">
                                                                                {item.email}
                                                                            </div>
                                                                        </div>
                                                                        <div>
                                                                            <Button variant="link" size="sm">
                                                                                檢視履歷
                                                                            </Button>
                                                                        </div>
                                                                    </div>
                                                                    <Card className="bg-gray-50">
                                                                        <CardContent>
                                                                            <div className="grid grid-cols-2 gap-4">
                                                                                <div className="space-y-2">
                                                                                    <div className="text-sm text-gray-500">
                                                                                        最高學歷
                                                                                    </div>
                                                                                    <div className="text-sm">
                                                                                        {item.academicDegreeName}
                                                                                    </div>
                                                                                </div>
                                                                                <div className="space-y-2">
                                                                                    <div className="text-sm text-gray-500">
                                                                                        學校名稱
                                                                                    </div>
                                                                                    <div className="text-sm">
                                                                                        {item.academyName}
                                                                                    </div>
                                                                                </div>
                                                                                <div className="space-y-2">
                                                                                    <div className="text-sm text-gray-500">
                                                                                        最近公司工作
                                                                                    </div>
                                                                                    <div className="text-sm">
                                                                                        {item.companyName}
                                                                                    </div>
                                                                                </div>
                                                                                <div className="space-y-2">
                                                                                    <div className="text-sm text-gray-500">
                                                                                        最近公司職稱
                                                                                    </div>
                                                                                    <div className="text-sm">
                                                                                        {item.jobTitle}
                                                                                    </div>
                                                                                </div>
                                                                                <div className="space-y-2">
                                                                                    <div className="text-sm text-gray-500">
                                                                                        工作年資
                                                                                    </div>
                                                                                    <div className="text-sm">
                                                                                        {item.seniority} 年
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </CardContent>
                                                                    </Card>
                                                                </div>
                                                            </CardContent>
                                                        </Card>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    ),
                                },
                            ]}
                        />
                    </form>
                </Form>
            </FormProvider>
        </DialogComponent>
    );
};

export default NewOnboardDialog;
