import { Button, toast } from '@mayo/mayo-ui-beta/v2';

import { setNewHireDialogOpen, useAppDispatch } from '../store';

const HireActions = () => {
    const dispatch = useAppDispatch();

    return (
        <div className="flex gap-2">
            <Button
                variant="outline"
                size="md"
                onClick={() =>
                    toast('即將推出', {
                        position: 'bottom-right',
                        icon: '🚀',
                    })
                }
            >
                Excel 匯出下載
            </Button>
            {/* 
<Button
variant="outline"
size="md"
onClick={() =>
    toast('即將推出', {
        position: 'bottom-right',
        icon: '🚀',
    })
}
className="flex text-nowrap"
>
批次匯入
</Button> */}

            <Button variant="default" size="md" onClick={() => dispatch(setNewHireDialogOpen(true))}>
                新增
            </Button>
        </div>
    );
};

export default HireActions;
