import { zodResolver } from '@hookform/resolvers/zod';
import { <PERSON><PERSON>, Card, CardContent, Form, MaskLoading } from '@mayo/mayo-ui-beta/v2';
import { CalendarCheck } from 'lucide-react';
import React from 'react';
import { FormProvider,useForm } from 'react-hook-form';
import ProgressBar from 'xe/components/ProgressBar';
import { z } from 'zod';

import { BreadcrumbItem, Breadcrumbs } from '../../components/breadcrumb';
import { SectionHeader } from '../../components/SectionHeader';
import SalaryFormFields from './components/SalaryFormFields';
import SalaryTypesTable from './components/SalaryTypesTable';
import WelfareSwitch from './components/WelfareSwitch';
import { FormSchema } from './form/schema';
import { defaultValues } from './form/types';

const breadcrumbItems: BreadcrumbItem[] = [
    { label: '流程專區', href: '' },
    { label: '人事異動', href: '' },
    { label: '新進人員', href: '' },
    { label: '薪資設定', href: '', isCurrent: true },
];

const PayrollSalary = ({ onboardingId }: { onboardingId: string }) => {
    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues,
    });

    const { control } = form;

    const onSubmit = async (values: any) => {
        console.log('values', values);
    };

    const isLoading = false;

    const [salaryChangeTypes, setSalaryChangeTypes] = React.useState(['新進', '新進']);

    return (
        <div className="flex flex-col gap-4">
            {isLoading && <MaskLoading />}

            <Breadcrumbs items={breadcrumbItems} />
            <FormProvider {...form}>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)}>
                        <div className="flex flex-col gap-4">
                            <h1 className="text-xl font-bold">新進人員資料</h1>
                            <ProgressBar onboardingId={onboardingId} onSave={form.handleSubmit(onSubmit)} />
                            {/* 內容卡片 */}
                            <Card className="scrollbar-none h-full w-full overflow-scroll px-10 py-12">
                                <CardContent className="flex flex-col gap-8">
                                    <div>
                                        <SectionHeader title="薪資設定" />
                                        <Alert
                                            alertDesc="生效日：2025/05/05"
                                            alertIcon={<CalendarCheck />}
                                            showActionButton={false}
                                            variant="info"
                                            className="mb-4 mt-2 w-full max-w-full bg-[#f1fcfe]"
                                        />
                                        <div className="mb-8 grid grid-cols-1 gap-x-8 gap-y-8 sm:grid-cols-1 md:grid-cols-1 lg:grid-cols-4" />
                                        {/* 異動行為 + 福利金扣款 layout */}
                                        <div className="mb-4">
                                            <div className="text-[14px] font-normal">異動行為</div>
                                            <div className="flex w-full flex-row gap-8">
                                                {/* Left: form fields */}
                                                <SalaryFormFields
                                                    control={control}
                                                    salaryChangeTypes={salaryChangeTypes}
                                                    setSalaryChangeTypes={setSalaryChangeTypes}
                                                />
                                                {/* Right: 福利金扣款 card */}
                                                <div className="max-w-[444px] flex-1">
                                                    <WelfareSwitch />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div>
                                        <SectionHeader title="薪資科目" />
                                        <SalaryTypesTable options={[]} />
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </form>
                </Form>
            </FormProvider>
        </div>
    );
};

export default PayrollSalary;
