import { z } from 'zod';

import { FormSchema } from './schema';

export type FormData = z.infer<typeof FormSchema>;

export const defaultValues: FormData = {
    salaryChangeType: ['新進', '新進'],
    welfareEnabled: true,
    welfareType: 'ratio',
    welfareValue: '',
    salaryAreaGroup: 'TW',
    stopPayment: 'no',
    salaryScope: '',
    subScope: '',
    salaryType: '',
    transfer: 'no',
    bonusRole: '',
    notes: '',
    salaryTypes: [],
};
