import { z } from 'zod';

export const FormSchema = z.object({
    salaryChangeType: z.array(z.string().nullable()).optional(),
    welfareEnabled: z.boolean().optional(),
    welfareType: z.enum(['ratio', 'amount']).nullable().optional(),
    welfareValue: z.union([z.number(), z.string()]).nullable().optional(),
    salaryAreaGroup: z.string().nullable().optional(),
    stopPayment: z.string().nullable().optional(),
    salaryScope: z.string().nullable().optional(),
    subScope: z.string().nullable().optional(),
    salaryType: z.string().nullable().optional(),
    transfer: z.string().nullable().optional(),
    bonusRole: z.string().nullable().optional(),
    notes: z.string().nullable().optional(),
    salaryTypes: z
        .array(
            z.object({
                subject: z.string().nullable().optional(),
                amount: z.string().nullable().optional(),
            }),
        )
        .optional(),
});
