import { useCallback,useState } from 'react';
import useSWR from 'swr';
import ClientService from 'xe/utils/service/clientService';

export interface SalaryOptionsGetResponse {
    data: {
        bankOptions: Array<{
            bankId: string;
            bankName: string;
            isRequiredAccountValidation: boolean;
            bankAccountLengthLimit: number;
            isRequiredBranchCodeValidation: boolean;
            branchCodeLengthLimit: number;
        }>;
        salaryRangeOptions: Array<{
            key: string;
            value: string;
        }>;
        mealAllowanceExemption: {
            hnCode: string;
            limit: number;
        };
        salaryAccountOptions: {
            hourly: Array<{
                salaryAccountId: string;
                salaryAccountCode: string;
                salaryAccountName: string;
                addReductionItem: string;
                hnCode: string;
            }>;
            month: Array<{
                salaryAccountId: string;
                salaryAccountCode: string;
                salaryAccountName: string;
                addReductionItem: string;
                hnCode: string;
            }>;
            other: Array<{
                salaryAccountId: string;
                salaryAccountCode: string;
                salaryAccountName: string;
                addReductionItem: string;
                hnCode: string;
            }>;
        };
    };
}

function useSalaryOptionsHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);

    const { data, error, mutate } = useSWR<SalaryOptionsGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
    );

    const fetchOptions = useCallback((onboardingDate?: string) => {
        let url = '/v2/platform-api/api/HireProcess/Salary/Options';
        if (onboardingDate) {
            url += `?onboardingDate=${encodeURIComponent(onboardingDate)}`;
        }
        setEndpoint(url);
        setShouldFetch(true);
    }, []);

    const refetchOptions = useCallback(() => {
        mutate();
    }, [mutate]);

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error,
        fetch: fetchOptions,
        refetch: refetchOptions,
    };
}

export default useSalaryOptionsHook;
