import { toast } from '@mayo/mayo-ui-beta/v2';
import { useCallback, useState } from 'react';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import ClientService from 'xe/utils/service/clientService';

export interface SalaryAccountViewModel {
    salaryAccountId: string;
    salaryAccountName: string;
    salaryAccountCode: string;
    money: number;
    hnCode: string;
    addReductionItem: string;
}

export interface SalaryData {
    onboardingDate: string;
    salaryRangeId: string;
    salaryRangeName: string;
    salaryCategory: string;
    benefitDebit: number;
    bankId: string;
    bankName: string;
    bankBranchCode: string;
    bankAccount: string;
    isPrint: boolean;
    remark: string;
    salaryAccountViewModels: SalaryAccountViewModel[];
}

export interface SalaryGetResponse {
    data: SalaryData;
}

type UpdateSalaryParams = SalaryData;

async function updateSalaryMutation(url: string, { arg }: { arg: UpdateSalaryParams }) {
    return ClientService.PUT(url, arg);
}

function useSalaryHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);
    const [hasShownSuccessToast, setHasShownSuccessToast] = useState(false);

    const { data, error, mutate } = useSWR<SalaryGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
        {
            onSuccess: (_data) => {
                if (!hasShownSuccessToast && shouldFetch) {
                    toast.success('資料獲取成功');
                    setHasShownSuccessToast(true);
                }
            },
            onError: (_error) => {
                toast.error('獲取資料失敗');
                setHasShownSuccessToast(false);
            },
        },
    );

    const {
        trigger: updateSalaryTrigger,
        error: updateError,
        isMutating: isUpdating,
    } = useSWRMutation(endpoint, updateSalaryMutation, {
        onSuccess: () => {
            mutate();
            toast.info('更新資料成功');
        },
        onError: (_error) => {
            toast.error('更新資料失敗');
        },
        rollbackOnError: true,
    });

    const fetchSalary = useCallback((onboardingId: string) => {
        if (onboardingId) {
            setEndpoint(`/v2/platform-api/api/HireProcess/Salary/${onboardingId}`);
            setShouldFetch(true);
            setHasShownSuccessToast(false);
        }
    }, []);

    const refetchSalary = useCallback(() => {
        mutate();
    }, [mutate]);

    const updateSalary = async (onboardingId: string, updateData: UpdateSalaryParams) => {
        if (!onboardingId) return;
        return updateSalaryTrigger(updateData);
    };

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error: error || updateError,
        isMutating: isUpdating,
        update: updateSalary,
        fetch: fetchSalary,
        refetch: refetchSalary,
    };
}

export default useSalaryHook;
