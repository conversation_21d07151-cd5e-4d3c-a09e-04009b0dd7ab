import { Card, CardContent, Input,RadioGroupComponent, Switch } from '@mayo/mayo-ui-beta/v2';
import { DollarSign } from 'lucide-react';
import React from 'react';
import { useController,useFormContext } from 'react-hook-form';

const WelfareSwitch: React.FC = () => {
    const { control } = useFormContext();

    const { field: enabledField } = useController({ name: 'welfareEnabled', control });
    const { field: typeField } = useController({ name: 'welfareType', control });
    const { field: valueField } = useController({ name: 'welfareValue', control });

    return (
        <Card className="w-full rounded-2xl border border-gray-300 p-6">
            <CardContent className="p-0">
                <div className="mb-2 flex items-center justify-between">
                    <div className="text-[14px] font-normal">職工福利金扣款</div>
                    <Switch checked={enabledField.value} onCheckedChange={enabledField.onChange} />
                </div>
                <RadioGroupComponent
                    defaultValue={typeField.value}
                    value={typeField.value}
                    isHorizontal
                    className="text-[14px]"
                    onValueChange={typeField.onChange}
                    radioOptions={[
                        { label: '比例', value: 'ratio', disabled: !enabledField.value },
                        { label: '金額', value: 'amount', disabled: !enabledField.value },
                    ]}
                />
                <Input
                    type="number"
                    className="mt-4 max-w-[350px]"
                    inputSize="md"
                    placeholder="輸入數字"
                    showSuffixIcon
                    suffixIcon={
                        typeField.value === 'ratio' ? (
                            <span className="text-gray-500">%</span>
                        ) : (
                            <DollarSign height={16} width={16} className="text-gray-500" />
                        )
                    }
                    disabled={!enabledField.value}
                    value={valueField.value}
                    onChange={valueField.onChange}
                />
            </CardContent>
        </Card>
    );
};

export default WelfareSwitch;
