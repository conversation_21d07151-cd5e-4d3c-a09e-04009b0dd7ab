import { FormItemContent, RadioGroupComponent, Select, Textarea } from '@mayo/mayo-ui-beta/v2';
import { PlusCircle } from 'lucide-react';
import React from 'react';
import { Control } from 'react-hook-form';

import { SelectOption } from '../../../utils/enum/select';
import { FormData } from '../form/types';

interface SalaryFormFieldsProps {
    control: Control<FormData>;
    salaryChangeTypes: string[];
    setSalaryChangeTypes: React.Dispatch<React.SetStateAction<string[]>>;
}

const SalaryFormFields: React.FC<SalaryFormFieldsProps> = ({ control, salaryChangeTypes, setSalaryChangeTypes }) => (
        <div className="flex min-w-[50%] flex-1 flex-col gap-4">
            {[0, 1].map((rowIdx) => (
                <div key={rowIdx} className="flex flex-row items-center gap-4">
                    {salaryChangeTypes.slice(rowIdx * 2, rowIdx * 2 + 2).map((_, idx) => {
                        const selectIdx = rowIdx * 2 + idx;
                        return (
                            <FormItemContent
                                key={selectIdx}
                                name={`salaryChangeType.${selectIdx}`}
                                fieldClassName="w-full"
                                control={control}
                                label=""
                                renderField={({ field }) => (
                                    <Select
                                        inputId={`salaryChangeType_${selectIdx}`}
                                        options={[
                                            {
                                                label: '新進',
                                                value: '新進',
                                            },
                                        ]}
                                        value={
                                            field.value
                                                ? {
                                                      label: '新進',
                                                      value: '新進',
                                                  }
                                                : null
                                        }
                                        onChange={(option: SelectOption | null) =>
                                            field.onChange(option ? option.value : '')
                                        }
                                        placeholder="請選擇"
                                        isClearable={false}
                                        isDisabled={selectIdx === 0}
                                        className="w-full"
                                    />
                                )}
                            />
                        );
                    })}
                    {rowIdx === Math.floor((salaryChangeTypes.length - 1) / 2) && salaryChangeTypes.length < 4 && (
                        <button
                            type="button"
                            className="text-primary ml-2"
                            onClick={() => setSalaryChangeTypes([...salaryChangeTypes, '新進'])}
                            aria-label="Add 異動行為"
                        >
                            <PlusCircle size={20} />
                        </button>
                    )}
                </div>
            ))}
            <div className="mt-8 grid w-full grid-cols-2 gap-x-8 gap-y-6">
                {/* Row 1 */}
                <div className="w-full">
                    <FormItemContent
                        name="salaryAreaGroup"
                        control={control}
                        label="薪資地區群組"
                        labelClassName="mb-2 text-[14px] font-normal"
                        fieldClassName="w-full"
                        renderField={({ field }) => (
                            <Select
                                inputId="salaryAreaGroup"
                                options={[{ label: 'TW', value: 'TW' }]}
                                value={field.value ? { label: 'TW', value: 'TW' } : null}
                                onChange={(option: SelectOption | null) => field.onChange(option ? option.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="w-full"
                            />
                        )}
                    />
                </div>
                <div className="w-full">
                    <FormItemContent
                        name="stopPayment"
                        control={control}
                        label="止付"
                        labelClassName="mb-2 text-[14px] font-normal"
                        fieldClassName="w-full"
                        renderField={({ field }) => (
                            <RadioGroupComponent
                                value={String(field.value)}
                                isHorizontal
                                className="text-[14px]"
                                onValueChange={field.onChange}
                                radioOptions={[
                                    { label: '否', value: 'no' },
                                    { label: '是', value: 'yes' },
                                ]}
                            />
                        )}
                    />
                </div>
                {/* Row 2 */}
                <div className="w-full">
                    <FormItemContent
                        name="salaryScope"
                        control={control}
                        label="薪資範圍"
                        labelClassName="mb-2 text-[14px] font-normal"
                        fieldClassName="w-full"
                        renderField={({ field }) => (
                            <Select
                                inputId="salaryScope"
                                options={[]}
                                value={null}
                                onChange={(option: SelectOption | null) => field.onChange(option ? option.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="w-full"
                            />
                        )}
                    />
                </div>
                <div className="w-full">
                    <FormItemContent
                        name="subScope"
                        control={control}
                        label="子範圍"
                        labelClassName="mb-2 text-[14px] font-normal"
                        fieldClassName="w-full"
                        renderField={({ field }) => (
                            <Select
                                inputId="subScope"
                                options={[]}
                                value={null}
                                onChange={(option: SelectOption | null) => field.onChange(option ? option.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="w-full"
                            />
                        )}
                    />
                </div>
                {/* Row 3 */}
                <div className="w-full">
                    <FormItemContent
                        name="salaryType"
                        control={control}
                        label="薪資類別"
                        labelClassName="mb-2 text-[14px] font-normal"
                        fieldClassName="w-full"
                        renderField={({ field }) => (
                            <Select
                                inputId="salaryType"
                                options={[]}
                                value={null}
                                onChange={(option: SelectOption | null) => field.onChange(option ? option.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="w-full"
                            />
                        )}
                    />
                </div>
                <div className="w-full">
                    <FormItemContent
                        name="transfer"
                        control={control}
                        label="轉帳"
                        labelClassName="mb-2 text-[14px] font-normal"
                        fieldClassName="w-full"
                        renderField={({ field }) => (
                            <RadioGroupComponent
                                value={String(field.value)}
                                isHorizontal
                                className="text-[14px]"
                                onValueChange={field.onChange}
                                radioOptions={[
                                    { label: '否', value: 'no' },
                                    { label: '是', value: 'yes' },
                                ]}
                            />
                        )}
                    />
                </div>
                {/* Row 4 */}
                <div className="col-span-2 w-full">
                    <FormItemContent
                        name="bonusRole"
                        control={control}
                        label="獎金角色"
                        labelClassName="mb-2 text-[14px] font-normal"
                        fieldClassName="w-full"
                        renderField={({ field }) => (
                            <Select
                                inputId="bonusRole"
                                options={[]}
                                value={null}
                                onChange={(option: SelectOption | null) => field.onChange(option ? option.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="w-full"
                            />
                        )}
                    />
                </div>
                {/* 備註 textarea, full width */}
                <div className="col-span-2">
                    <FormItemContent
                        fieldClassName="w-full"
                        name="notes"
                        control={control}
                        label="備註"
                        labelClassName="mb-2 text-[14px] font-normal"
                        renderField={({ field }) => (
                            <Textarea
                                value={field.value}
                                onChange={field.onChange}
                                placeholder="請輸入"
                                maxLength={250}
                                showMaxLength
                                disabled={false}
                                className="w-full"
                            />
                        )}
                    />
                </div>
            </div>
        </div>
    );

export default SalaryFormFields;
