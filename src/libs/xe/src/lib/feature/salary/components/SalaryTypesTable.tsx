import { Button, DataTable, FormItemContent,Input, Select } from '@mayo/mayo-ui-beta/v2';
import { PlusCircle,Trash2 } from 'lucide-react';
import React, { useMemo } from 'react';
import { useFieldArray, useFormContext, useWatch } from 'react-hook-form';

export interface SalaryTypeOption {
    label: string;
    value: string;
}

export interface SalaryTypesTableProps {
    options: SalaryTypeOption[];
}

// Extracted component for total calculation
const SalaryTypesTotal: React.FC = () => {
    const salaryTypes = useWatch({ name: 'salaryTypes' }) || [];
    const total = salaryTypes.reduce((sum: number, row: any) => sum + (Number(row.amount) || 0), 0);
    return (
        <div className="mt-2 flex items-center justify-end">
            <div className="flex items-center text-[16px] font-normal">
                金額加總：<span className="align-middle text-[24px] text-[#26abe3]">{total.toLocaleString()}</span>
            </div>
        </div>
    );
};

const SalaryTypesTable: React.FC<SalaryTypesTableProps> = ({ options }) => {
    const { control } = useFormContext();
    const { fields, append, remove } = useFieldArray({ control, name: 'salaryTypes' });

    const memoizedColumns = useMemo(
        () => [
            {
                accessorKey: 'subject',
                header: '科目名稱',
                enableResizing: false,
                size: 200,
                cell: ({ row }: any) => (
                    <FormItemContent
                        control={control}
                        name={`salaryTypes.${row.index}.subject`}
                        fieldClassName="w-full"
                        label=""
                        renderField={({ field }) => (
                            <Select
                                options={options}
                                value={options.find((opt) => opt.value === field.value) || null}
                                onChange={(opt: SalaryTypeOption | null) => field.onChange(opt ? opt.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="min-w-[120px]"
                            />
                        )}
                    />
                ),
            },
            {
                accessorKey: 'amount',
                header: '金額',
                enableResizing: false,
                size: 200,
                cell: ({ row }: any) => (
                    <FormItemContent
                        control={control}
                        name={`salaryTypes.${row.index}.amount`}
                        fieldClassName="w-full"
                        label=""
                        renderField={({ field }) => (
                            <Input
                                type="number"
                                value={field.value ?? ''}
                                onChange={field.onChange}
                                className="min-w-[120px]"
                                placeholder="0"
                            />
                        )}
                    />
                ),
            },
            {
                id: 'actions',
                header: '功能',
                enableResizing: false,
                size: 48,
                cell: ({ row }: any) => (
                    <div className="flex items-center justify-around">
                        <Button variant="ghost" size="icon" onClick={() => remove(row.index)}>
                            <Trash2 className="h-4 w-4 text-gray-600" />
                        </Button>
                    </div>
                ),
            },
        ],
        [control, options, remove],
    );

    // Use only fields as the data source for stable row keys
    const tableData = fields;

    return (
        <div>
            <DataTable columns={memoizedColumns} data={tableData} showPagination={false} getRowId={(row) => row.id} />
            <div className="mt-2 flex items-center justify-between">
                <Button
                    variant="link"
                    size="sm"
                    onClick={() => append({ subject: '', amount: '' })}
                    className="text-primary flex h-full items-center gap-1"
                >
                    <span className="flex h-full items-center">
                        <PlusCircle className="mr-2 h-4 w-4 align-middle" /> 新增
                    </span>
                </Button>
            </div>
            <SalaryTypesTotal />
        </div>
    );
};

export default SalaryTypesTable;
