import { zodResolver } from '@hookform/resolvers/zod';
import {
    <PERSON><PERSON>,
    Card,
    CardContent,
    DatePicker,
    Form,
    FormControl,
    FormField,
    FormItemContent,
    MaskLoading,
    toast,
} from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { BreadcrumbItem, Breadcrumbs } from 'xe/components/breadcrumb';
import ProgressBar from 'xe/components/ProgressBar';
import { useGetHireProcessOptionsQuery, useGetHireProcessPersonalQuery } from 'xe/redux/query/hireProcessApi';
import { HireProcessPersonalResponse, UpdateHireProcessPersonalParams } from 'xe/redux/query/types';
import { injectReducers } from 'xe/redux/store';
import { z } from 'zod';

import BasicInfoForm from './basic-info-form';
import CertificatesForm from './certificates-form';
import ContactForm from './contact-form';
import EducationsForm from './educations-form';
import EmployeeInfoForm from './employee-info-form';
import ImageUploadForm from './image-upload-form';
import SeniorityInfoForm from './seniority-info-form';
import reducer from './store';
import WorkExperiencesForm from './work-experiences-form';

injectReducers({ foundationHirePersonal: reducer });

const breadcrumbItems: BreadcrumbItem[] = [
    { label: '流程專區', href: '' },
    { label: '人事異動', href: '' },
    { label: '新進人員', href: '' },
    { label: '新進人員資料', href: '', isCurrent: true },
];

const PersonalSchema = z.object({
    OnboardingDate: z.string().min(1, { message: '請選擇到職日期' }),
    OnboardingStatus: z.number().min(1, { message: '請選擇到職狀態' }),
    PersonalPictureUrl: z.string().optional(),

    BasicInfo: z.object({
        LastName: z.string().min(1, { message: '請輸入姓氏' }),
        FirstName: z.string().min(1, { message: '請輸入名字' }),
        EnglishName: z.string().optional(),
        Gender: z.string().min(1, { message: '請選擇性別' }),
        Birthday: z.string().min(1, { message: '請選擇生日' }),
        Nation: z.string().min(1, { message: '請選擇國籍' }),
        IDType: z.string().min(1, { message: '請選擇身分證件類型' }),
        IDNumber: z.string().min(1, { message: '請輸入身分證件號碼' }),
        IDExpiryDate: z.string().optional(),
        ArmyStatus: z.string().optional(),
        ArmyType: z.string().optional(),
        ArmyStart: z.string().optional(),
        ArmyEnd: z.string().optional(),
        ExemptReason: z.string().optional(),
        EntryTime: z.string().optional(),
        MaritalStatus: z.string().min(1, { message: '請選擇婚姻狀態' }),
    }),
    Contact: z.object({
        BusinessEmail: z
            .string()
            .optional()
            .or(z.string().email({ message: '請輸入正確的 Email 格式' })),
        BusinessMobileNumber: z.string().optional(),
        ContactAddress: z.string().min(1, { message: '請輸入聯絡地址' }),
        Emergency: z.string().min(1, { message: '請輸入緊急聯絡人' }),
        EmergencyLandlineNumber: z.string().optional(),
        EmergencyMobileNumber: z.string().min(1, { message: '請輸入手機號碼' }),
        EmergencyRelationship: z.string().optional(),
        ExtensionNumber: z.string().optional(),
        LandlineNumber: z.string().optional().nullable(),
        MobileNumber: z.string().min(1, { message: '請輸入手機號碼' }),
        PermanentAddress: z.string().min(1, { message: '請輸入戶籍地址' }),
        PersonalEmail: z
            .string()
            .optional()
            .or(z.string().email({ message: '請輸入正確的 Email 格式' })),
    }),
    Educations: z.array(
        z.object({
            AcademicDegree: z.string().optional(),
            IsHighestDegree: z.boolean().optional(),
            AcademyName: z.string().min(1, { message: '請輸入學校名稱' }),
            AcademicDeptCategory: z.string().min(1, { message: '請輸入科系類別' }),
            AcademicDeptName: z.string().min(1, { message: '請輸入科系名稱' }),
            EducationCategory: z.string().min(1, { message: '請輸入學歷類別' }),
            CompletionStatus: z.string().min(1, { message: '請輸入就學狀態' }),
            AttendanceRange: z.tuple([z.string(), z.string()]).optional(),
            AcademyLocation: z.string().min(1, { message: '請輸入學校所在地' }),
            SchoolingCategory: z.string().min(1, { message: '請輸入就學類別' }),
        }),
    ),
    Certificates: z.array(
        z
            .object({
                CertificateName: z.string().min(1, { message: '請輸入證照名稱' }),
                CertificateIssuingUnit: z.string().optional(),
                CertificateIssuingDate: z.string().optional(),
                IsNotExpire: z.boolean(),
                CertificateExpiryDate: z.string().optional(),
                Notes: z.string().optional(),
            })
            .refine(
                (data) => data.IsNotExpire || (!!data.CertificateExpiryDate && data.CertificateExpiryDate.length > 0),
                {
                    message: '請輸入證照有效期限',
                    path: ['CertificateExpiryDate'],
                },
            ),
    ),
    WorkExperiences: z.array(
        z.object({
            CompanyName: z.string().min(1, { message: '請輸入公司名稱' }),
            IndustryCategory: z.string().min(1, { message: '請輸入產業類別' }),
            IndustrySubCategory: z.string().optional(),
            JobCategory: z.string().min(1, { message: '請輸入職務類別' }),
            DepartmentName: z.string().optional(),
            JobTitle: z.string().min(1, { message: '請輸入職務名稱' }),
            DirectReport: z.string().optional(),
            EmploymentRange: z.tuple([z.string(), z.string()]).optional(),
            JobDuties: z.string().optional(),
            SeperationReason: z.string().optional(),
        }),
    ),
    EmployeeInfo: z.object({
        DepartmentId: z.string().min(1, { message: '請輸入所屬單位' }),
        DeptPositionPositionNumber: z.string().min(1, { message: '請輸入職務編號' }),
        WorkCountryId: z.string().min(1, { message: '請輸入工作地區' }),
        WorkCountyId: z.string().optional(),
        DeptFactoryId: z.string().optional().nullable(),
        EmployeeGroupId: z.string().min(1, { message: '請輸入身份類別' }),
        EmployeeSubGroupId: z.string().optional(),
        JobFamilyId: z.string().optional(),
        JobGradeId: z.string().optional(),
        JobGroupId: z.string().optional(),
        JobLevelId: z.string().optional(),
        JobStreamId: z.string().optional(),
        JobTitleId: z.string().optional(),
        BusinessTitleId: z.string().optional(),
        CorporationId: z.string().optional(),
    }),
    SeniorityInfo: z.object({
        CareerRecognizedSeniority: z.string().optional(),
        CareerSeniority: z.string().optional(),
        ReHireRecognizedJobGradeSeniority: z.string().optional(),
        ReHireRecognizedSeniority: z.string().optional(),
        ActionCategoryCode: z.string().min(1, { message: '請輸入動作類別' }),
    }),
    Base64: z.string().optional(),
});

const FoundationHirePersonal = ({ personalId }: { personalId: string }) => {
    const methods = useForm({
        resolver: zodResolver(PersonalSchema),
        defaultValues: {
            OnboardingDate: dayjs().format('YYYY-MM-DD'),
            OnboardingStatus: 30,
            PersonalPictureUrl: '' as string | undefined,
            BasicInfo: {
                LastName: '',
                FirstName: '',
                EnglishName: '',
                Gender: '',
                Birthday: '',
                Nation: '',
                IDType: '',
                IDNumber: '',
                IDExpiryDate: '',
                ArmyStatus: '',
                ArmyType: '',
                ArmyStart: '',
                ArmyEnd: '',
                ExemptReason: '',
                EntryTime: '',
                MaritalStatus: '',
            },
            Contact: {
                BusinessEmail: '',
                BusinessMobileNumber: '',
                ContactAddress: '',
                Emergency: '',
                EmergencyLandlineNumber: '',
                EmergencyMobileNumber: '',
                EmergencyRelationship: '',
                ExtensionNumber: '',
                LandlineNumber: null,
                MobileNumber: '',
                PermanentAddress: '',
                PersonalEmail: '',
            },
            Educations: [
                {
                    AcademicDegree: '',
                    IsHighestDegree: false,
                    AcademyName: '',
                    AcademicDeptCategory: '',
                    AcademicDeptName: '',
                    EducationCategory: '',
                    CompletionStatus: '',
                    AttendanceRange: ['', ''],
                    AcademyLocation: '',
                    SchoolingCategory: '',
                },
            ],
            Certificates: [
                {
                    CertificateName: '',
                    CertificateIssuingUnit: '',
                    CertificateIssuingDate: '',
                    CertificateExpiryDate: '',
                    IsNotExpire: false,
                    Notes: '',
                },
            ],
            WorkExperiences: [
                {
                    CompanyName: '',
                    IndustryCategory: '',
                    IndustrySubCategory: '',
                    JobCategory: '',
                    DepartmentName: '',
                    JobTitle: '',
                    DirectReport: '',
                    EmploymentRange: ['', ''],
                    JobDuties: '',
                    SeperationReason: '',
                },
            ],
            EmployeeInfo: {
                DepartmentId: '',
                DeptPositionPositionNumber: '',
                WorkCountryId: '',
                WorkCountyId: '',
                DeptFactoryId: null,
                EmployeeGroupId: '',
                EmployeeSubGroupId: '',
                JobFamilyId: '',
                JobGradeId: '',
                JobGroupId: '',
                JobLevelId: '',
                JobStreamId: '',
                JobTitleId: '',
                BusinessTitleId: '',
                CorporationId: '',
            },
            SeniorityInfo: {
                CareerRecognizedSeniority: '0',
                CareerSeniority: '0',
                ReHireRecognizedJobGradeSeniority: '0',
                ReHireRecognizedSeniority: '0',
                ActionCategoryCode: '',
            },
        },
    });

    const { control, setValue, watch, handleSubmit } = methods;

    const values = watch();

    const { data } = useGetHireProcessOptionsQuery({
        onboardingDate: values.OnboardingDate,
    });

    const hireProcessOptions = data?.data;

    const handleInitPersonalData = (personalData: HireProcessPersonalResponse['data']) => {
        // 基本欄位
        setValue(
            'OnboardingDate',
            personalData.onboardingDate ? dayjs(personalData.onboardingDate).format('YYYY-MM-DD') : '',
        );
        setValue('OnboardingStatus', personalData.onboardingStatus);
        setValue('PersonalPictureUrl', personalData.personalPictureUrl || '');
        setValue('BasicInfo.LastName', personalData.basicInfo.lastName || '');
        setValue('BasicInfo.FirstName', personalData.basicInfo.firstName || '');
        setValue('BasicInfo.EnglishName', personalData.basicInfo.englishName || '');
        // ... 其餘 basicInfo 欄位
        setValue('BasicInfo.Gender', personalData.basicInfo.gender || '');
        setValue(
            'BasicInfo.Birthday',
            personalData.basicInfo.birthday ? dayjs(personalData.basicInfo.birthday).format('YYYY-MM-DD') : '',
        );
        setValue('BasicInfo.Nation', personalData.basicInfo.nation || '');
        setValue('BasicInfo.IDType', personalData.basicInfo.idType ? personalData.basicInfo.idType.toString() : '');
        setValue('BasicInfo.IDNumber', personalData.basicInfo.idNumber || '');
        setValue(
            'BasicInfo.IDExpiryDate',
            personalData.basicInfo.idExpiryDate ? dayjs(personalData.basicInfo.idExpiryDate).format('YYYY-MM-DD') : '',
        );
        setValue('BasicInfo.ArmyStatus', personalData.basicInfo.armyStatus || '');
        setValue('BasicInfo.ArmyType', personalData.basicInfo.armyType || '');
        setValue(
            'BasicInfo.ArmyStart',
            personalData.basicInfo.armyStart ? dayjs(personalData.basicInfo.armyStart).format('YYYY-MM-DD') : '',
        );
        setValue(
            'BasicInfo.ArmyEnd',
            personalData.basicInfo.armyEnd ? dayjs(personalData.basicInfo.armyEnd).format('YYYY-MM-DD') : '',
        );
        setValue('BasicInfo.ExemptReason', personalData.basicInfo.exemptReason || '');
        setValue(
            'BasicInfo.EntryTime',
            personalData.basicInfo.entryTime ? dayjs(personalData.basicInfo.entryTime).format('YYYY-MM-DD') : '',
        );
        setValue('BasicInfo.MaritalStatus', personalData.basicInfo.maritalStatus || '');

        // 通訊資料
        setValue('Contact.BusinessEmail', personalData.hireContact.businessEmail || '');
        setValue('Contact.BusinessMobileNumber', personalData.hireContact.businessMobileNumber || '');
        setValue('Contact.ContactAddress', personalData.hireContact.contactAddress || '');
        setValue('Contact.Emergency', personalData.hireContact.emergency || '');
        setValue('Contact.EmergencyLandlineNumber', personalData.hireContact.emergencyLandlineNumber || '');
        setValue('Contact.EmergencyMobileNumber', personalData.hireContact.emergencyMobileNumber || '');
        setValue('Contact.EmergencyRelationship', personalData.hireContact.emergencyRelationship || '');
        setValue('Contact.ExtensionNumber', personalData.hireContact.extensionNumber || '');
        setValue('Contact.LandlineNumber', personalData.hireContact.landlineNumber || null);
        setValue('Contact.MobileNumber', personalData.hireContact.mobileNumber || '');
        setValue('Contact.PermanentAddress', personalData.hireContact.permanentAddress || '');
        setValue('Contact.PersonalEmail', personalData.hireContact.personalEmail || '');

        // 學歷資料
        const educations = (personalData.educationDatas || []).map((edu) => ({
            ...edu,
            AttendanceRange: [
                edu.attendanceStart ? dayjs(edu.attendanceStart).format('YYYY-MM-DD') : '',
                edu.attendanceEnd ? dayjs(edu.attendanceEnd).format('YYYY-MM-DD') : '',
            ] as [string, string],
        }));

        // Transform educations to match expected format
        const formattedEducations = educations.map((edu) => ({
            AcademyName: edu.academyName || '',
            AcademicDeptCategory: edu.academicDeptCategory || '',
            AcademicDeptName: edu.academicDeptName || '',
            EducationCategory: edu.educationCategory || '',
            CompletionStatus: edu.completionStatus || '',
            AcademyLocation: edu.academyLocation || '',
            SchoolingCategory: edu.academicDeptCategory || '', // Using academicDeptCategory as fallback since schoolingCategory doesn't exist
            AcademicDegree: edu.academicDegree || '',
            IsHighestDegree: edu.isHighestDegree || false,
            AttendanceRange: edu.AttendanceRange as [string, string],
        }));
        setValue('Educations', formattedEducations);

        // 證照資料
        // Transform certificates to match expected format
        const formattedCertificates = (personalData.certificaties || []).map((cert) => ({
            CertificateName: cert.certificateName || '',
            IsNotExpire: !cert.certificateExpiryDate,
            CertificateIssuingUnit: cert.certificateIssuingUnit || '',
            CertificateIssuingDate: cert.certificateIssuingDate || '',
            CertificateExpiryDate: cert.certificateExpiryDate || '',
            Notes: cert.notes || '',
        }));
        setValue('Certificates', formattedCertificates);

        // 工作經歷
        const workExperiences = (personalData.workExperience || []).map((exp) => ({
            CompanyName: exp.companyName || '',
            IndustryCategory: exp.industryCategory || '',
            JobCategory: exp.jobCategory || '',
            DepartmentName: exp.departmentName || '',
            JobTitle: exp.jobTitle || '',
            DirectReport: exp.directReport || '',
            JobDuties: exp.jobDuties || '',
            SeperationReason: exp.seperationReason || '',
            IndustrySubCategory: exp.industryName || '',
            EmploymentRange: [
                exp.employmentStart ? dayjs(exp.employmentStart).format('YYYY-MM-DD') : '',
                exp.employmentEnd ? dayjs(exp.employmentEnd).format('YYYY-MM-DD') : '',
            ] as [string, string],
        }));
        setValue('WorkExperiences', workExperiences);

        // 職位資料
        const employeeInfo = personalData.employeeInfo || {};
        setValue('EmployeeInfo', {
            DepartmentId: employeeInfo.departmentId || '',
            DeptPositionPositionNumber: employeeInfo.deptPositionPositionNumber || '',
            WorkCountryId: employeeInfo.workCountryId || '',
            WorkCountyId: employeeInfo.workCountyId || '',
            DeptFactoryId: employeeInfo.deptFactoryId || null,
            EmployeeGroupId: employeeInfo.employeeGroupId || '',
            EmployeeSubGroupId: employeeInfo.employeeSubGroupId || '',
            JobFamilyId: employeeInfo.jobFamilyId || '',
            JobGradeId: employeeInfo.jobGradeId || '',
            JobGroupId: employeeInfo.jobGroupId || '',
            JobLevelId: employeeInfo.jobLevelId || '',
            JobStreamId: employeeInfo.jobStreamId || '',
            JobTitleId: employeeInfo.jobTitleId || '',
            BusinessTitleId: employeeInfo.businessTitleId || '',
            CorporationId: employeeInfo.corporationId || '',
        });

        // 年資資料
        const seniorityInfo = personalData.seniorityInfo || {};
        setValue('SeniorityInfo', {
            ActionCategoryCode: seniorityInfo.actionCategoryCode || '',
            CareerRecognizedSeniority: seniorityInfo.careerRecognizedSeniority?.toString() || '',
            CareerSeniority: seniorityInfo.careerSeniority?.toString() || '',
            ReHireRecognizedJobGradeSeniority: seniorityInfo.reHireRecognizedJobGradeSeniority?.toString() || '',
            ReHireRecognizedSeniority: seniorityInfo.reHireRecognizedSeniority?.toString() || '',
        });
    };

    const { data: personalData, isFetching: isGetHireProcessPersonalFetching } = useGetHireProcessPersonalQuery(
        personalId,
        { skip: !personalId },
    );

    const onSubmit = async (values: any) => {
        try {
            // 將 AttendanceRange 拆成 AttendanceStart/AttendanceEnd，僅於送出物件中存在
            const educations = values.Educations.map((edu: any) => {
                const [AttendanceStart, AttendanceEnd] = edu.AttendanceRange || ['', ''];
                const { AttendanceRange: _AttendanceRange, ...rest } = edu;
                return {
                    ...rest,
                    AttendanceStart,
                    AttendanceEnd,
                };
            });
            // 將 EmploymentRange 拆成 EmploymentStart/EmploymentEnd
            const workExperiences = values.WorkExperiences.map((exp: any) => {
                const [EmploymentStart, EmploymentEnd] = exp.EmploymentRange || ['', ''];
                const { EmploymentRange: _EmploymentRange, ...rest } = exp;
                return {
                    ...rest,
                    EmploymentStart,
                    EmploymentEnd,
                };
            });

            const payload: UpdateHireProcessPersonalParams = {
                ...values,
                IndustryCategory: values.IndustrySubCategory || null, // 產業類別直接使用產業子類別 ex: 產業類別 C, 子類別 C11, 直接帶 C11
                Educations: educations,
                WorkExperiences: workExperiences,
                base64: values.Base64, // 確保 base64 資料被包含在 payload 中
            };

            console.log(payload);

            // 更新個人資料資訊
            // await updateHireProcessPersonal(payload);
            toast.success('更新個人資料資訊成功');
        } catch (err) {
            console.log(err);
        }
    };

    const resetFormByOnboardingDate = () => {
        setValue('EmployeeInfo', {
            DepartmentId: '',
            DeptPositionPositionNumber: '',
            WorkCountryId: '',
            WorkCountyId: '',
            DeptFactoryId: null,
            EmployeeGroupId: '',
            EmployeeSubGroupId: '',
            JobFamilyId: '',
            JobGradeId: '',
            JobGroupId: '',
            JobLevelId: '',
            JobStreamId: '',
            JobTitleId: '',
            BusinessTitleId: '',
            CorporationId: '',
        });
        setValue('BasicInfo.Nation', '');
        setValue('BasicInfo.Gender', '');
        // setValue('BasicInfo.IDType', '');
        setValue('BasicInfo.MaritalStatus', '');
        // 清空所有學歷
        setValue(
            'Educations',
            values.Educations.map((edu: any) => ({
                ...edu,
                EducationCategory: '',
                AcademicDeptCategory: '',
                CompletionStatus: '',
                // 其他有 options 的欄位
            })),
        );
        // 清空所有工作經歷
        setValue(
            'WorkExperiences',
            values.WorkExperiences.map((exp: any) => ({
                ...exp,
                IndustryCategory: '',
                JobCategory: '',
                // 其他有 options 的欄位
            })),
        );
    };

    useEffect(() => {
        if (!isGetHireProcessPersonalFetching && personalData) {
            handleInitPersonalData(personalData.data);
        }
    }, [isGetHireProcessPersonalFetching]);

    return (
        <div className="flex flex-col gap-4">
            {isGetHireProcessPersonalFetching && <MaskLoading />}

            <Breadcrumbs items={breadcrumbItems} />
            <FormProvider {...methods}>
                <Form {...methods}>
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <div className="flex flex-col gap-4">
                            <h1 className="text-xl font-bold">新進人員資料</h1>
                            <ProgressBar onboardingId={personalId} onSave={() => null} />
                            {/* 步驟條 */}

                            {/* <Card className="scrollbar-none h-24 w-full overflow-scroll">
                                <CardContent className="">
                                    <div className="flex items-center justify-between">
                                        <div className="flex h-14 w-44 flex-col justify-between">
                                            <div className="flex items-center gap-2">
                                                <span className="text-primary h-8 w-3 text-2xl">1</span>

                                                <span className="text-dark-900 h-[22px] w-[110px] text-lg">
                                                    個人資料
                                                </span>
                                            </div>
                                            <Progress value={100} />
                                        </div>

                                        <div className="mx-8 h-14 w-1 bg-gray-50" />

                                        <div className="flex gap-4">
                                            <div className="flex h-14 w-44 flex-col justify-between">
                                                <div className="flex items-center gap-2">
                                                    <span className="text-primary h-8 w-3 text-2xl">2</span>

                                                    <span className="text-dark-900 h-[22px] w-[110px] text-lg">
                                                        出缺勤
                                                    </span>
                                                </div>
                                                <Progress value={100} />
                                            </div>
                                            <div className="flex h-14 w-44 flex-col justify-between">
                                                <div className="flex h-8 items-center gap-2">
                                                    <span className="text-primary w-3 text-2xl" />

                                                    <span className="text-dark-900 h-[22px] w-[110px] text-lg">
                                                        薪資設定
                                                    </span>
                                                </div>
                                                <Progress value={100} className="bg-gray-50" />
                                            </div>
                                            <div className="flex h-14 w-44 flex-col justify-between">
                                                <div className="flex h-8 items-center gap-2">
                                                    <span className="text-primary w-3 text-2xl" />

                                                    <span className="text-dark-900 h-[22px] w-[110px] text-lg">
                                                        稅與保險
                                                    </span>
                                                </div>
                                                <Progress value={100} />
                                            </div>
                                        </div>
                                        <div className="mx-8 h-14 w-1 bg-gray-50" />

                                        <div className="flex h-14 w-44 flex-col justify-between">
                                            <div className="flex items-center gap-2">
                                                <span className="text-primary h-8 w-3 text-2xl">3</span>

                                                <span className="text-dark-900 h-[22px] w-[110px] text-lg">檢視</span>
                                            </div>
                                            <Progress value={100} />
                                        </div>

                                        <Button className="w-20" color="primary" type="submit">
                                            <div className="w-full text-center">暫存</div>
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card> */}
                            {/* 內容卡片 */}
                            <Card className="scrollbar-none h-full w-full overflow-scroll px-10 py-12">
                                <CardContent className="flex flex-col gap-8">
                                    {/* 上傳照片區塊 */}
                                    <ImageUploadForm />

                                    {/* 到職日 */}
                                    <div>
                                        <div className="grid grid-cols-4 gap-6">
                                            {/* // 到職日 */}

                                            <FormItemContent
                                                required={false}
                                                name="OnboardingDate"
                                                label="到職日"
                                                control={control}
                                                fieldClassName="w-full"
                                                itemComponent={
                                                    <FormField
                                                        control={control}
                                                        name="OnboardingDate"
                                                        render={({ field }) => (
                                                            <FormControl>
                                                                <DatePicker
                                                                    className="w-full"
                                                                    value={field.value || ''}
                                                                    onChange={(date) => {
                                                                        field.onChange(date);
                                                                        resetFormByOnboardingDate();
                                                                    }}
                                                                />
                                                            </FormControl>
                                                        )}
                                                    />
                                                }
                                            />

                                            <div className="col-span-3 flex items-end">
                                                <Alert
                                                    alertDesc="到職日變更後，部分設定可能需要重新調整。"
                                                    alertIcon={null}
                                                    showActionButton={false}
                                                    variant="warning"
                                                    className="mt-2 w-full max-w-full"
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    {/*  基本資料 */}
                                    <BasicInfoForm hireProcessOptions={hireProcessOptions} />

                                    {/* 通訊資料 */}
                                    <ContactForm personalId={personalId} />

                                    {/* 職位資料 */}
                                    <EmployeeInfoForm hireProcessOptions={hireProcessOptions} />

                                    {/* 到職前年資 */}
                                    <SeniorityInfoForm />

                                    {/* 工作經歷 */}
                                    <WorkExperiencesForm hireProcessOptions={hireProcessOptions} />

                                    {/* 學歷資料 */}
                                    <EducationsForm hireProcessOptions={hireProcessOptions} />

                                    {/* 證照資料 */}
                                    <CertificatesForm />
                                </CardContent>
                            </Card>
                        </div>
                    </form>
                </Form>
            </FormProvider>
        </div>
    );
};

export default FoundationHirePersonal;
