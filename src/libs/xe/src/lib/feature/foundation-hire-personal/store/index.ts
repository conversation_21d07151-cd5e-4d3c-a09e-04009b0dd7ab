import { combineReducers } from '@reduxjs/toolkit';
import type { TypedUseSelectorHook } from 'react-redux';
import { useSelector } from 'react-redux';
import { RootState } from 'xe/redux/store';

import reducers, { FoundationHirePersonalState,SLICE_NAME } from './slice';

const reducer = combineReducers({
    data: reducers,
});

export const useAppSelector: TypedUseSelectorHook<
    RootState & {
        [SLICE_NAME]: {
            data: FoundationHirePersonalState;
        };
    }
> = useSelector;

export * from './slice';
export { useAppDispatch } from 'xe/redux/store';
export default reducer;
