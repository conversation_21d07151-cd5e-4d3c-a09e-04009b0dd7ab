import { createSlice } from '@reduxjs/toolkit';

export type FoundationHirePersonalState = {
    //
};

export const SLICE_NAME = 'foundationHirePersonal';

const initialState: FoundationHirePersonalState = {
    //
};
const foundationHirePersonalSlice = createSlice({
    name: `${SLICE_NAME}/state`,
    initialState,
    reducers: {
        //
    },
});

export default foundationHirePersonalSlice.reducer;
