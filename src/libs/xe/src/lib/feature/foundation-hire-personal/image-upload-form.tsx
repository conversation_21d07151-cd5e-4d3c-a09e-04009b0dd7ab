import { Card, CardContent, FormControl, FormField, FormItemContent, Input, toast } from '@mayo/mayo-ui-beta/v2';
import { User } from 'lucide-react';
import React, { useRef, useState } from 'react';
import { useFormContext } from 'react-hook-form';

const ImageUploadForm = () => {
    const { control, setValue } = useFormContext();
    const [previewUrl, setPreviewUrl] = useState<string | null>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleImageClick = () => {
        fileInputRef.current?.click();
    };

    const handleImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            // 檢查檔案大小（300KB）
            if (file.size > 300 * 1024) {
                toast.error('檔案大小不能超過300KB');
                return;
            }

            // 檢查檔案類型
            const validTypes = ['image/jpeg', 'image/jpg', 'image/gif', 'image/png'];
            if (!validTypes.includes(file.type)) {
                toast.error('只接受JPG、JPEG、GIF、PNG格式的圖片');
                return;
            }

            // 建立預覽URL
            const reader = new FileReader();
            reader.onloadend = () => {
                const base64String = reader.result as string;
                setPreviewUrl(base64String);
                // 設置 base64 到表單中
                setValue('Base64', base64String);
                setValue('PersonalPictureUrl', base64String);
            };
            reader.readAsDataURL(file);
        }
    };

    return (
        <Card className="w-full rounded-2xl border-0 bg-gray-50 px-10 py-8">
            <CardContent className="flex items-center justify-between">
                {/* 上傳照片區塊 */}
                <div className="flex min-w-[400px] items-center gap-6">
                    <div className="flex-shrink-0">
                        <div
                            className="flex h-32 w-32 cursor-pointer items-center justify-center overflow-hidden rounded-full bg-gray-100"
                            onClick={handleImageClick}
                        >
                            {previewUrl ? (
                                <img src={previewUrl} alt="個人照片" className="h-full w-full object-cover" />
                            ) : (
                                <User className="text-white" size={90} />
                            )}
                        </div>
                        <input
                            type="file"
                            ref={fileInputRef}
                            onChange={handleImageChange}
                            accept="image/jpeg,image/jpg,image/gif,image/png"
                            className="hidden"
                        />
                    </div>
                    <div className="flex flex-col justify-center">
                        <button
                            type="button"
                            onClick={handleImageClick}
                            className="mb-2 text-base font-bold text-sky-500"
                        >
                            上傳個人照片
                        </button>
                        <div className="text-muted-foreground text-sm leading-6">
                            *照片格式限JPG、JPEG、GIF、PNG
                            <br />
                            *大小限制為300 KB以內
                        </div>
                    </div>
                </div>
                {/* 輸入欄位 */}
                <div className="flex flex-1 justify-end gap-8">
                    <div className="max-w-xs flex-1">
                        <FormItemContent
                            name="BasicInfo.LastName"
                            label="姓"
                            required
                            control={control}
                            fieldClassName="w-full"
                            itemComponent={
                                <FormField
                                    control={control}
                                    name="BasicInfo.LastName"
                                    render={({ field }) => (
                                        <FormControl>
                                            <Input
                                                placeholder="請輸入"
                                                value={field.value}
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                    field.onChange(e.target.value)
                                                }
                                            />
                                        </FormControl>
                                    )}
                                />
                            }
                        />
                    </div>
                    <div className="max-w-xs flex-1">
                        <FormItemContent
                            name="BasicInfo.FirstName"
                            label="名"
                            required
                            control={control}
                            fieldClassName="w-full"
                            itemComponent={
                                <FormField
                                    control={control}
                                    name="BasicInfo.FirstName"
                                    render={({ field }) => (
                                        <FormControl>
                                            <Input
                                                placeholder="請輸入"
                                                value={field.value}
                                                onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                    field.onChange(e.target.value)
                                                }
                                            />
                                        </FormControl>
                                    )}
                                />
                            }
                        />
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default ImageUploadForm;
