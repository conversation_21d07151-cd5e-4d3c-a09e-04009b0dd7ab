import {
    Checkbox,
    DateRangePicker,
    FormControl,
    FormField,
    FormItemContent,
    Input,
    Select,
} from '@mayo/mayo-ui-beta/v2';
import { find, get } from 'lodash';
import { CirclePlus, Trash2 } from 'lucide-react';
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { HireProcessOptionsResponse } from '../../redux/query/types';

const EducationsForm = ({
    hireProcessOptions,
}: {
    hireProcessOptions: HireProcessOptionsResponse['data'] | undefined;
}) => {
    const { control, getValues, setValue } = useFormContext();

    const baseSettingOptionsMap = hireProcessOptions?.baseSettingOptionsMap;

    const {
        fields: educationFields,
        append: appendEducation,
        remove: removeEducation,
    } = useFieldArray({
        control,
        name: 'Educations',
    });

    const educationCategoryOptions = get(baseSettingOptionsMap, 'EducationCategory', []);

    const schoolingStatusOptions = get(baseSettingOptionsMap, 'SchoolingStatus', []);

    // 科系類別 DepartmentCategory
    const departmentCategoryOptions = get(baseSettingOptionsMap, 'DepartmentCategory', []);

    // 就學類別 SchoolingCategory
    const schoolingCategoryOptions = get(baseSettingOptionsMap, 'SchoolingCategory', []);

    return (
        <div>
            <div className="mb-4 mt-10 text-lg font-bold">學歷資料</div>
            <div className="mb-6 border-b border-gray-200" />
            <div className="flex flex-col gap-6">
                {educationFields.map((field, index) => (
                    <div key={field.id} className="flex gap-4">
                        <div className="border-primary flex-1 rounded-md border-l-4 bg-white py-6 pl-6">
                            <div className="mb-4 flex items-center">
                                <span className="text-primary-lighter mr-2 text-base font-bold">學歷 {index + 1}</span>
                                <Trash2
                                    className="size-4 cursor-pointer text-red-500"
                                    onClick={() => removeEducation(index)}
                                />
                            </div>
                            <div className="mb-4 grid grid-cols-4 gap-6">
                                <FormItemContent
                                    required={false}
                                    name={`Educations.${index}.EducationCategory`}
                                    label={
                                        <div className="flex h-6 items-center justify-between gap-2">
                                            <div className="flex items-center">
                                                學歷類別
                                                <span className="text-red-600">*</span>
                                            </div>
                                            <FormField
                                                control={control}
                                                name={`Educations.${index}.IsHighestDegree`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Checkbox
                                                            label="最高學歷"
                                                            checked={field.value}
                                                            onValueChange={(checked: boolean) => {
                                                                const educations = [...getValues('Educations')];
                                                                // 只允許一個最高學歷
                                                                educations.forEach((edu, i) => {
                                                                    edu.IsHighestDegree = i === index ? checked : false;
                                                                });
                                                                setValue('Educations', educations);
                                                            }}
                                                            className="mr-2 whitespace-nowrap"
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        </div>
                                    }
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.EducationCategory`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Select
                                                        placeholder="請選擇"
                                                        value={
                                                            find(educationCategoryOptions, {
                                                                value: field?.value,
                                                            }) || null
                                                        }
                                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                                        options={educationCategoryOptions}
                                                        formatOptionLabel={(option: any) => option.text}
                                                        isPortal
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    name={`Educations.${index}.AcademyName`}
                                    label="學校名稱"
                                    required
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.AcademyName`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Input
                                                        placeholder="請輸入"
                                                        value={field.value}
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            field.onChange(e.target.value)
                                                        }
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    name={`Educations.${index}.AcademicDeptCategory`}
                                    label="科系類別"
                                    required
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.AcademicDeptCategory`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Select
                                                        placeholder="請選擇"
                                                        value={
                                                            find(departmentCategoryOptions, {
                                                                value: field?.value,
                                                            }) || null
                                                        }
                                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                                        options={departmentCategoryOptions}
                                                        formatOptionLabel={(option: any) => option.text}
                                                        isPortal
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    name={`Educations.${index}.AcademicDeptName`}
                                    label="科系名稱"
                                    required
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.AcademicDeptName`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Input
                                                        placeholder="請輸入"
                                                        value={field.value}
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            field.onChange(e.target.value)
                                                        }
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    name={`Educations.${index}.SchoolingCategory`}
                                    label="就學類別"
                                    required
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.SchoolingCategory`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Select
                                                        placeholder="請選擇"
                                                        value={
                                                            find(schoolingCategoryOptions, {
                                                                value: field?.value,
                                                            }) || null
                                                        }
                                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                                        options={schoolingCategoryOptions}
                                                        formatOptionLabel={(option: any) => option.text}
                                                        isPortal
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    name={`Educations.${index}.AttendanceRange`}
                                    label="就學期間"
                                    required
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.AttendanceRange`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <DateRangePicker
                                                        value={field.value}
                                                        onChange={field.onChange}
                                                        placeholder={['YYYY/MM/DD', 'YYYY/MM/DD']}
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    name={`Educations.${index}.AcademyLocation`}
                                    label="學校所在地"
                                    required
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.AcademyLocation`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Input
                                                        placeholder="請輸入"
                                                        value={field.value}
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            field.onChange(e.target.value)
                                                        }
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    name={`Educations.${index}.CompletionStatus`}
                                    label="就學狀態"
                                    required
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Educations.${index}.CompletionStatus`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Select
                                                        placeholder="請選擇"
                                                        value={
                                                            find(schoolingStatusOptions, {
                                                                value: field?.value,
                                                            }) || null
                                                        }
                                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                                        options={schoolingStatusOptions}
                                                        formatOptionLabel={(option: any) => option.text}
                                                        isPortal
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />
                            </div>
                        </div>
                    </div>
                ))}
                <button
                    type="button"
                    className="text-primary-lighter mt-2 flex items-center text-base font-bold"
                    onClick={() =>
                        appendEducation({
                            AcademicDegree: '',
                            IsHighestDegree: false,
                            AcademyName: '',
                            AcademicDeptCategory: '',
                            AcademicDeptName: '',
                            EducationCategory: '',
                            CompletionStatus: '',
                            AttendanceRange: ['', ''],
                            AcademyLocation: '',
                            SchoolingCategory: '',
                        })
                    }
                >
                    <CirclePlus className="mr-2 size-4" />
                    新增
                </button>
            </div>
        </div>
    );
};

export default EducationsForm;
