import {
    DatePicker,
    DateRangePicker,
    FormControl,
    FormField,
    FormItemContent,
    Input,
    Select,
} from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import { find, get } from 'lodash';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { useValidateIdNumberMutation } from '../../redux/query/hireProcessApi';
import { HireProcessOptionsResponse } from '../../redux/query/types';
import PersonalFormField from './components/personal-form-field';

const BasicInfoForm = ({
    hireProcessOptions,
}: {
    hireProcessOptions: HireProcessOptionsResponse['data'] | undefined;
}) => {
    const { watch, setValue, clearErrors, setError, control } = useFormContext();
    const values = watch();

    const baseSettingOptionsMap = hireProcessOptions?.baseSettingOptionsMap;

    // 國籍
    const nationalityOptions = get(baseSettingOptionsMap, 'Nationality', []);

    // 身分証類型
    const idTypeOptions = get(baseSettingOptionsMap, 'IDType', []);

    // 性別
    const sexOptions = get(baseSettingOptionsMap, 'Sex', []);

    // 兵役狀態 MilitaryService
    const militaryServiceOptions = get(baseSettingOptionsMap, 'MilitaryService', []);

    // 役別 ArmsType
    const armsOptions = get(baseSettingOptionsMap, 'Arms', []);

    // 婚姻狀態 MaritalStatus
    const maritalStatusOptions = get(baseSettingOptionsMap, 'MaritalStatus', []);

    const [validateIdNumber, { isLoading: isValidateIdNumberLoading }] = useValidateIdNumberMutation();

    const handleValidateIdNumber = async () => {
        if (!values.BasicInfo.IDNumber || !values.BasicInfo.IDType) {
            return;
        }

        const payload = {
            idType: values.BasicInfo.IDType,
            idNumber: values.BasicInfo.IDNumber,
            onboardingDate: dayjs(values.OnboardingDate).format('YYYY-MM-DD'),
        };
        try {
            clearErrors('BasicInfo.IDNumber');
            await validateIdNumber(payload).unwrap();
        } catch (error: any) {
            const errorMessage = error?.data?.errorMessage || '身分證號碼驗證失敗';
            setError('BasicInfo.IDNumber', { message: errorMessage });
        }
    };

    return (
        <div>
            <div>
                <div className="mb-4 text-lg font-bold">個人資料</div>
                <div className="mb-6 border-b border-gray-200" />
                <div className="grid grid-cols-4 gap-6">
                    <PersonalFormField
                        fieldCode="0040"
                        control={control}
                        name="BasicInfo.EnglishName"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.EnglishName"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(e.target.value)
                                            }
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />

                    <FormItemContent
                        name="BasicInfo.Gender"
                        label="性別"
                        required
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.Gender"
                                render={({ field }) => (
                                    <FormControl>
                                        <Select
                                            placeholder="請選擇"
                                            value={find(sexOptions, { value: field?.value }) || null}
                                            onChange={(option: any) => field.onChange(option?.value || '')}
                                            options={sexOptions}
                                            formatOptionLabel={(option: any) => option.text}
                                            isPortal
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />

                    <FormItemContent
                        name="BasicInfo.Birthday"
                        label="生日"
                        required
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.Birthday"
                                render={({ field }) => (
                                    <FormControl>
                                        <DatePicker
                                            className="w-full"
                                            value={field.value || ''}
                                            onChange={(date) => field.onChange(date)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />

                    <FormItemContent
                        name="BasicInfo.Nation"
                        label="國籍"
                        required
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.Nation"
                                render={({ field }) => (
                                    <FormControl>
                                        <Select
                                            placeholder="請選擇"
                                            value={
                                                find(nationalityOptions, {
                                                    value: field?.value,
                                                }) || null
                                            }
                                            onChange={(option: any) => field.onChange(option?.value || '')}
                                            options={nationalityOptions}
                                            formatOptionLabel={(option: any) => option.text}
                                            isPortal
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />

                    {/* 兵役狀態相關欄位（僅男性且國籍為 TW 顯示） */}
                    {values.BasicInfo.Gender === '1' && values.BasicInfo.Nation === 'TW' && (
                        <>
                            <PersonalFormField
                                fieldCode="0060"
                                control={control}
                                name="BasicInfo.ArmyStatus"
                                itemComponent={
                                    <FormField
                                        control={control}
                                        name="BasicInfo.ArmyStatus"
                                        render={({ field }) => (
                                            <FormControl>
                                                <Select
                                                    placeholder="請選擇"
                                                    value={
                                                        find(militaryServiceOptions, {
                                                            value: field?.value,
                                                        }) || null
                                                    }
                                                    onChange={(option: any) => field.onChange(option?.value || '')}
                                                    options={militaryServiceOptions}
                                                    formatOptionLabel={(option: any) => option.text}
                                                    isPortal
                                                />
                                            </FormControl>
                                        )}
                                    />
                                }
                            />
                            {/* 役畢/服役中顯示役別與兵役期間 */}
                            {(values.BasicInfo.ArmyStatus === '1' || values.BasicInfo.ArmyStatus === '5') && (
                                <>
                                    <PersonalFormField
                                        fieldCode="0070"
                                        control={control}
                                        name="BasicInfo.ArmyType"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name="BasicInfo.ArmyType"
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Select
                                                            placeholder="請選擇"
                                                            value={
                                                                find(armsOptions, {
                                                                    value: field?.value,
                                                                }) || null
                                                            }
                                                            onChange={(option: any) =>
                                                                field.onChange(option?.value || '')
                                                            }
                                                            options={armsOptions}
                                                            formatOptionLabel={(option: any) => option.text}
                                                            isPortal
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />
                                    <PersonalFormField
                                        fieldCode="0080"
                                        control={control}
                                        name="BasicInfo.ArmyStart"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name="BasicInfo.ArmyStart"
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <DateRangePicker
                                                            value={field.value}
                                                            onChange={field.onChange}
                                                            placeholder={['YYYY/MM/DD', 'YYYY/MM/DD']}
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />
                                </>
                            )}

                            {/* 免役顯示免役原因 */}
                            {values.BasicInfo.ArmyStatus === '2' && (
                                <PersonalFormField
                                    fieldCode="0090"
                                    control={control}
                                    name="BasicInfo.ExemptReason"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name="BasicInfo.ExemptReason"
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Input
                                                        placeholder="請輸入"
                                                        value={field.value}
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            field.onChange(e.target.value)
                                                        }
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />
                            )}
                        </>
                    )}
                    {/* 入境時間 */}

                    <FormItemContent
                        required={false}
                        name="BasicInfo.EntryTime"
                        label="入境時間"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.EntryTime"
                                render={({ field }) => (
                                    <FormControl>
                                        <DatePicker
                                            className="w-full"
                                            value={field.value || ''}
                                            onChange={(date) => field.onChange(date)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />

                    {/* 婚姻狀態 */}
                    <PersonalFormField
                        fieldCode="0100"
                        control={control}
                        name="BasicInfo.MaritalStatus"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.MaritalStatus"
                                render={({ field }) => (
                                    <FormControl>
                                        <Select
                                            placeholder="請選擇"
                                            value={
                                                find(maritalStatusOptions, {
                                                    value: field?.value,
                                                }) || null
                                            }
                                            onChange={(option: any) => field.onChange(option?.value || '')}
                                            options={maritalStatusOptions}
                                            formatOptionLabel={(option: any) => option.text}
                                            isPortal
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                        required
                    />
                </div>
            </div>

            {/* 身分證件區塊 */}
            <div className="rounded-lg bg-gray-50 p-6">
                <div className="mb-4 text-base font-bold">身分證件資料</div>
                <div className="grid grid-cols-4 gap-6">
                    <FormItemContent
                        name="BasicInfo.IDType"
                        label="身分證件類型"
                        required
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.IDType"
                                render={({ field }) => (
                                    <FormControl>
                                        <Select
                                            placeholder="請選擇"
                                            value={
                                                find(idTypeOptions, {
                                                    value: field?.value,
                                                }) || null
                                            }
                                            onChange={(option: any) => {
                                                field.onChange(option?.value || '');
                                                // 只在 IDType 異動時清空 IDNumber 和 IDExpiryDate
                                                setValue('BasicInfo.IDNumber', '');
                                                setValue('BasicInfo.IDExpiryDate', '');
                                            }}
                                            options={idTypeOptions}
                                            formatOptionLabel={(option: any) => option.text}
                                            isPortal
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        name="BasicInfo.IDNumber"
                        label="身分證件號碼"
                        required
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.IDNumber"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            isLoading={isValidateIdNumberLoading}
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(e.target.value)
                                            }
                                            onBlur={handleValidateIdNumber}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        required={false}
                        name="BasicInfo.IDExpiryDate"
                        label="身分證件到期日"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="BasicInfo.IDExpiryDate"
                                render={({ field }) => (
                                    <FormControl>
                                        <DatePicker
                                            className="w-full"
                                            value={field.value || ''}
                                            onChange={field.onChange}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
            </div>
        </div>
    );
};

export default BasicInfoForm;
