import { Checkbox, DatePicker, FormControl, FormField, FormItemContent, Input } from '@mayo/mayo-ui-beta/v2';
import { CirclePlus, Trash2 } from 'lucide-react';
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

const CertificatesForm = () => {
    const { control, setValue, watch } = useFormContext();
    const values = watch();

    const {
        fields: certificateFields,
        append: appendCertificate,
        remove: removeCertificate,
    } = useFieldArray({
        control,
        name: 'Certificates',
    });

    return (
        <div>
            <div className="mb-4 mt-10 text-lg font-bold">證照資料</div>
            <div className="mb-6 border-b border-gray-200" />
            <div className="flex flex-col gap-6">
                {certificateFields.map((field, index) => (
                    <div key={field.id} className="flex gap-4">
                        <div className="border-primary flex-1 rounded-md border-l-4 bg-white py-6 pl-6">
                            <div className="mb-4 flex items-center">
                                <span className="text-primary-lighter mr-2 text-base font-bold">證照 {index + 1}</span>
                                <Trash2
                                    className="size-4 cursor-pointer text-red-500"
                                    onClick={() => removeCertificate(index)}
                                />
                            </div>
                            <div className="mb-4 grid grid-cols-4 gap-6">
                                <FormItemContent
                                    name={`Certificates.${index}.CertificateName`}
                                    label="證照名稱"
                                    required
                                    control={control}
                                    fieldClassName="col-span-1 w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Certificates.${index}.CertificateName`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Input
                                                        placeholder="請輸入"
                                                        value={field.value}
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            field.onChange(e.target.value)
                                                        }
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />
                                <FormItemContent
                                    required={false}
                                    name={`Certificates.${index}.CertificateIssuingUnit`}
                                    label="發照單位"
                                    control={control}
                                    fieldClassName="col-span-1 w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Certificates.${index}.CertificateIssuingUnit`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Input
                                                        placeholder="請輸入"
                                                        value={field.value}
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            field.onChange(e.target.value)
                                                        }
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    required={false}
                                    name={`Certificates.${index}.CertificateIssuingDate`}
                                    label="取得日期"
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Certificates.${index}.CertificateIssuingDate`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <DatePicker
                                                        className="w-full"
                                                        value={field.value || ''}
                                                        onChange={field.onChange}
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />

                                <FormItemContent
                                    required={false}
                                    name={`Certificates.${index}.CertificateExpiryDate`}
                                    label={
                                        <div className="flex h-6 items-center justify-between gap-2">
                                            <span>到期日期</span>
                                            <FormField
                                                control={control}
                                                name={`Certificates.${index}.IsNotExpire`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Checkbox
                                                            label="永久有效"
                                                            checked={field.value}
                                                            onValueChange={(checked: boolean) => {
                                                                field.onChange(checked);
                                                                if (checked) {
                                                                    setValue(
                                                                        `Certificates.${index}.CertificateExpiryDate`,
                                                                        '9999-12-31',
                                                                    );
                                                                } else {
                                                                    setValue(
                                                                        `Certificates.${index}.CertificateExpiryDate`,
                                                                        '',
                                                                    );
                                                                }
                                                            }}
                                                            className="mr-2 whitespace-nowrap"
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        </div>
                                    }
                                    control={control}
                                    fieldClassName="w-full"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Certificates.${index}.CertificateExpiryDate`}
                                            render={({ field }) => {
                                                const isNotExpire = values?.Certificates[index]?.IsNotExpire;

                                                return (
                                                    <FormControl>
                                                        <DatePicker
                                                            className="w-full"
                                                            value={field.value || ''}
                                                            onChange={field.onChange}
                                                            disabled={isNotExpire}
                                                        />
                                                    </FormControl>
                                                );
                                            }}
                                        />
                                    }
                                />

                                <FormItemContent
                                    required={false}
                                    name={`Certificates.${index}.Notes`}
                                    label="備註"
                                    control={control}
                                    fieldClassName="w-full col-span-1"
                                    itemComponent={
                                        <FormField
                                            control={control}
                                            name={`Certificates.${index}.Notes`}
                                            render={({ field }) => (
                                                <FormControl>
                                                    <Input
                                                        placeholder="請輸入"
                                                        value={field.value}
                                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                            field.onChange(e.target.value)
                                                        }
                                                    />
                                                </FormControl>
                                            )}
                                        />
                                    }
                                />
                            </div>
                        </div>
                    </div>
                ))}
                <button
                    type="button"
                    className="text-primary-lighter mt-2 flex items-center text-base font-bold"
                    onClick={() =>
                        appendCertificate({
                            CertificateName: '',
                            CertificateIssuingUnit: '',
                            CertificateIssuingDate: '',
                            CertificateExpiryDate: '',
                            IsNotExpire: false,
                            Notes: '',
                        })
                    }
                >
                    <CirclePlus className="mr-2 size-4" />
                    新增
                </button>
            </div>
        </div>
    );
};

export default CertificatesForm;
