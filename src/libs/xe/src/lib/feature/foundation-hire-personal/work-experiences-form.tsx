import {
    DateRangePicker,
    FormControl,
    FormField,
    FormItemContent,
    Input,
    Select,
    Textarea,
} from '@mayo/mayo-ui-beta/v2';
import { find, get } from 'lodash';
import { CirclePlus, Trash2 } from 'lucide-react';
import React from 'react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { HireProcessOptionsResponse } from '../../redux/query/types';

const WorkExperiencesForm = ({
    hireProcessOptions,
}: {
    hireProcessOptions: HireProcessOptionsResponse['data'] | undefined;
}) => {
    const { control, watch, setValue } = useFormContext();
    const values = watch();
    const baseSettingOptionsMap = hireProcessOptions?.baseSettingOptionsMap;

    const {
        fields: workExperienceFields,
        append: appendWorkExperience,
        remove: removeWorkExperience,
    } = useFieldArray({
        control,
        name: 'WorkExperiences',
    });

    // 產業類別 IndustryCategory
    const industryCategoryOptions = get(baseSettingOptionsMap, 'IndustryCategory', []);

    const jobCategoryOptions = get(baseSettingOptionsMap, 'JobCategory', []);

    return (
        <div>
            <div className="mb-4 mt-10 text-lg font-bold">工作經歷</div>
            <div className="mb-6 border-b border-gray-200" />
            <div className="flex flex-col gap-6">
                {workExperienceFields.map((field, index) => {
                    const industryName = `IndustryName_${values?.WorkExperiences[index]?.IndustryCategory}`;
                    const industryNameOptions = get(baseSettingOptionsMap, industryName, []) || [];

                    return (
                        <div key={field.id} className="flex gap-4">
                            <div className="border-primary flex-1 rounded-md border-l-4 bg-white py-6 pl-6">
                                <div className="mb-4 flex items-center">
                                    <span className="text-primary-lighter mr-2 text-base font-bold">
                                        工作經歷 {index + 1}
                                    </span>
                                    <Trash2
                                        className="size-4 cursor-pointer text-red-500"
                                        onClick={() => removeWorkExperience(index)}
                                    />
                                </div>
                                <div className="mb-4 grid grid-cols-4 gap-6">
                                    <FormItemContent
                                        name={`WorkExperiences.${index}.CompanyName`}
                                        label="公司名稱"
                                        required
                                        control={control}
                                        fieldClassName="w-full"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.CompanyName`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Input
                                                            placeholder="請輸入"
                                                            value={field.value}
                                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                                field.onChange(e.target.value)
                                                            }
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />

                                    <FormItemContent
                                        name={`WorkExperiences.${index}.IndustryCategory`}
                                        label="產業類別"
                                        required
                                        control={control}
                                        fieldClassName="w-full"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.IndustryCategory`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Select
                                                            placeholder="請選擇"
                                                            value={
                                                                find(industryCategoryOptions, {
                                                                    value: field?.value,
                                                                }) || null
                                                            }
                                                            onChange={(option: any) => {
                                                                field.onChange(option?.value || '');
                                                                setValue(
                                                                    `WorkExperiences.${index}.IndustrySubCategory`,
                                                                    '',
                                                                );
                                                            }}
                                                            options={industryCategoryOptions}
                                                            formatOptionLabel={(option: any) => option.text}
                                                            isPortal
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />
                                    {/* 產業子分類 */}
                                    <FormItemContent
                                        required={false}
                                        name={`WorkExperiences.${index}.IndustrySubCategory`}
                                        label="產業子類別"
                                        control={control}
                                        fieldClassName="w-full"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.IndustrySubCategory`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Select
                                                            placeholder="請選擇"
                                                            value={
                                                                find(industryNameOptions, {
                                                                    value: field?.value,
                                                                }) || null
                                                            }
                                                            onChange={(option: any) =>
                                                                field.onChange(option?.value || '')
                                                            }
                                                            options={industryNameOptions}
                                                            formatOptionLabel={(option: any) => option.text}
                                                            isPortal
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />

                                    <FormItemContent
                                        name={`WorkExperiences.${index}.JobCategory`}
                                        label="職務類別"
                                        required
                                        control={control}
                                        fieldClassName="w-full"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.JobCategory`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Select
                                                            placeholder="請選擇"
                                                            value={
                                                                find(jobCategoryOptions, {
                                                                    value: field?.value,
                                                                }) || null
                                                            }
                                                            onChange={(option: any) =>
                                                                field.onChange(option?.value || '')
                                                            }
                                                            options={jobCategoryOptions}
                                                            formatOptionLabel={(option: any) => option.text}
                                                            isPortal
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />

                                    <FormItemContent
                                        name={`WorkExperiences.${index}.JobTitle`}
                                        label="職稱"
                                        required
                                        control={control}
                                        fieldClassName="w-full col-span-1"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.JobTitle`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Input
                                                            placeholder="請輸入"
                                                            value={field.value}
                                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                                field.onChange(e.target.value)
                                                            }
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />

                                    <FormItemContent
                                        name={`WorkExperiences.${index}.DepartmentName`}
                                        label="任職部門"
                                        required={false}
                                        control={control}
                                        fieldClassName="w-full"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.DepartmentName`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Input
                                                            placeholder="請輸入"
                                                            value={field.value}
                                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                                field.onChange(e.target.value)
                                                            }
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />

                                    <FormItemContent
                                        name={`WorkExperiences.${index}.EmploymentRange`}
                                        label="任職期間"
                                        control={control}
                                        fieldClassName="w-full col-span-1"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.EmploymentRange`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <DateRangePicker
                                                            value={field.value}
                                                            onChange={field.onChange}
                                                            placeholder={['YYYY/MM/DD', 'YYYY/MM/DD']}
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />

                                    <FormItemContent
                                        name={`WorkExperiences.${index}.DirectReport`}
                                        label="直屬主管姓名"
                                        required={false}
                                        control={control}
                                        fieldClassName="w-full"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.DirectReport`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Input
                                                            placeholder="請輸入"
                                                            value={field.value}
                                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                                field.onChange(e.target.value)
                                                            }
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />
                                    <FormItemContent
                                        name={`WorkExperiences.${index}.JobDuties`}
                                        label="工作內容"
                                        required={false}
                                        control={control}
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.JobDuties`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Textarea
                                                            placeholder="請輸入"
                                                            value={field.value}
                                                            onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                                                                field.onChange(e.target.value)
                                                            }
                                                            maxLength={2000}
                                                            rows={3}
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />
                                    <FormItemContent
                                        name={`WorkExperiences.${index}.SeperationReason`}
                                        label="離職原因"
                                        required={false}
                                        control={control}
                                        fieldClassName="w-full"
                                        itemComponent={
                                            <FormField
                                                control={control}
                                                name={`WorkExperiences.${index}.SeperationReason`}
                                                render={({ field }) => (
                                                    <FormControl>
                                                        <Input
                                                            placeholder="請輸入"
                                                            value={field.value}
                                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                                field.onChange(e.target.value)
                                                            }
                                                        />
                                                    </FormControl>
                                                )}
                                            />
                                        }
                                    />
                                </div>
                            </div>
                        </div>
                    );
                })}
                <button
                    type="button"
                    className="text-primary-lighter mt-2 flex items-center text-base font-bold"
                    onClick={() =>
                        appendWorkExperience({
                            CompanyName: '',
                            IndustryCategory: '',
                            IndustrySubCategory: '',
                            JobCategory: '',
                            DepartmentName: '',
                            JobTitle: '',
                            DirectReport: '',
                            EmploymentRange: ['', ''],
                            JobDuties: '',
                            SeperationReason: '',
                        })
                    }
                >
                    <CirclePlus className="mr-2 size-4" />
                    新增
                </button>
            </div>
        </div>
    );
};

export default WorkExperiencesForm;
