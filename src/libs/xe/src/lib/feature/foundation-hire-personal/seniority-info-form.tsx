import { FormControl, FormField, FormItemContent, Input } from '@mayo/mayo-ui-beta/v2';
import React from 'react';
import { useFormContext } from 'react-hook-form';

const SeniorityInfoForm = () => {
    const { control } = useFormContext();
    return (
        <div>
            <div className="mb-4 text-lg font-bold">到職前年資</div>
            <div className="mb-6 border-b border-gray-200" />
            <div className="grid grid-cols-4 gap-6">
                <div>
                    <FormItemContent
                        name="SeniorityInfo.ReHireRecognizedSeniority"
                        label="承認內部年資"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="SeniorityInfo.ReHireRecognizedSeniority"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(String(e.target.value).replace(/\D/g, ''))
                                            }
                                            suffixIcon={<span className="text-gray-500">年</span>}
                                            showSuffixIcon
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div>
                    <FormItemContent
                        name="SeniorityInfo.CareerSeniority"
                        label="工作年資"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="SeniorityInfo.CareerSeniority"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(String(e.target.value).replace(/\D/g, ''))
                                            }
                                            suffixIcon={<span className="text-gray-500">年</span>}
                                            showSuffixIcon
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div>
                    <FormItemContent
                        name="SeniorityInfo.CareerRecognizedSeniority"
                        label="專業年資"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="SeniorityInfo.CareerRecognizedSeniority"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(String(e.target.value).replace(/\D/g, ''))
                                            }
                                            suffixIcon={<span className="text-gray-500">年</span>}
                                            showSuffixIcon
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div>
                    <FormItemContent
                        name="SeniorityInfo.ReHireRecognizedJobGradeSeniority"
                        label="承認職等年資"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="SeniorityInfo.ReHireRecognizedJobGradeSeniority"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(String(e.target.value).replace(/\D/g, ''))
                                            }
                                            suffixIcon={<span className="text-gray-500">年</span>}
                                            showSuffixIcon
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
            </div>
        </div>
    );
};

export default SeniorityInfoForm;
