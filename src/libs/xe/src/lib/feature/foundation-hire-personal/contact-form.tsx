import { FormControl, FormField, FormItemContent, Input } from '@mayo/mayo-ui-beta/v2';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { useValidateBusinessEmailMutation } from '../../redux/query/hireProcessApi';

const ContactForm = ({ personalId }: { personalId: string }) => {
    const { control, clearErrors, setError } = useFormContext();

    const [validateBusinessEmail, { isLoading: isValidateBusinessEmailLoading }] = useValidateBusinessEmailMutation();

    const handleValidateBusinessEmail = async (email: string | undefined) => {
        if (!email) return;

        try {
            clearErrors('Contact.BusinessEmail');
            await validateBusinessEmail({
                onboardingId: personalId,
                businessEmail: email,
            }).unwrap();
        } catch (error: any) {
            const errorMessage = error?.response?.data?.message || '公司信箱驗證失敗';

            setError('Contact.BusinessEmail', { message: errorMessage });
        }
    };

    return (
        <div>
            <div className="mb-4 text-lg font-bold">通訊資料</div>
            <div className="mb-6 border-b border-gray-200" />
            <div className="grid grid-cols-4 gap-6">
                <FormItemContent
                    name="Contact.MobileNumber"
                    label="電話(手機)"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="Contact.MobileNumber"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    name="Contact.PermanentAddress"
                    label="戶籍地址"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="Contact.PermanentAddress"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    name="Contact.ContactAddress"
                    label="聯絡地址"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="Contact.ContactAddress"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="Contact.ExtensionNumber"
                    label="分機"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="Contact.ExtensionNumber"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="Contact.BusinessMobileNumber"
                    label="公務手機"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="Contact.BusinessMobileNumber"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="Contact.BusinessEmail"
                    label="公司信箱"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="Contact.BusinessEmail"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                        onBlur={() => handleValidateBusinessEmail(field.value)}
                                        isLoading={isValidateBusinessEmailLoading}
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="Contact.PersonalEmail"
                    label="私人信箱"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="Contact.PersonalEmail"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            {/* 緊急聯絡人區塊 */}
            <div className="rounded-lg bg-gray-50 p-6">
                <div className="mb-4 text-base font-bold">緊急聯絡人</div>
                <div className="grid grid-cols-4 gap-6">
                    <FormItemContent
                        name="Contact.Emergency"
                        label="姓名"
                        required
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="Contact.Emergency"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(e.target.value)
                                            }
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        required={false}
                        name="Contact.EmergencyRelationship"
                        label="關係"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="Contact.EmergencyRelationship"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(e.target.value)
                                            }
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        required={false}
                        name="Contact.EmergencyLandlineNumber"
                        label="電話(市話)"
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="Contact.EmergencyLandlineNumber"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(e.target.value)
                                            }
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        name="Contact.EmergencyMobileNumber"
                        label="電話(手機)"
                        required
                        control={control}
                        fieldClassName="w-full"
                        itemComponent={
                            <FormField
                                control={control}
                                name="Contact.EmergencyMobileNumber"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            placeholder="請輸入"
                                            value={field.value}
                                            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                                field.onChange(e.target.value)
                                            }
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
            </div>
        </div>
    );
};

export default ContactForm;
