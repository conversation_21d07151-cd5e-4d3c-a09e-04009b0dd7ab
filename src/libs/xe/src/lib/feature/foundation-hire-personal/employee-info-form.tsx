import { FormControl, FormField, FormItemContent, Select } from '@mayo/mayo-ui-beta/v2';
import { find, get } from 'lodash';
import { useFormContext } from 'react-hook-form';

import { HireProcessOptionsResponse } from '../../redux/query/types';

const EmployeeInfoForm = ({
    hireProcessOptions,
}: {
    hireProcessOptions: HireProcessOptionsResponse['data'] | undefined;
}) => {
    const { control, watch, setValue } = useFormContext();
    const values = watch();

    const baseSettingOptionsMap = hireProcessOptions?.baseSettingOptionsMap;

    // 身份類別 IdentityCategory
    const identityCategoryOptions = get(baseSettingOptionsMap, 'IdentityCategory', []);

    const personalEffectiveDepOptions = hireProcessOptions?.personalEffectiveDepOptions?.map((option) => ({
        value: option.departmentId,
        text: option.departmentName,
    }));

    const countryOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: 'Country' })?.items;

    // filter by fieldCode: 0290 & upperKey is values.Country
    const areasOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0290' })
        ?.items?.filter((item) => item.upperKey === values.EmployeeInfo?.WorkCountryId)
        .map((item) => ({
            value: item.key,
            text: item.value,
        }));

    const deptPositionPositionNumberOptions = find(hireProcessOptions?.personalEffectiveDepOptions, {
        departmentId: values.EmployeeInfo.DepartmentId,
    })?.positions?.map((option) => ({
        value: option.positionNumber,
        text: option.supervisorName,
    }));

    // 職系 0230
    const jobFamilyOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0230' })?.items.map(
        (item) => ({
            value: item.key,
            text: item.value,
        }),
    );
    // 職類 0250
    const jobStreamOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0250' })
        ?.items.filter((item) => item.upperKey === values.EmployeeInfo?.JobFamilyId)
        .map((item) => ({
            value: item.key,
            text: item.value,
        }));
    // 職等 0220
    const jobGradeOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0220' })?.items.map(
        (item) => ({
            value: item.key,
            text: item.value,
        }),
    );
    // 職級 0210, upperFieldCode:"0220"
    const jobLevelOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0210' })
        ?.items.filter((item) => item.upperKey === values.EmployeeInfo?.JobGradeId)
        .map((item) => ({
            value: item.key,
            text: item.value,
        }));
    // 職稱 0240
    const jobTitleOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0240' })?.items.map(
        (item) => ({
            value: item.key,
            text: item.value,
        }),
    );

    // 對外職稱 0241
    const businessTitleOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0241' })?.items.map(
        (item) => ({
            value: item.key,
            text: item.value,
        }),
    );

    // 群組 0260
    const jobGroupOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0260' })?.items.map(
        (item) => ({
            value: item.key,
            text: item.value,
        }),
    );
    // 身份子類別 0280
    const identitySubGroupOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0280' })
        ?.items.filter((item) => item.upperKey === values.EmployeeInfo?.EmployeeGroupId)
        .map((item) => ({
            value: item.key,
            text: item.value,
        }));

    // 公司別 0310
    const corporationOptions = find(hireProcessOptions?.personalFieldOptions, { fieldCode: '0310' })?.items.map(
        (item) => ({
            value: item.key,
            text: item.value,
        }),
    );

    return (
        <div>
            <div className="mb-4 text-lg font-bold">職位資料</div>
            <div className="mb-6 border-b border-gray-200" />
            <div className="grid grid-cols-4 gap-6">
                <FormItemContent
                    name="EmployeeInfo.JobFamilyId"
                    label="職系"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.JobFamilyId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(jobFamilyOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => {
                                            field.onChange(option?.value || '');
                                            setValue('EmployeeInfo.JobStreamId', '');
                                        }}
                                        options={jobFamilyOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="EmployeeInfo.JobStreamId"
                    label="職類"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.JobStreamId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(jobStreamOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={jobStreamOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="EmployeeInfo.JobGradeId"
                    label="職等"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.JobGradeId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(jobGradeOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => {
                                            field.onChange(option?.value || '');
                                            setValue('EmployeeInfo.JobLevelId', '');
                                        }}
                                        options={jobGradeOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="EmployeeInfo.JobLevelId"
                    label="職級"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.JobLevelId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(jobLevelOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={jobLevelOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    name="EmployeeInfo.JobTitleId"
                    label="職稱"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.JobTitleId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(jobTitleOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={jobTitleOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="EmployeeInfo.BusinessTitleId"
                    label="對外職稱"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.BusinessTitleId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(businessTitleOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={businessTitleOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="EmployeeInfo.JobGroupId"
                    label="職務群組"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.JobGroupId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(jobGroupOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={jobGroupOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    name="EmployeeInfo.EmployeeGroupId"
                    label="身份類別"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.EmployeeGroupId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(identityCategoryOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => {
                                            field.onChange(option?.value || '');
                                            setValue('EmployeeInfo.EmployeeSubGroupId', '');
                                        }}
                                        options={identityCategoryOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    name="EmployeeInfo.EmployeeSubGroupId"
                    label="身份子類別"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.EmployeeSubGroupId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(identitySubGroupOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={identitySubGroupOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />

                <FormItemContent
                    name="EmployeeInfo.DepartmentId"
                    label="所屬單位"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.DepartmentId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(personalEffectiveDepOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => {
                                            field.onChange(option?.value || '');
                                            setValue('EmployeeInfo.DeptPositionPositionNumber', '');
                                        }}
                                        options={personalEffectiveDepOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />

                <FormItemContent
                    name="EmployeeInfo.DeptPositionPositionNumber"
                    label="直屬主管"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.DeptPositionPositionNumber"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(deptPositionPositionNumberOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={deptPositionPositionNumberOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />

                <FormItemContent
                    name="EmployeeInfo.WorkCountryId"
                    label="國家"
                    required
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.WorkCountryId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={find(countryOptions, { key: field?.value }) || null}
                                        onChange={(option: any) => {
                                            field.onChange(option?.key || '');
                                            setValue('EmployeeInfo.WorkCountyId', '');
                                        }}
                                        options={countryOptions || []}
                                        formatOptionLabel={(option: any) => option.value}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />

                <FormItemContent
                    required={false}
                    name="EmployeeInfo.WorkCountyId"
                    label="地區"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.WorkCountyId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={find(areasOptions, { value: field?.value }) || null}
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={areasOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
                <FormItemContent
                    required={false}
                    name="EmployeeInfo.CorporationId"
                    label="公司別"
                    control={control}
                    fieldClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="EmployeeInfo.CorporationId"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            find(corporationOptions, {
                                                value: field?.value,
                                            }) || null
                                        }
                                        onChange={(option: any) => field.onChange(option?.value || '')}
                                        options={corporationOptions}
                                        formatOptionLabel={(option: any) => option.text}
                                        isPortal
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>
        </div>
    );
};

export default EmployeeInfoForm;
