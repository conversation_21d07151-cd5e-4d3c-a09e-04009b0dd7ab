import { FormItemContent } from '@mayo/mayo-ui-beta/v2';
import dayjs from 'dayjs';
import { find } from 'lodash';
import { useFormContext } from 'react-hook-form';

import { useGetHireProcessOptionsQuery } from '../../../redux/query/hireProcessApi';

interface FormFieldProps {
    fieldCode: string;
    control: any;
    name: string;
    itemComponent: React.ReactNode;
    formItemClassName?: string;
    [key: string]: any;
}

const PersonalFormField = ({
    fieldCode,
    control,
    name,
    itemComponent,
    formItemClassName = 'w-full',
    ...rest
}: FormFieldProps) => {
    const { getValues } = useFormContext();

    const { data } = useGetHireProcessOptionsQuery({
        onboardingDate: dayjs(getValues('OnboardingDate')).format('YYYY-MM-DD'),
    });
    const hireProcessOptions = data?.data;

    const personalFields = hireProcessOptions?.personalFields || [];
    const fieldConfig = find(personalFields, { fieldCode });

    if (!fieldConfig || !fieldConfig.isDisplay) return null;

    return (
        <FormItemContent
            name={name}
            label={fieldConfig.displayText}
            required={fieldConfig.isRequired}
            control={control}
            fieldClassName={formItemClassName}
            itemComponent={itemComponent}
            {...rest}
        />
    );
};

export default PersonalFormField;
