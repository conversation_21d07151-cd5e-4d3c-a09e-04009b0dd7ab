import { Control } from 'react-hook-form';

export interface CustomLeaveTypeRow {
    individualLeaveTypeId: string;
    leaveCode: string;
    leaveName: string;
    usedMinutes: number;
    leaveSourceType: string;
    leaveSourceTypeName: string;
    seniority: number;
    availableMinutes: number;
    startDate: string;
    endDate: string;
    restrictedDate: string;
    leaveClass: string;
}

export interface CustomLeaveTypeTableProps {
    name: string; // field array name, e.g., 'individualLeaveTypesGeneral' or 'individualLeaveTypesSpecial'
    isFixedTable?: boolean;
    leaveTypeOptions?: Option[];
    onRefresh?: (rowIndex: number) => void;
    onDelete?: (rowIndex: number) => void;
    defaultLeaveClass: 'general' | 'special';
}

// Add a type for DataTable rows that includes id
export interface CustomLeaveTypeRowWithId extends CustomLeaveTypeRow {
    id: string;
}

export interface Option {
    label: string;
    value: string;
}

export interface SelectWithTextareaProps {
    label: string;
    selectName: string;
    textareaName: string;
    control: Control<any>;
    selectOptions: Option[];
    textareaPlaceholder?: string;
    maxLength?: number;
    disabled?: boolean;
    className?: string;
    isClearable?: boolean;
    onSelectChange?: (value: string | null) => void; // Optional callback for additional logic
}

export const initialAttendanceState = {
    changeRecord: {
        shiftId: '',
        shiftName: '',
        shiftChangeReason: '',
        attendanceRuleId: '',
        attendanceRuleName: '',
        attendanceRuleChangeReason: '',
    },
    individualLeaveTypesGeneral: [],
    individualLeaveTypesSpecial: [],
    individualLeaveYear: 2025,
    onboardingDate: new Date().toISOString().split('T')[0],
    onboardingId: '',
    workAreaGroup: '',
    workAreaGroupNote: '',
    attendanceScopeChange: '',
    attendanceScopeChangeNote: '',
};

export type AttendanceState = {
    changeRecord: {
        shiftId: string;
        shiftName: string;
        shiftChangeReason: string;
        attendanceRuleId: string;
        attendanceRuleName: string;
        attendanceRuleChangeReason: string;
    };
    individualLeaveTypes: CustomLeaveTypeRow[];
    individualLeaveTypesGeneral: CustomLeaveTypeRow[];
    individualLeaveTypesSpecial: CustomLeaveTypeRow[];
    individualLeaveYear: number;
    onboardingDate: string | null | undefined;
    onboardingId: string;
    workAreaGroup: string;
    workAreaGroupNote: string;
    attendanceScopeChange: string;
    attendanceScopeChangeNote: string;
};
