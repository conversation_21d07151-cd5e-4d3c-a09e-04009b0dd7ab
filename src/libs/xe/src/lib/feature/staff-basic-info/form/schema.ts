import z from 'zod';

const customLeaveTypeRowSchema = z
    .object({
        individualLeaveTypeId: z.string().optional(),
        leaveCode: z.string().optional(),
        leaveName: z.string().optional(),
        usedMinutes: z.number().optional(),
        leaveSourceType: z.string().optional(),
        leaveSourceTypeName: z.string().optional(),
        seniority: z.number().optional(),
        availableMinutes: z.number().optional(),
        startDate: z.string().nullable().optional(),
        endDate: z.string().nullable().optional(),
        restrictedDate: z.string().nullable().optional(),
        leaveClass: z.string().optional(),
    })
    .superRefine((data, ctx) => {
        const isValidityDisabled = data.leaveCode === 'H1003' || data.leaveCode === 'H1004';
        const isRestrictedDateDisabled = data.leaveCode === 'H1001' || data.leaveCode === 'H1002';

        if (!isValidityDisabled) {
            if (!data.startDate) {
                ctx.addIssue({
                    code: 'custom',
                    path: ['startDate'],
                    message: 'Date is required',
                });
            }
            if (!data.endDate) {
                ctx.addIssue({
                    code: 'custom',
                    path: ['endDate'],
                    message: 'Date is required',
                });
            }
        }

        if (!isRestrictedDateDisabled) {
            if (!data.restrictedDate) {
                ctx.addIssue({
                    code: 'custom',
                    path: ['restrictedDate'],
                    message: 'Date is required',
                });
            }
        }
    });

// Define your form schema
export const FormSchema = z.object({
    changeRecord: z
        .object({
            shiftId: z.string().optional(),
            shiftName: z.string().optional(),
            shiftChangeReason: z.string().optional(),
            attendanceRuleId: z.string().optional(),
            attendanceRuleName: z.string().optional(),
            attendanceRuleChangeReason: z.string().optional(),
        })
        .optional(),
    individualLeaveTypes: z.array(customLeaveTypeRowSchema).optional(),
    individualLeaveTypesGeneral: z.array(customLeaveTypeRowSchema).optional(),
    individualLeaveTypesSpecial: z.array(customLeaveTypeRowSchema).optional(),
    individualLeaveYear: z.number().optional(),
    onboardingDate: z.string().nullable().optional(),
    onboardingId: z.string().optional(),
    workAreaGroup: z.string().optional(),
    workAreaGroupNote: z.string().optional(),
    attendanceScopeChange: z.string().optional(),
    attendanceScopeChangeNote: z.string().optional(),
});
