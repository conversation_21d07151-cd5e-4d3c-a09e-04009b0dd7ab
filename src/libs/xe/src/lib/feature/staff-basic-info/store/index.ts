import { combineReducers } from '@reduxjs/toolkit';
import type { TypedUseSelectorHook } from 'react-redux';
import { useSelector } from 'react-redux';
import { RootState } from 'xe/redux/store';

import reducers, { SLICE_NAME, StaffBasicInfoState } from './staffBasicInfoSlice';

const reducer = combineReducers({
    data: reducers,
});

export const useAppSelector: TypedUseSelectorHook<
    RootState & {
        [SLICE_NAME]: {
            data: StaffBasicInfoState;
        };
    }
> = useSelector;

export * from './staffBasicInfoSlice';
export { useAppDispatch } from 'xe/redux/store';
export default reducer;
