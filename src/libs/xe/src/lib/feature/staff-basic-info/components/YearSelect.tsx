import { Select } from '@mayo/mayo-ui-beta/v2';
import { memo, useCallback,useMemo } from 'react';

export const YearSelect = memo(
    ({
        field,
        yearOptions,
        handleYearChange,
    }: {
        field: any;
        yearOptions: { label: string; value: string }[];
        handleYearChange: (option: { label: string; value: string } | null) => void;
    }) => {
        const selectedOption = useMemo(
            () => yearOptions.find((opt) => opt.value === field.value?.toString()) || null,
            [field.value, yearOptions],
        );

        const onChange = useCallback(
            (option: { label: string; value: string } | null) => {
                const newValue = option ? Number(option.value) : Number(yearOptions[0].value);
                field.onChange(newValue);
                handleYearChange(option);
            },
            [field, handleYearChange, yearOptions],
        );

        return (
            <Select
                options={yearOptions}
                value={selectedOption}
                onChange={onChange}
                placeholder="請選擇"
                className="w-[120px]"
                isClearable={false}
            />
        );
    },
);
