import {
    Button,
    ColumnDef,
    DataTable,
    DatePicker,
    DateRangePicker,
    FormItemContent,
    NumberPicker,
    Select,
    Tooltip,
} from '@mayo/mayo-ui-beta/v2';
import { PlusCircle,RefreshCw, Trash2 } from 'lucide-react';
import { useMemo } from 'react';
import { useFieldArray,useFormContext } from 'react-hook-form';

import { CustomLeaveTypeRow, CustomLeaveTypeTableProps } from '../form/types';

export const CustomLeaveTypeTable = ({
    name,
    isFixedTable = false,
    leaveTypeOptions,
    onRefresh,
    onDelete,
    defaultLeaveClass,
}: CustomLeaveTypeTableProps) => {
    const { control, setValue } = useFormContext();
    const { fields, append, remove } = useFieldArray({
        control,
        name,
    });

    const getHoursAndMinutes = (availableMinutes: number) => {
        const hours = Math.floor(availableMinutes / 60);
        const minutes = availableMinutes % 60;
        return { hours, minutes };
    };
    const handleAddRow = () => {
        const newRow: CustomLeaveTypeRow = {
            individualLeaveTypeId: '',
            leaveCode: '',
            leaveName: '',
            usedMinutes: 0,
            leaveSourceType: 'default',
            leaveSourceTypeName: '手動給假',
            seniority: 0,
            availableMinutes: 480,
            startDate: '',
            endDate: '',
            restrictedDate: '',
            leaveClass: defaultLeaveClass,
        };
        append(newRow);
    };
    const columns: ColumnDef<any>[] = useMemo(
        () => [
            {
                accessorKey: 'leaveName',
                header: '假別',
                enableResizing: false,
                size: 80,
                cell: ({ row }) => {
                    if (isFixedTable) {
                        return row.original.leaveName;
                    }
                    return (
                        <FormItemContent
                            control={control}
                            name={`${name}.${row.index}.leaveCode`}
                            label=""
                            renderField={({ field }) => (
                                <Select
                                    options={leaveTypeOptions}
                                    value={leaveTypeOptions?.find((opt) => opt.value === field.value) || null}
                                    onChange={(option: { label: string; value: string } | null) => {
                                        field.onChange(option ? option.value : '');
                                        setValue(`${name}.${row.index}.leaveName`, option ? option.label : '');
                                    }}
                                    placeholder="請選擇"
                                    className="min-w-[120px]"
                                    isClearable={false}
                                />
                            )}
                        />
                    );
                },
            },
            {
                id: 'availableTime',
                header: '可用時數',
                enableResizing: false,
                size: 50,
                cell: ({ row }) => {
                    const data = row.original;
                    const { hours, minutes } = getHoursAndMinutes(data.availableMinutes || 0);
                    return (
                        <div className="flex items-center gap-2">
                            <FormItemContent
                                control={control}
                                name={`${name}.${row.index}.availableMinutes`}
                                label=""
                                renderField={({ field }) => (
                                    <NumberPicker
                                        value={Math.floor((field.value ?? 0) / 60)}
                                        min={0}
                                        max={999}
                                        unit="時"
                                        onChange={(val: number) => {
                                            field.onChange(val * 60 + minutes);
                                        }}
                                    />
                                )}
                            />
                            <FormItemContent
                                control={control}
                                name={`${name}.${row.index}.availableMinutes`}
                                label=""
                                renderField={({ field }) => (
                                    <NumberPicker
                                        value={(field.value ?? 0) % 60}
                                        min={0}
                                        max={59}
                                        unit="分"
                                        onChange={(val: number) => {
                                            field.onChange(hours * 60 + val);
                                        }}
                                    />
                                )}
                            />
                        </div>
                    );
                },
            },
            {
                accessorKey: 'validity',
                header: '有效期限',
                enableResizing: false,
                size: 250,
                cell: ({ row }) => {
                    const data = row.original;
                    const isDisabled = data.leaveCode === 'H1003' || data.leaveCode === 'H1004';
                    return (
                        <FormItemContent
                            control={control}
                            name={`${name}.${row.index}.startDate`}
                            label=""
                            renderField={({ field: startField }) => (
                                <FormItemContent
                                    control={control}
                                    name={`${name}.${row.index}.endDate`}
                                    label=""
                                    renderField={({ field: endField }) => (
                                        <DateRangePicker
                                            format="YYYY/MM/DD"
                                            onChange={(val: [string, string]) => {
                                                startField.onChange(val[0]);
                                                endField.onChange(val[1]);
                                            }}
                                            picker="date"
                                            value={[startField.value, endField.value]}
                                            language="zh-TW"
                                            disabled={isDisabled}
                                        />
                                    )}
                                />
                            )}
                        />
                    );
                },
            },
            {
                accessorKey: 'restrictedDate',
                header: '計薪基準日',
                enableResizing: false,
                size: 140,
                cell: ({ row }) => {
                    const data = row.original;
                    const isDisabled = data.leaveCode === 'H1001' || data.leaveCode === 'H1002';
                    return (
                        <FormItemContent
                            control={control}
                            name={`${name}.${row.index}.restrictedDate`}
                            label=""
                            renderField={({ field }) => (
                                <DatePicker
                                    value={field.value}
                                    format="YYYY/MM/DD"
                                    onChange={field.onChange}
                                    language="zh-TW"
                                    disabled={isDisabled}
                                />
                            )}
                        />
                    );
                },
            },
            {
                accessorKey: 'leaveSourceTypeName',
                header: '來源',
                enableResizing: false,
                size: 80,
                cell: ({ row }) => row.original.leaveSourceTypeName,
            },
            {
                id: 'actions',
                header: '功能',
                enableResizing: false,
                size: 80,
                meta: {
                    pin: 'right',
                },
                cell: ({ row }) => (
                    <div className="flex justify-around gap-2">
                        <Tooltip content="重算">
                            <Button variant="ghost" size="icon" onClick={() => onRefresh?.(row.index)}>
                                <RefreshCw className="h-4 w-4 text-gray-600" />
                            </Button>
                        </Tooltip>
                        <Tooltip content="刪除">
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                    if (fields.length > 1) {
                                        remove(row.index);
                                        onDelete?.(row.index);
                                    }
                                }}
                                disabled={fields.length === 1}
                            >
                                <Trash2 className="h-4 w-4 text-gray-600" />
                            </Button>
                        </Tooltip>
                    </div>
                ),
            },
        ],
        [control, name, leaveTypeOptions, setValue, remove, onRefresh, onDelete, isFixedTable, fields],
    );

    // If leaveTypeOptions is empty, show an empty table (no rows)
    const isNoOptions = !leaveTypeOptions || leaveTypeOptions.length === 0;
    const tableDataForTable = useMemo(() => (isNoOptions ? [] : fields), [isNoOptions, fields]);
    return (
        <>
            <DataTable<any, unknown>
                columns={columns}
                data={tableDataForTable}
                showPagination={false}
                getRowId={(row) => row.id}
            />
            {!isFixedTable && (
                <div className="mt-2">
                    <Button
                        variant="link"
                        size="sm"
                        onClick={handleAddRow}
                        className="text-primary flex items-center gap-1"
                        disabled={fields.length >= (leaveTypeOptions?.length || 0)}
                    >
                        <PlusCircle className="h-4 w-4" /> 新增
                    </Button>
                </div>
            )}
        </>
    );
};

export default CustomLeaveTypeTable;
