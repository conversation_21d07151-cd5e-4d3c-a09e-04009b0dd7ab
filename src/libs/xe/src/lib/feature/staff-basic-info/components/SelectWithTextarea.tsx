import { FormItemContent,Select, Textarea } from '@mayo/mayo-ui-beta/v2';
import React from 'react';

import { Option,SelectWithTextareaProps  } from '../form/types';

const SelectWithTextarea: React.FC<SelectWithTextareaProps> = ({
    label,
    selectName,
    textareaName,
    control,
    selectOptions,
    textareaPlaceholder = '請輸入',
    maxLength = 1000,
    disabled = false,
    className = '',
    isClearable = false,
    onSelectChange,
}) => (
        <div className={`flex w-full flex-col gap-2 ${className}`}>
            <FormItemContent
                name={selectName}
                label={label}
                labelClassName="text-dark-900 mb-1 text-base font-normal"
                fieldClassName="w-full"
                control={control}
                renderField={({ field }) => (
                    <Select
                        options={selectOptions}
                        value={selectOptions.find((opt) => opt.value === field.value) || null}
                        onChange={(option: Option | null) => {
                            const value = option ? option.value : null;
                            field.onChange(value);
                            onSelectChange?.(value);
                        }}
                        isDisabled={disabled}
                        placeholder="請選擇"
                        className="mb-2 w-full"
                        isClearable={isClearable}
                    />
                )}
            />
            <FormItemContent
                name={textareaName}
                label=""
                fieldClassName="w-full"
                control={control}
                renderField={({ field }) => (
                    <Textarea
                        value={field.value || ''}
                        onChange={field.onChange}
                        placeholder={textareaPlaceholder}
                        maxLength={maxLength}
                        showMaxLength
                        disabled={disabled}
                        className="w-full"
                    />
                )}
            />
        </div>
    );

export default SelectWithTextarea;
