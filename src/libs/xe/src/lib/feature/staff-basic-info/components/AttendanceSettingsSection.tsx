import { Alert } from '@mayo/mayo-ui-beta/v2';
import { CalendarCheck } from 'lucide-react';
import { useFormContext } from 'react-hook-form';
import SelectWithTextarea from 'xe/feature/staff-basic-info/components/SelectWithTextarea';
import { z } from 'zod';

import { SectionHeader } from '../../../components/SectionHeader';
import { FormSchema } from '../form/schema';

type AttendanceSettingsSectionProps = {
    onboardingDate: string | null | undefined;
    shiftOptions: { label: string; value: string }[];
    attendanceRuleOptions: { label: string; value: string }[];
    handleAttendanceRuleChange: (val: string | null) => void;
};

const AttendanceSettingsSection = ({
    onboardingDate,
    shiftOptions,
    attendanceRuleOptions,
    handleAttendanceRuleChange,
}: AttendanceSettingsSectionProps) => {
    const { control } = useFormContext<z.infer<typeof FormSchema>>();

    return (
        <div>
            <SectionHeader title="出勤設定" />
            <Alert
                alertDesc={`到職時間：${onboardingDate || ''}`}
                alertIcon={<CalendarCheck />}
                showActionButton={false}
                variant="info"
                className="mb-4 mt-2 w-full max-w-full bg-[#f1fcfe]"
            />
            <div className="mb-8 grid grid-cols-4 gap-x-8 gap-y-8">
                <SelectWithTextarea
                    label="工作地區群組"
                    selectName="workAreaGroup"
                    textareaName="workAreaGroupNote"
                    control={control}
                    selectOptions={[]}
                    maxLength={1000}
                    className="w-full"
                    isClearable={false}
                />
                <SelectWithTextarea
                    label="班別異動"
                    selectName="changeRecord.shiftId"
                    textareaName="changeRecord.shiftChangeReason"
                    control={control}
                    selectOptions={shiftOptions}
                    maxLength={1000}
                    className="w-full"
                    isClearable={false}
                />
                <SelectWithTextarea
                    label="出勤規則異動"
                    selectName="changeRecord.attendanceRuleId"
                    textareaName="changeRecord.attendanceRuleChangeReason"
                    control={control}
                    selectOptions={attendanceRuleOptions}
                    maxLength={1000}
                    className="w-full"
                    isClearable={false}
                    onSelectChange={handleAttendanceRuleChange}
                />
                <SelectWithTextarea
                    label="出勤範圍異動"
                    selectName="attendanceScopeChange"
                    textareaName="attendanceScopeChangeNote"
                    control={control}
                    selectOptions={[]}
                    maxLength={1000}
                    className="w-full"
                    isClearable={false}
                />
            </div>
        </div>
    );
};

export default AttendanceSettingsSection;
