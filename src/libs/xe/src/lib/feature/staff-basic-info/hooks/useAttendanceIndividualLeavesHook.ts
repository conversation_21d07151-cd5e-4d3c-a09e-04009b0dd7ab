import { useCallback,useState } from 'react';
import useS<PERSON> from 'swr';
import useSWRMutation from 'swr/mutation';
import ClientService from 'xe/utils/service/clientService';

export interface IndividualLeave {
    individualLeaveTypeId: string;
    leaveCode: string;
    leaveName: string;
    usedMinutes: number;
    leaveSourceType: string;
    leaveSourceTypeName: string;
    seniority: number;
    availableMinutes: number;
    startDate: string;
    endDate: string;
    restrictedDate: string;
    leaveClass: string;
}

export interface IndividualLeavesGetResponse {
    data: IndividualLeave[];
}

export type UpdateIndividualLeavesParams = {
    year: number;
    attendanceRuleId: string;
    leaveCodes: string[];
};

async function updateIndividualLeavesMutation(
    url: string,
    { arg }: { arg: Omit<UpdateIndividualLeavesParams, 'onboardingId'> },
) {
    return ClientService.PUT(url, arg);
}

async function createIndividualLeave(
    onboardingId: string,
    createData: Omit<UpdateIndividualLeavesParams, 'onboardingId'>,
) {
    if (!onboardingId) return;
    const url = `/v2/platform-api/api/HireProcess/Attendance/${onboardingId}/IndividualLeaves/Generate`;
    return ClientService.POST(url, createData);
}

function useAttendanceIndividualLeavesHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);

    const { data, error, mutate } = useSWR<IndividualLeavesGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
    );

    const {
        trigger: updateIndividualLeavesTrigger,
        error: updateError,
        isMutating: isUpdating,
    } = useSWRMutation(endpoint, updateIndividualLeavesMutation, {
        onSuccess: () => mutate(),
        rollbackOnError: true,
    });

    const fetchIndividualLeaves = useCallback((onboardingId: string) => {
        if (onboardingId) {
            setEndpoint(`/v2/platform-api/api/HireProcess/Attendance/${onboardingId}/IndividualLeaves`);
            setShouldFetch(true);
        }
    }, []);

    const refetchIndividualLeaves = useCallback(() => {
        mutate();
    }, [mutate]);

    const updateIndividualLeaves = async (
        onboardingId: string,
        updateData: Omit<UpdateIndividualLeavesParams, 'onboardingId'>,
    ) => {
        if (!onboardingId) return;
        // The endpoint is already set by fetchIndividualLeaves/onboardingId
        return updateIndividualLeavesTrigger(updateData);
    };

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error: error || updateError,
        isMutating: isUpdating,
        update: updateIndividualLeaves,
        fetch: fetchIndividualLeaves,
        refetch: refetchIndividualLeaves,
        create: createIndividualLeave,
    };
}

export default useAttendanceIndividualLeavesHook;
