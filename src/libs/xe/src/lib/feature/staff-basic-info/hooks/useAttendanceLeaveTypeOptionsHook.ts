import { useCallback,useState } from 'react';
import useS<PERSON> from 'swr';
import ClientService from 'xe/utils/service/clientService';

export interface AttendanceLeaveTypeOptionsGetResponse {
    data: {
        leaveTypeOptions: Array<{
            key: string;
            value: string;
        }>;
    };
}

function useAttendanceLeaveTypeOptionsHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);

    const { data, error, mutate } = useSWR<AttendanceLeaveTypeOptionsGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
    );

    const fetchLeaveTypeOptions = useCallback((attendanceRuleId: string) => {
        if (attendanceRuleId) {
            setEndpoint(
                `/v2/platform-api/api/HireProcess/Attendance/Options/LeaveTypes?attendanceRuleId=${encodeURIComponent(attendanceRuleId)}`,
            );
            setShouldFetch(true);
        }
    }, []);

    const clearLeaveTypeOptions = useCallback(() => {
        setShouldFetch(false);
        setEndpoint(null);
    }, []);

    const refetchLeaveTypeOptions = useCallback(() => {
        mutate();
    }, [mutate]);

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error,
        fetch: fetchLeaveTypeOptions,
        refetch: refetchLeaveTypeOptions,
        clear: clearLeaveTypeOptions,
    };
}

export default useAttendanceLeaveTypeOptionsHook;
