import { toast } from '@mayo/mayo-ui-beta/v2';
import { useCallback,useState } from 'react';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import ClientService from 'xe/utils/service/clientService';

export interface AttendanceData {
    onboardingId: string;
    onboardingDate: string;
    changeRecord: {
        shiftId: string;
        shiftName: string;
        shiftChangeReason: string;
        attendanceRuleId: string;
        attendanceRuleName: string;
        attendanceRuleChangeReason: string;
    };
    individualLeaveTypes: Array<{
        individualLeaveTypeId: string;
        leaveCode: string;
        leaveName: string;
        usedMinutes: number;
        leaveSourceType: string;
        leaveSourceTypeName: string;
        seniority: number;
        availableMinutes: number;
        startDate: string;
        endDate: string;
        restrictedDate: string;
        leaveClass: string;
    }>;
    individualLeaveYear: number;
}

export interface AttendanceGetResponse {
    data: AttendanceData;
}

type UpdateAttendanceParams = AttendanceData;

async function updateAttendanceMutation(url: string, { arg }: { arg: Omit<UpdateAttendanceParams, 'onboardingId'> }) {
    // url is the endpoint with onboardingId
    return ClientService.PUT(url, arg);
}

function useAttendanceHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);
    const [hasShownSuccessToast, setHasShownSuccessToast] = useState(false);

    const { data, error, mutate } = useSWR<AttendanceGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
        {
            onSuccess: (_: any) => {
                // Only show toast on initial fetch, not on revalidations
                if (!hasShownSuccessToast && shouldFetch) {
                    setHasShownSuccessToast(true);
                }
            },
            onError: (_: any) => {
                toast.error(`獲取資料失敗`);
                setHasShownSuccessToast(false); // Reset on error
            },
        },
    );

    const {
        trigger: updateAttendanceTrigger,
        error: updateError,
        isMutating: isUpdating,
    } = useSWRMutation(endpoint, updateAttendanceMutation, {
        onSuccess: () => {
            mutate();
            toast.info('更新資料成功');
        },
        onError: (_: any) => {
            toast.error(`更新資料失敗`);
        },
        rollbackOnError: true,
    });

    const fetchAttendance = useCallback((onboardingId: string) => {
        if (onboardingId) {
            setEndpoint(`/v2/platform-api/api/HireProcess/Attendance/${onboardingId}`);
            setShouldFetch(true);
            setHasShownSuccessToast(false); // Reset for new fetch
        }
    }, []);

    const refetchAttendance = useCallback(() => {
        mutate();
    }, [mutate]);

    const updateAttendance = async (onboardingId: string, updateData: Omit<UpdateAttendanceParams, 'onboardingId'>) => {
        if (!onboardingId) return;
        // The endpoint is already set by fetchAttendance/onboardingId
        return updateAttendanceTrigger(updateData);
    };

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error: error || updateError,
        isMutating: isUpdating,
        update: updateAttendance,
        fetch: fetchAttendance,
        refetch: refetchAttendance,
    };
}

export default useAttendanceHook;
