import { useCallback,useState } from 'react';
import useS<PERSON> from 'swr';
import ClientService from 'xe/utils/service/clientService';

export interface AttendanceOptionsGetResponse {
    data: {
        attendanceRuleOptions: Array<{
            key: string;
            value: string;
        }>;
        shiftOptions: Array<{
            key: string;
            value: string;
        }>;
    };
}

function useAttendanceOptionsHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);

    const { data, error, mutate } = useSWR<AttendanceOptionsGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
    );

    const fetchOptions = useCallback((onboardingDate?: string) => {
        let url = '/v2/platform-api/api/HireProcess/Attendance/Options';
        if (onboardingDate) {
            url += `?onboardingDate=${encodeURIComponent(onboardingDate)}`;
        }
        setEndpoint(url);
        setShouldFetch(true);
    }, []);

    const refetchOptions = useCallback(() => {
        mutate();
    }, [mutate]);

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error,
        fetch: fetchOptions,
        refetch: refetchOptions,
    };
}

export default useAttendanceOptionsHook;
