import { zodResolver } from '@hookform/resolvers/zod';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
    Card,
    CardContent,
    Form,
    FormItemContent,
    MaskLoading,
} from '@mayo/mayo-ui-beta/v2';
import { useCallback, useEffect, useMemo,useState } from 'react';
import { FormProvider,useForm } from 'react-hook-form';
import ProgressBar from 'xe/components/ProgressBar';
import CustomLeaveTypeTable from 'xe/feature/staff-basic-info/components/CustomLeaveTypeTable';
import { z } from 'zod';

import { BreadcrumbItem, Breadcrumbs } from '../../components/breadcrumb';
import { SectionHeader } from '../../components/SectionHeader';
import AttendanceSettingsSection from './components/AttendanceSettingsSection';
import { YearSelect } from './components/YearSelect';
import { FormSchema } from './form/schema';
import { initialAttendanceState } from './form/types';
import useAttendanceHook from './hooks/useAttendanceHook';
import useAttendanceIndividualLeavesHook from './hooks/useAttendanceIndividualLeavesHook';
import useAttendanceLeaveTypeOptionsHook from './hooks/useAttendanceLeaveTypeOptionsHook';
import useAttendanceOptionsHook from './hooks/useAttendanceOptionsHook';

const breadcrumbItems: BreadcrumbItem[] = [
    { label: '流程專區', href: '' },
    { label: '人事異動', href: '' },
    { label: '新進人員', href: '' },
    { label: '新進人員資料', href: '', isCurrent: true },
];

// Year select state
const currentYear = new Date().getFullYear();
const defaultYearOptions = [
    { label: currentYear.toString(), value: currentYear.toString() },
    { label: (currentYear + 1).toString(), value: (currentYear + 1).toString() },
];

const StaffBasicInfo = ({ onboardingId }: { onboardingId: string }) => {
    const [selectedYear, setSelectedYear] = useState(defaultYearOptions[0].value);
    const [yearOptions, setYearOptions] = useState(defaultYearOptions);

    const form = useForm<z.infer<typeof FormSchema>>({
        resolver: zodResolver(FormSchema),
        defaultValues: initialAttendanceState,
    });

    const { reset, getValues, control } = form;

    // Get values for display purposes
    const onboardingDate = getValues('onboardingDate');
    const changeRecord = getValues('changeRecord');

    const attendanceRuleName = changeRecord?.attendanceRuleName || '';
    // const attendanceRuleChangeReason = changeRecord?.attendanceRuleChangeReason || '';
    const attendanceRuleId = changeRecord?.attendanceRuleId || '';
    const shiftName = changeRecord?.shiftName || '';
    const shiftId = changeRecord?.shiftId || '';

    const onSubmit = async (values: any) => {
        // Combine both arrays for API submission
        const allLeaveTypes = [
            ...(values.individualLeaveTypesGeneral || []),
            ...(values.individualLeaveTypesSpecial || []),
        ];
        const submitValues = {
            ...values,
            individualLeaveTypes: allLeaveTypes,
        };
        console.log('submitValues', submitValues);
        // send submitValues to API
    };

    const {
        data: attendanceData,
        isLoading: isAttendanceLoading,
        // error: attendanceError,
        isMutating: isAttendanceMutating,
        fetch: fetchAttendance,
    } = useAttendanceHook();
    const {
        data: optionsData,
        isLoading: isOptionsLoading,
        // error: optionsError,
        fetch: fetchOptions,
    } = useAttendanceOptionsHook();
    const {
        data: leaveTypeOptionsData,
        isLoading: isLeaveTypeOptionsLoading,
        // error: leaveTypeOptionsError,
        fetch: fetchLeaveTypeOptions,
        clear: clearLeaveTypeOptions,
    } = useAttendanceLeaveTypeOptionsHook();
    const {
        // data: individualLeavesData,
        // error: individualLeavesError,
        isMutating: isIndividualLeavesMutating,
        create: createIndividualLeaves,
        // refetch: refetchIndividualLeaves,
    } = useAttendanceIndividualLeavesHook();

    // Combine loading and error states
    const isLoading =
        isAttendanceLoading ||
        isAttendanceMutating ||
        isOptionsLoading ||
        isLeaveTypeOptionsLoading ||
        isIndividualLeavesMutating;
    // const error = attendanceError || optionsError || leaveTypeOptionsError || individualLeavesError;

    const options = optionsData?.data;
    const leaveTypeOptions = leaveTypeOptionsData?.data;
    // const individualLeaves = individualLeavesData?.data;

    // Memoized options for better performance
    const shiftOptions = useMemo(() => {
        if (Array.isArray(options?.shiftOptions) && options.shiftOptions.length > 0) {
            return options.shiftOptions.map((option: any) => ({
                label: String(option.value),
                value: String(option.key),
            }));
        }
        return [
            {
                label: String(shiftName),
                value: String(shiftId),
            },
        ];
    }, [options?.shiftOptions, shiftName, shiftId]);

    const attendanceRuleOptions = useMemo(() => {
        if (Array.isArray(options?.attendanceRuleOptions) && options.attendanceRuleOptions.length > 0) {
            return options.attendanceRuleOptions.map((option: any) => ({
                label: String(option.value),
                value: String(option.key),
            }));
        }
        return [
            {
                label: String(attendanceRuleName),
                value: String(attendanceRuleId),
            },
        ];
    }, [options?.attendanceRuleOptions, attendanceRuleName, attendanceRuleId]);

    const generalLeaveTypeOptions = useMemo(() => {
        if (
            Array.isArray(leaveTypeOptions) &&
            leaveTypeOptions.filter((option: any) => option.leaveClass === 'general').length > 0
        ) {
            return leaveTypeOptions
                .filter((option: any) => option.leaveClass === 'general')
                .map((option: any) => ({
                    label: option.leaveName,
                    value: option.leaveCode,
                }));
        }
        return [];
    }, [leaveTypeOptions]);

    const specialLeaveTypeOptions = useMemo(() => {
        if (
            Array.isArray(leaveTypeOptions) &&
            leaveTypeOptions.filter((option: any) => option.leaveClass === 'special').length > 0
        ) {
            return leaveTypeOptions
                .filter((option: any) => option.leaveClass === 'special')
                .map((option: any) => ({
                    label: option.leaveName,
                    value: option.leaveCode,
                }));
        }
        return [];
    }, [leaveTypeOptions]);

    // Memoized onChange handlers
    const handleAttendanceRuleChange = useCallback(
        (val: string | null) => {
            if (val) {
                fetchLeaveTypeOptions(val);
            } else {
                clearLeaveTypeOptions();
            }
        },
        [fetchLeaveTypeOptions, clearLeaveTypeOptions],
    );

    const handleYearChange = useCallback(
        (option: { label: string; value: string } | null) => {
            setSelectedYear(option ? option.value : yearOptions[0].value);
        },
        [yearOptions],
    );

    useEffect(() => {
        fetchAttendance(onboardingId);
        fetchOptions();
    }, []);

    useEffect(() => {
        if (attendanceData) {
            const apiLeaveTypes = attendanceData.data.individualLeaveTypes || [];
            const general = apiLeaveTypes.filter((item: any) => item.leaveClass === 'general');
            const special = apiLeaveTypes.filter((item: any) => item.leaveClass === 'special');
            reset({
                ...attendanceData.data,
                individualLeaveTypesGeneral: general,
                individualLeaveTypesSpecial: special,
            });
            setYearOptions([
                {
                    label: attendanceData.data.individualLeaveYear.toString(),
                    value: attendanceData.data.individualLeaveYear.toString(),
                },
                {
                    label: (attendanceData.data.individualLeaveYear + 1).toString(),
                    value: (attendanceData.data.individualLeaveYear + 1).toString(),
                },
            ]);
            setSelectedYear(attendanceData.data.individualLeaveYear.toString());
        }
    }, [attendanceData, reset]);

    // Fetch leaveTypeOptions on initial load and whenever attendanceRuleId changes from backend
    useEffect(() => {
        const attendanceRuleId = attendanceData?.data?.changeRecord?.attendanceRuleId;
        if (attendanceRuleId) {
            fetchLeaveTypeOptions(attendanceRuleId);
        }
    }, [attendanceData?.data?.changeRecord?.attendanceRuleId]);

    useEffect(() => {
        if (
            leaveTypeOptions &&
            Array.isArray(leaveTypeOptions) &&
            leaveTypeOptions.length > 0 &&
            getValues('changeRecord.attendanceRuleName') &&
            onboardingId
        ) {
            const year = Number(selectedYear) || new Date().getFullYear();
            const attendanceRuleId = getValues('changeRecord.attendanceRuleId') || '';
            const leaveCodes = leaveTypeOptions.map((opt) => opt.leaveCode);
            createIndividualLeaves(onboardingId, { year, attendanceRuleId, leaveCodes }).then(() => {
                // refetchIndividualLeaves();
            });
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [leaveTypeOptionsData]);

    return (
        <div className="flex flex-col gap-4">
            {isLoading && <MaskLoading />}

            <Breadcrumbs items={breadcrumbItems} />
            <FormProvider {...form}>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)}>
                        <div className="flex flex-col gap-4">
                            <h1 className="text-xl font-bold">新進人員資料</h1>
                            <ProgressBar onboardingId={onboardingId} onSave={form.handleSubmit(onSubmit)} />
                            {/* 內容卡片 */}
                            <Card className="scrollbar-none h-full w-full overflow-scroll px-10 py-12">
                                <CardContent className="flex flex-col gap-8">
                                    <AttendanceSettingsSection
                                        onboardingDate={onboardingDate}
                                        shiftOptions={shiftOptions}
                                        attendanceRuleOptions={attendanceRuleOptions}
                                        handleAttendanceRuleChange={handleAttendanceRuleChange}
                                    />
                                    <div>
                                        <SectionHeader title="休假時數" />
                                        <FormItemContent
                                            name="individualLeaveYear"
                                            label="年度"
                                            fieldClassName="mb-6"
                                            control={control}
                                            renderField={({ field }) => (
                                                <YearSelect
                                                    field={field}
                                                    yearOptions={yearOptions}
                                                    handleYearChange={handleYearChange}
                                                />
                                            )}
                                        />
                                        <Accordion className="w-full" type="multiple">
                                            <AccordionItem value="item-1" defaultOpen>
                                                <AccordionTrigger className="px-2 py-4 text-base font-normal">
                                                    一般假別
                                                </AccordionTrigger>
                                                <AccordionContent>
                                                    <CustomLeaveTypeTable
                                                        name="individualLeaveTypesGeneral"
                                                        isFixedTable
                                                        defaultLeaveClass="general"
                                                        leaveTypeOptions={generalLeaveTypeOptions}
                                                    />
                                                </AccordionContent>
                                            </AccordionItem>
                                            <AccordionItem value="item-2">
                                                <AccordionTrigger className="px-2 py-4 text-base font-normal">
                                                    法定假別
                                                </AccordionTrigger>
                                                <AccordionContent>
                                                    {/* <LeaveTypeTable data={leaveTypeMockData} /> */}
                                                </AccordionContent>
                                            </AccordionItem>
                                            <AccordionItem value="item-3" defaultOpen>
                                                <AccordionTrigger className="px-2 py-4 text-base font-normal">
                                                    公司福利假別
                                                </AccordionTrigger>
                                                <AccordionContent>
                                                    <CustomLeaveTypeTable
                                                        name="individualLeaveTypesSpecial"
                                                        defaultLeaveClass="special"
                                                        leaveTypeOptions={specialLeaveTypeOptions}
                                                    />
                                                </AccordionContent>
                                            </AccordionItem>
                                        </Accordion>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </form>
                </Form>
            </FormProvider>
        </div>
    );
};

export default StaffBasicInfo;
