/* eslint-disable no-console */

import { Button } from '@mayo/mayo-ui-beta/v2';
import { useAtom } from 'jotai';
import { useTranslations } from 'next-intl';
import useSWR from 'swr';
import { decrement, increment } from 'xe/redux/slice/commonSlice';
import { useAppDispatch, useAppSelector } from 'xe/redux/store';

import { envsAtom } from '../utils/jotai/env';
import { jotaiStore } from '../utils/jotai/store';
import ClientService from '../utils/service/clientService';
import useCountStore from '../utils/zustand/count';

interface User {
    isVerify: boolean;
    userModule: string[];
    userName: string;
    userRole: string[];
    IsSupervisor: boolean;
    IsSecretary: boolean;
    PersonalPicture: string;
    EmployeeId: string;
    CompanyId: string;
}

export function Demo() {
    const t = useTranslations();

    // Jotai state
    const [envs, setEnvs] = useAtom(envsAtom, { store: jotaiStore });

    // SWR data fetching
    const { data: user } = useSWR<User>('/fd/api/userInfo', ClientService.GET, {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
    });

    // Navigation hook

    // Zustand state
    const count = useCountStore((state: any) => state.count);
    const total = useCountStore((state: any) => state.total);
    const zustandIncrement = useCountStore((state: any) => state.increment);
    const zustandDecrement = useCountStore((state: any) => state.decrement);
    const setCount = useCountStore((state: any) => state.setCount);
    const setTotal = useCountStore((state: any) => state.setTotal);

    // Redux state
    const value = useAppSelector((state) => state.common.value);
    const dispatch = useAppDispatch();

    console.log(value);
    // Log user data
    console.log(user);

    return (
        <div className="space-y-8 p-4">
            {/* Welcome Section */}
            <section>
                <h1 className="mb-4 text-2xl font-bold">Welcome to ModuleXe!</h1>
                <Button>{t('AH_ForgetPassword')}</Button>
            </section>

            {/* Jotai Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">Jotai Demo</h1>
                <p>Locale: {JSON.stringify(envs)}</p>
                <button
                    type="button"
                    className="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                    onClick={() => setEnvs({ PLATFORM: 'zh-TW' })}
                >
                    Set Envs
                </button>
            </section>

            {/* Zustand Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">Zustand Demo</h1>
                <p>Count: {count}</p>
                <p>Total: {total}</p>
                <div className="space-x-2">
                    <button
                        type="button"
                        className="rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
                        onClick={() => zustandIncrement()}
                    >
                        Increment
                    </button>
                    <button
                        type="button"
                        className="rounded bg-red-500 px-4 py-2 text-white hover:bg-red-600"
                        onClick={() => zustandDecrement()}
                    >
                        Decrement
                    </button>
                    <button
                        type="button"
                        className="rounded bg-yellow-500 px-4 py-2 text-white hover:bg-yellow-600"
                        onClick={() => setCount(count + 1)}
                    >
                        Set Count
                    </button>
                    <button
                        type="button"
                        className="rounded bg-purple-500 px-4 py-2 text-white hover:bg-purple-600"
                        onClick={() => setTotal(total + 1)}
                    >
                        Set Total
                    </button>
                </div>
            </section>

            {/* Navigation Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">useNavigation Demo</h1>
                <div className="space-x-2">
                    <button
                        type="button"
                        className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
                        onClick={() => null}
                    >
                        Push
                    </button>
                    <button
                        type="button"
                        className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
                        onClick={() => null}
                    >
                        Replace
                    </button>
                    <button
                        type="button"
                        className="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600"
                        onClick={() => null}
                    >
                        Back
                    </button>
                </div>
            </section>

            {/* Redux Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">Redux Demo</h1>
                <p>Value: {value}</p>
                <button type="button" onClick={() => dispatch(increment())}>
                    Increment
                </button>
                <button type="button" onClick={() => dispatch(decrement())}>
                    Decrement
                </button>
            </section>
        </div>
    );
}

export default Demo;
