import { SidebarInset, SidebarProvider } from '@mayo/mayo-ui-beta/v2';
import classNames from 'classnames';
import { usePathname, useSearchParams } from 'next/navigation';

import { WHITE_LIST } from '../../../utils/config';
import { AppSidebar } from './app-sidebar';
import { SiteHeader } from './site-header';

// Constants
const LAYOUT_CONSTANTS = {
    FOOTER_HEIGHT: '48px',
    Z_INDEX: 9999,
} as const;

const IFRAME_MODE_PARAMS = ['isIframeMode', 'isiframemode', 'isIframemode'] as const;

const EXCLUDED_PATHS = ['/mayo-form', '/custom-report'] as const;

function Sidebar({ children }: { children: React.ReactNode }) {
    const pathname = usePathname();
    const searchParams = useSearchParams();

    // Check if in iframe mode
    const isIframeMode = IFRAME_MODE_PARAMS.some((param) => searchParams.get(param) === 'true');

    // Check if sidebar should be hidden
    const hideSidebar = [...WHITE_LIST, ...EXCLUDED_PATHS].some((path) => pathname.includes(path)) || isIframeMode;

    // Generate content area classes
    const getContentClasses = () => {
        const baseClasses = 'w-full h-full';
        const marginTop = 'mt-[var(--header-height)]';

        if (isIframeMode) {
            return classNames(baseClasses, marginTop);
        }

        return classNames(baseClasses, marginTop, `mb-[${LAYOUT_CONSTANTS.FOOTER_HEIGHT}]`, {
            'p-4': !hideSidebar,
        });
    };

    // Render footer conditionally
    const renderFooter = () => {
        if (hideSidebar || isIframeMode) return null;

        return (
            <footer className="fixed bottom-0 right-0 z-20 flex h-12 w-full items-center justify-end bg-[rgb(240_240_241)] px-4 py-4">
                <p className="break-words text-end text-sm text-gray-500">
                    Copyright © 2025 MAYO Human Capital Inc. All rights reserved./隱私權政策/About Us
                </p>
            </footer>
        );
    };

    return (
        <div>
            <SidebarProvider>
                {/* Fixed Header */}
                <div className={`fixed left-0 top-0 z-[${LAYOUT_CONSTANTS.Z_INDEX}] w-full`}>
                    <SiteHeader hideSidebar={hideSidebar} />
                </div>

                {/* Main Content Area */}
                <div className="flex flex-1">
                    {!hideSidebar && <AppSidebar />}

                    <SidebarInset className="h-dvh bg-[rgb(240_240_241)]">
                        <div className={getContentClasses()}>{children}</div>

                        {renderFooter()}
                    </SidebarInset>
                </div>
            </SidebarProvider>
        </div>
    );
}

export default Sidebar;
