import { Button, CompoundDropdownMenu, CompoundDropdownMenuItem, SidebarMenuButton } from '@mayo/mayo-ui-beta/v2';
import { ChevronsUpDown, ContactRound, FileText } from 'lucide-react';

const FunctionDropdown = () => {
    const menuItems: CompoundDropdownMenuItem[] = [
        {
            id: 'label-1',
            type: 'label',
            label: 'Feature Recommendations',
        },
        {
            id: 'func-1',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>MAYO Form</span>
                </div>
            ),
        },
        {
            id: 'xe-2',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>MAYO Report</span>
                </div>
            ),
        },
        {
            id: 'xe-3',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>Arranging work timetable</span>
                </div>
            ),
        },
        {
            id: 'sep-1',
            type: 'separator',
        },
        {
            id: 'xe-1',
            label: 'Apollo XE',
            type: 'label',
        },
        {
            id: 'xe-func-1',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>Foundation</span>
                </div>
            ),
        },
        {
            id: 'xe-func-2',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>Attendance</span>
                </div>
            ),
        },
        {
            id: 'xe-func-3',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>Payroll</span>
                </div>
            ),
        },
        {
            id: 'xe-func-4',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>Recruitment</span>
                </div>
            ),
        },
        {
            id: 'xe-func-5',
            type: 'item',
            label: (
                <div className="flex w-full items-center gap-2">
                    <Button className="h-8 w-6 rounded-lg bg-transparent hover:bg-transparent">
                        <FileText size={16} />
                    </Button>
                    <span>Dashboard</span>
                </div>
            ),
        },
    ];

    return (
        <CompoundDropdownMenu
            dropdownMenuContentProps={{
                className: 'w-[228px]',
            }}
            menuItems={menuItems}
            triggerLabel="open"
            placement="left-start"
            triggerProps={{ variant: 'outline', className: 'w-52' }}
            trigger={
                <div className="z-10 flex items-center justify-center">
                    <SidebarMenuButton>
                        <div className="flex w-full items-center justify-between">
                            <div className="ml-[-4px] flex items-center gap-2">
                                <Button size="icon" variant="default" color="primary">
                                    <ContactRound size={16} />
                                </Button>
                                <span>Foundation</span>
                            </div>

                            <ChevronsUpDown size={16} />
                        </div>
                    </SidebarMenuButton>
                </div>
            }
        />
    );
};

export default FunctionDropdown;
