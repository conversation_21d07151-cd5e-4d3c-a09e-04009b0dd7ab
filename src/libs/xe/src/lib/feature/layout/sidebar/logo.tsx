import Image from 'next/image';
import React from 'react';
import LogoImage from 'xe/assets/images/xe-logo-blue.svg?url';

interface LogoProps {
    className?: string;
    alt?: string;
}

/**
 * Logo 組件，用於顯示 XE 的 logo
 *
 * @param {string} className - 可選的 CSS 類名
 * @param {string} alt - 可選的替代文本
 * @returns {JSX.Element} Logo 組件
 */
export const Logo: React.FC<LogoProps> = () => (
    <div>
        <Image src={LogoImage.src} alt="logo" width={100} height={100} />
    </div>
);

export default Logo;
