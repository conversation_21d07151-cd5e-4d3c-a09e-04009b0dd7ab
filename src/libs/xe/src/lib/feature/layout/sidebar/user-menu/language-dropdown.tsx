import { Button, CompoundDropdownMenu, CompoundDropdownMenuItem } from '@mayo/mayo-ui-beta/v2';
import axios from 'axios';
import Cookies from 'js-cookie';
import { Globe } from 'lucide-react';
import { useState, useTransition } from 'react';

import { COOKIE_NAMES, defaultLocale } from '../../../../utils/config';
import { deleteCookie, setCookie } from '../../../../utils/cookies';

const LanguageDropdown = () => {
    // 從 cookie 中讀取當前語系
    const getCurrentLanguage = () => Cookies.get(COOKIE_NAMES.LOCALE) || defaultLocale;

    const [locale] = useState(getCurrentLanguage());

    const [, startTransition] = useTransition();

    const handleLanguageChange = async (newLocale: string) => {
        startTransition(() => {
            // 清除 form auth cookie
            deleteCookie(COOKIE_NAMES.AUTH, '.mayohr.com');
            deleteCookie(COOKIE_NAMES.REFRESH_TOKEN, '.mayohr.com');

            setCookie(COOKIE_NAMES.LOCALE, newLocale, '.mayohr.com');
            axios.defaults.headers.common['Accept-Language'] = newLocale;
            window.location.reload();
        });
    };

    const menuItems: CompoundDropdownMenuItem[] = [
        { id: 'label-language', type: 'label', label: 'Language Family' },
        { id: 'sep-1', type: 'separator' },
        {
            id: 'radio-language',
            type: 'radioGroup',
            label: 'languageSelection',
            value: locale,
            onValueChange: handleLanguageChange,
            items: [
                { id: 'radio-en', label: 'English', value: 'en-us' },
                { id: 'radio-zh-TW', label: '繁體中文', value: 'zh-TW' },
                { id: 'radio-zh-CN', label: '简体中文', value: 'zh-CN' },
            ],
        },
    ];

    return (
        <CompoundDropdownMenu
            menuItems={menuItems}
            triggerLabel="open"
            dropdownMenuContentProps={{
                className: 'border-sidebar-border',
            }}
            placement="bottom-end"
            triggerProps={{ variant: 'outline', className: 'w-52' }}
            trigger={
                <Button size="icon" variant="default" className="bg-foreground hover:bg-accent">
                    <Globe height={18} width={18} />
                </Button>
            }
        />
    );
};

export default LanguageDropdown;
