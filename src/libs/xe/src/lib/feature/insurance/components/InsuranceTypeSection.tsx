import { Alert, FormItemContent,RadioGroupComponent, Select, Textarea } from '@mayo/mayo-ui-beta/v2';
import { CalendarCheck } from 'lucide-react';
import React from 'react';
import { useFormContext } from 'react-hook-form';

import { SelectOption } from '../../../utils/enum/select';
import ExemptionStatus from './ExemptionStatus';

interface InsuranceTypeSectionProps {
    sectionKey: 'laborInsurance' | 'occupationInsurance' | 'healthInsurance';
    title: string;
}

const InsuranceTypeSection: React.FC<InsuranceTypeSectionProps> = ({ sectionKey, title }) => {
    const { control } = useFormContext();
    return (
        <div>
            <div className="mb-4 text-lg font-bold">{title}</div>
            <div className="mb-6 border-b border-gray-200" />
            <Alert
                alertDesc="生效日：2025/05/05"
                alertIcon={<CalendarCheck />}
                showActionButton={false}
                variant="info"
                className="mb-4 mt-2 w-full max-w-full bg-[#f1fcfe]"
            />
            <div className="flex w-full flex-row gap-8">
                {/* Left: core fields */}
                <div className="flex flex-1 flex-col gap-6">
                    {/* Row 1: 狀態 | 投保金額 */}
                    <div className="flex w-full flex-row gap-6">
                        <div className="flex flex-1 flex-col">
                            <FormItemContent
                                name={`${sectionKey}.status`}
                                control={control}
                                label="狀態"
                                labelClassName="mb-2 text-[14px] font-normal"
                                fieldClassName="w-full"
                                renderField={({ field }: { field: any }) => {
                                    const uniquePrefix = `${sectionKey}-status`;
                                    const baseOptions =
                                        sectionKey === 'healthInsurance'
                                            ? [
                                                  { label: '加保', value: '加保' },
                                                  { label: '退保', value: '退保' },
                                                  { label: '停保', value: '停保' },
                                              ]
                                            : [
                                                  { label: '加保', value: '加保' },
                                                  { label: '退保', value: '退保' },
                                              ];
                                    return (
                                        <RadioGroupComponent
                                            value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                            isHorizontal
                                            className="text-[14px]"
                                            onValueChange={(val: string) =>
                                                field.onChange(val.substring(uniquePrefix.length + 1))
                                            }
                                            radioOptions={baseOptions.map((o) => ({
                                                ...o,
                                                value: `${uniquePrefix}-${o.value}`,
                                            }))}
                                        />
                                    );
                                }}
                            />
                        </div>
                        <div className="flex flex-1 flex-col">
                            <FormItemContent
                                name={`${sectionKey}.amount`}
                                control={control}
                                label="投保金額"
                                labelClassName="mb-2 text-[14px] font-normal"
                                fieldClassName="w-full"
                                renderField={({ field }: { field: any }) => (
                                    <Select
                                        options={[]}
                                        value={field.value ? { label: field.value, value: field.value } : null}
                                        onChange={(option: SelectOption | null) =>
                                            field.onChange(option ? option.value : '')
                                        }
                                        placeholder="請選擇"
                                        isClearable
                                        className="w-full"
                                    />
                                )}
                            />
                        </div>
                    </div>
                    {/* Row 2: 投保單位 (full width) */}
                    <div className="flex w-full flex-col">
                        <FormItemContent
                            name={`${sectionKey}.unit`}
                            control={control}
                            label="投保單位"
                            labelClassName="mb-2 text-[14px] font-normal"
                            fieldClassName="w-full"
                            renderField={({ field }: { field: any }) => (
                                <Select
                                    options={[
                                        {
                                            label: '54510058 鼎恆數位科技股份有限公司',
                                            value: '54510058 鼎恆數位科技股份有限公司',
                                        },
                                    ]}
                                    value={field.value ? { label: field.value, value: field.value } : null}
                                    onChange={(option: SelectOption | null) =>
                                        field.onChange(option ? option.value : '')
                                    }
                                    placeholder="請選擇"
                                    isClearable={false}
                                    className="w-full"
                                />
                            )}
                        />
                    </div>
                    {/* Row 3: 籍別 | 投保身份 */}
                    <div className="flex w-full flex-row gap-6">
                        <div className="flex flex-1 flex-col">
                            <FormItemContent
                                name={`${sectionKey}.type`}
                                control={control}
                                label="籍別"
                                labelClassName="mb-2 text-[14px] font-normal"
                                fieldClassName="w-full"
                                renderField={({ field }: { field: any }) => {
                                    if (sectionKey === 'healthInsurance') {
                                        const uniquePrefix = `${sectionKey}-type`;
                                        return (
                                            <RadioGroupComponent
                                                value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                                isHorizontal
                                                className="text-[14px]"
                                                onValueChange={(val: string) =>
                                                    field.onChange(val.substring(uniquePrefix.length + 1))
                                                }
                                                radioOptions={[
                                                    { label: '本國人', value: `${uniquePrefix}-本國人` },
                                                    { label: '外籍', value: `${uniquePrefix}-外籍` },
                                                ]}
                                            />
                                        );
                                    }
                                    return (
                                        <Select
                                            options={[{ label: '本國人', value: '本國人' }]}
                                            value={field.value ? { label: field.value, value: field.value } : null}
                                            onChange={(option: SelectOption | null) =>
                                                field.onChange(option ? option.value : '')
                                            }
                                            placeholder="請選擇"
                                            isClearable={false}
                                            className="w-full"
                                        />
                                    );
                                }}
                            />
                        </div>
                        <div className="flex flex-1 flex-col">
                            <FormItemContent
                                name={`${sectionKey}.identity`}
                                control={control}
                                label="投保身份"
                                labelClassName="mb-2 text-[14px] font-normal"
                                fieldClassName="w-full"
                                renderField={({ field }: { field: any }) => {
                                    if (sectionKey === 'healthInsurance') {
                                        const uniquePrefix = `${sectionKey}-identity`;
                                        return (
                                            <RadioGroupComponent
                                                value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                                isHorizontal
                                                className="text-[14px]"
                                                onValueChange={(val: string) =>
                                                    field.onChange(val.substring(uniquePrefix.length + 1))
                                                }
                                                radioOptions={[
                                                    { label: '雇主', value: `${uniquePrefix}-雇主` },
                                                    { label: '一般', value: `${uniquePrefix}-一般` },
                                                ]}
                                            />
                                        );
                                    }
                                    return (
                                        <Select
                                            options={[]}
                                            value={field.value ? { label: field.value, value: field.value } : null}
                                            onChange={(option: SelectOption | null) =>
                                                field.onChange(option ? option.value : '')
                                            }
                                            placeholder="請選擇"
                                            isClearable
                                            className="w-full"
                                        />
                                    );
                                }}
                            />
                        </div>
                    </div>
                    {/* Row 4: 雇主負擔 | 個人負擔 */}
                    <div className="flex w-full flex-row gap-6">
                        <div className="flex flex-1 flex-col">
                            <FormItemContent
                                name={`${sectionKey}.employerBurden`}
                                control={control}
                                label="雇主負擔"
                                labelClassName="mb-2 text-[14px] font-normal"
                                fieldClassName="w-full"
                                renderField={({ field }: { field: any }) => {
                                    const uniquePrefix = `${sectionKey}-employerBurden`;
                                    return (
                                        <RadioGroupComponent
                                            value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                            isHorizontal
                                            className="text-[14px]"
                                            onValueChange={(val: string) => {
                                                field.onChange(val.substring(uniquePrefix.length + 1));
                                            }}
                                            radioOptions={[
                                                { label: '計算', value: `${uniquePrefix}-計算` },
                                                { label: '不計算', value: `${uniquePrefix}-不計算` },
                                            ]}
                                        />
                                    );
                                }}
                            />
                        </div>
                        <div className="flex flex-1 flex-col">
                            <FormItemContent
                                name={`${sectionKey}.personalBurden`}
                                control={control}
                                label="個人負擔"
                                labelClassName="mb-2 text-[14px] font-normal"
                                fieldClassName="w-full"
                                renderField={({ field }: { field: any }) => {
                                    const uniquePrefix = `${sectionKey}-personalBurden`;
                                    return (
                                        <RadioGroupComponent
                                            value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                            isHorizontal
                                            className="text-[14px]"
                                            onValueChange={(val: string) => {
                                                field.onChange(val.substring(uniquePrefix.length + 1));
                                            }}
                                            radioOptions={[
                                                { label: '計算', value: `${uniquePrefix}-計算` },
                                                { label: '不計算', value: `${uniquePrefix}-不計算` },
                                            ]}
                                        />
                                    );
                                }}
                            />
                        </div>
                    </div>
                    {/* Row 5: 備註 */}
                    <div className="flex w-full flex-row gap-6">
                        <div className="flex flex-1 flex-col">
                            <FormItemContent
                                name={`${sectionKey}.notes`}
                                control={control}
                                label="備註"
                                labelClassName="mb-2 text-[14px] font-normal"
                                fieldClassName="w-full"
                                renderField={({ field }: { field: any }) => (
                                    <Textarea
                                        value={field.value}
                                        onChange={field.onChange}
                                        placeholder="請輸入"
                                        maxLength={250}
                                        showMaxLength
                                        disabled={false}
                                        className="w-full"
                                    />
                                )}
                            />
                        </div>
                    </div>
                </div>
                {/* Right: ExemptionStatus */}
                <div className="flex min-w-[340px] max-w-[33%] flex-col">
                    <ExemptionStatus section={sectionKey} />
                </div>
            </div>
        </div>
    );
};

export default InsuranceTypeSection;
