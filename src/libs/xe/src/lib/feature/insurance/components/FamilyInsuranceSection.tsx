import { But<PERSON>, DatePicker, FormItemContent, Input, Select } from '@mayo/mayo-ui-beta/v2';
import { PlusCircle, Trash2 } from 'lucide-react';
import { useFieldArray, useFormContext } from 'react-hook-form';

import { SectionHeader } from '../../../components/SectionHeader';
import { SelectOption } from '../../../utils/enum/select';
import { InsuranceFormValues } from '../form/types';

const FamilyInsuranceSection = () => {
    const { control } = useFormContext<InsuranceFormValues>();
    const { fields, append, remove } = useFieldArray({
        control,
        name: 'dependents',
    });

    return (
        <div>
            <SectionHeader title="扶養親屬" />
            {/* 扶養親屬動態表單 */}
            <div className="flex flex-col gap-6">
                {fields.map((item, index) => (
                    <div key={item.id} className="border-primary flex-1 rounded-md border-l-4 bg-white py-6 pl-6">
                        <div className="mb-6 flex items-center">
                            <span className="mr-2 text-[16px] font-normal" style={{ color: '#26abe3' }}>
                                親屬 {index + 1}
                            </span>
                            <Button
                                type="button"
                                size="icon"
                                disabled={fields.length === 1}
                                className={`ml-2 border-none bg-transparent p-0 shadow-none hover:bg-transparent focus:bg-transparent active:border-none active:bg-transparent ${fields.length === 1 ? 'pointer-events-none opacity-50' : 'opacity-100'}`}
                                onClick={() => remove(index)}
                            >
                                <Trash2 size={18} style={{ color: '#dc2626' }} />
                            </Button>
                        </div>
                        <div className="flex w-full flex-row gap-8">
                            {/* LEFT HALF */}
                            <div className="flex flex-1 flex-col gap-6">
                                {/* Row 1: 姓名, 稱謂 */}
                                <div className="flex w-full flex-row gap-6">
                                    <div className="flex flex-1 flex-col">
                                        <FormItemContent
                                            name={`dependents.${index}.name`}
                                            fieldClassName="w-full"
                                            control={control}
                                            label="姓名"
                                            labelClassName="mb-2 text-[14px] font-normal"
                                            renderField={({ field }) => (
                                                <Input {...field} placeholder="請輸入" className="w-full" />
                                            )}
                                        />
                                    </div>
                                    <div className="flex flex-1 flex-col">
                                        <FormItemContent
                                            name={`dependents.${index}.relation`}
                                            fieldClassName="w-full"
                                            control={control}
                                            label="稱謂"
                                            labelClassName="mb-2 text-[14px] font-normal"
                                            renderField={({ field }) => (
                                                <Input {...field} placeholder="請輸入" className="w-full" />
                                            )}
                                        />
                                    </div>
                                </div>
                                {/* Row 2: 地址 */}
                                <div className="flex w-full flex-col">
                                    <FormItemContent
                                        name={`dependents.${index}.address`}
                                        fieldClassName="w-full"
                                        control={control}
                                        label="地址"
                                        labelClassName="mb-2 text-[14px] font-normal"
                                        renderField={({ field }) => (
                                            <Input {...field} placeholder="請輸入" className="w-full" />
                                        )}
                                    />
                                </div>
                            </div>
                            {/* RIGHT HALF */}
                            <div className="flex flex-1 flex-col gap-6">
                                {/* Row 1: 生日, 證件號碼 */}
                                <div className="flex w-full flex-row gap-6">
                                    <div className="flex flex-1 flex-col">
                                        <FormItemContent
                                            name={`dependents.${index}.birthday`}
                                            fieldClassName="w-full"
                                            control={control}
                                            label="生日"
                                            labelClassName="mb-2 text-[14px] font-normal"
                                            renderField={({ field }) => {
                                                const stringValue = String(field.value);
                                                const displayValue = ['null', 'undefined', 'false'].includes(
                                                    stringValue,
                                                )
                                                    ? ''
                                                    : stringValue;
                                                return (
                                                    <DatePicker
                                                        value={displayValue}
                                                        onChange={field.onChange}
                                                        placeholder="YYYY/MM/DD"
                                                        className="w-full"
                                                    />
                                                );
                                            }}
                                        />
                                    </div>
                                    <div className="flex flex-1 flex-col">
                                        <FormItemContent
                                            name={`dependents.${index}.idNumber`}
                                            fieldClassName="w-full"
                                            control={control}
                                            label="證件號碼"
                                            labelClassName="mb-2 text-[14px] font-normal"
                                            renderField={({ field }) => (
                                                <Input {...field} placeholder="請輸入" className="w-full" />
                                            )}
                                        />
                                    </div>
                                </div>
                                {/* Row 2: 符合條件 (full width of right half) */}
                                <div className="flex w-full flex-col">
                                    <FormItemContent
                                        name={`dependents.${index}.condition`}
                                        fieldClassName="w-full"
                                        control={control}
                                        label="符合條件"
                                        labelClassName="mb-2 text-[14px] font-normal"
                                        renderField={({ field }) => (
                                            <Select
                                                placeholder="請選擇"
                                                className="w-full"
                                                options={[]}
                                                value={
                                                    field.value
                                                        ? {
                                                              label: field.value,
                                                              value: field.value,
                                                          }
                                                        : null
                                                }
                                                onChange={(option: SelectOption | null) => {
                                                    field.onChange(option ? option.value : '');
                                                }}
                                                isClearable
                                            />
                                        )}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
                <Button
                    type="button"
                    color="primary"
                    variant="ghost"
                    className="mt-2 flex w-fit items-center gap-1 border-none bg-transparent p-0 text-[14px] font-normal shadow-none hover:bg-transparent focus:bg-transparent"
                    style={{ color: '#26abe3' }}
                    onClick={() =>
                        append({
                            name: '',
                            relation: '',
                            birthday: '',
                            idNumber: '',
                            address: '',
                            condition: '',
                        })
                    }
                >
                    <PlusCircle className="mr-1" size={18} style={{ color: '#26abe3' }} /> 新增
                </Button>
            </div>
        </div>
    );
};

export default FamilyInsuranceSection;
