import { Alert, FormItemContent, RadioGroupComponent, Select, Textarea } from '@mayo/mayo-ui-beta/v2';
import { CalendarCheck } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import { SectionHeader } from '../../../components/SectionHeader';
import { SelectOption } from '../../../utils/enum/select';
import { InsuranceFormValues } from '../form/types';

const ReportingUnitSection = () => {
    const { control } = useFormContext<InsuranceFormValues>();

    return (
        <div>
            <SectionHeader title="申報單位" />
            <Alert
                alertDesc="生效日：2025/05/05"
                alertIcon={<CalendarCheck />}
                showActionButton={false}
                variant="info"
                className="mb-4 mt-2 w-full max-w-full bg-[#f1fcfe]"
            />
            <div className="flex flex-row gap-6">
                {/* Left column */}
                <div className="flex w-1/2 w-full flex-col gap-6">
                    {/* 申報單位 */}
                    <FormItemContent
                        name="declarationUnit"
                        control={control}
                        fieldClassName="w-full"
                        required
                        label="申報單位"
                        labelClassName="mb-2 text-[14px] font-normal"
                        renderField={({ field }) => (
                            <Select
                                options={[
                                    {
                                        label: '54510058 鼎恆數位科技股份有限公司',
                                        value: '54510058 鼎恆數位科技股份有限公司',
                                    },
                                ]}
                                value={field.value ? { label: field.value, value: field.value } : null}
                                onChange={(option: SelectOption | null) => field.onChange(option ? option.value : '')}
                                placeholder="請選擇"
                                isClearable={false}
                                className="w-full"
                            />
                        )}
                    />
                    {/* Radio groups side by side */}
                    <div className="flex flex-row gap-x-8">
                        {/* 計算兼職薪資補充保費 */}
                        <div className="flex-1">
                            <FormItemContent
                                name="partTimeSupplementaryPremium"
                                fieldClassName="w-full"
                                control={control}
                                required
                                label="計算兼職薪資補充保費"
                                labelClassName="mb-2 text-[14px] font-normal"
                                renderField={({ field }) => {
                                    const uniquePrefix = 'partTimeSupplementaryPremium';
                                    return (
                                        <RadioGroupComponent
                                            value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                            isHorizontal
                                            className="text-[14px]"
                                            onValueChange={(val: string) =>
                                                field.onChange(val.substring(uniquePrefix.length + 1))
                                            }
                                            radioOptions={[
                                                {
                                                    label: '是',
                                                    value: `${uniquePrefix}-yes`,
                                                },
                                                {
                                                    label: '否',
                                                    value: `${uniquePrefix}-no`,
                                                },
                                            ]}
                                        />
                                    );
                                }}
                            />
                        </div>
                        {/* 補充保費弱勢族群身份 */}
                        <div className="flex-1">
                            <FormItemContent
                                name="supplementaryPremiumDisadvantaged"
                                fieldClassName="w-full"
                                control={control}
                                required
                                label="補充保費弱勢族群身份"
                                labelClassName="mb-2 text-[14px] font-normal"
                                renderField={({ field }) => {
                                    const uniquePrefix = 'supplementaryPremiumDisadvantaged';
                                    return (
                                        <RadioGroupComponent
                                            value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                            isHorizontal
                                            className="text-[14px]"
                                            onValueChange={(val: string) =>
                                                field.onChange(val.substring(uniquePrefix.length + 1))
                                            }
                                            radioOptions={[
                                                {
                                                    label: '是',
                                                    value: `${uniquePrefix}-yes`,
                                                },
                                                {
                                                    label: '否',
                                                    value: `${uniquePrefix}-no`,
                                                },
                                            ]}
                                        />
                                    );
                                }}
                            />
                        </div>
                    </div>
                </div>
                {/* Right column */}
                <div className="flex w-1/2 w-full flex-col gap-6">
                    {/* 證號別 and 扣繳方式 side by side */}
                    <div className="flex flex-row gap-x-8">
                        {/* 證號別 */}
                        <div className="flex-1">
                            <FormItemContent
                                name="idType"
                                fieldClassName="w-full"
                                control={control}
                                required
                                label="證號別"
                                labelClassName="mb-2 text-[14px] font-normal"
                                renderField={({ field }) => (
                                    <Select
                                        options={[{ label: '0本國個人', value: '0本國個人' }]}
                                        value={
                                            field.value
                                                ? { label: field.value, value: field.value }
                                                : { label: '0本國個人', value: '0本國個人' }
                                        }
                                        onChange={(option: SelectOption | null) =>
                                            field.onChange(option ? option.value : '')
                                        }
                                        placeholder="請選擇"
                                        isClearable={false}
                                        className="w-full"
                                    />
                                )}
                            />
                        </div>
                        {/* 扣繳方式 */}
                        <div className="flex-1">
                            <FormItemContent
                                name="withholdingMethod"
                                fieldClassName="w-full"
                                control={control}
                                required
                                label="扣繳方式"
                                labelClassName="mb-2 text-[14px] font-normal"
                                renderField={({ field }) => (
                                    <Select
                                        options={[
                                            {
                                                label: '依照所得稅額表扣繳',
                                                value: '依照所得稅額表扣繳',
                                            },
                                        ]}
                                        value={
                                            field.value
                                                ? { label: field.value, value: field.value }
                                                : {
                                                      label: '依照所得稅額表扣繳',
                                                      value: '依照所得稅額表扣繳',
                                                  }
                                        }
                                        onChange={(option: SelectOption | null) =>
                                            field.onChange(option ? option.value : '')
                                        }
                                        placeholder="請選擇"
                                        isClearable={false}
                                        className="w-full"
                                    />
                                )}
                            />
                        </div>
                    </div>
                    {/* 備註 */}
                    <FormItemContent
                        name="taxNotes"
                        fieldClassName="w-full"
                        control={control}
                        label="備註"
                        labelClassName="mb-2 text-[14px] font-normal"
                        renderField={({ field }) => (
                            <Textarea
                                value={field.value}
                                onChange={field.onChange}
                                placeholder="請輸入"
                                maxLength={250}
                                showMaxLength
                                disabled={false}
                                className="w-full"
                            />
                        )}
                    />
                </div>
            </div>
        </div>
    );
};

export default ReportingUnitSection;
