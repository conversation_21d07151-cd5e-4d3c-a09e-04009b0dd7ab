import { Card, CardContent, FormItemContent,Input, Switch, Textarea } from '@mayo/mayo-ui-beta/v2';
import React from 'react';
import { useFormContext } from 'react-hook-form';

interface ExemptionStatusProps {
    section: 'laborInsurance' | 'occupationInsurance' | 'healthInsurance';
}

const ExemptionStatus: React.FC<ExemptionStatusProps> = ({ section }) => {
    const { control, watch } = useFormContext();

    const exemptionEnabled = watch(`${section}.exemptionEnabled`);
    return (
        <Card className="w-full rounded-2xl border border-gray-300 p-6">
            <CardContent className="p-0">
                <FormItemContent
                    name={`${section}.exemptionEnabled`}
                    control={control}
                    label="減免身份"
                    labelClassName="text-[14px] font-normal"
                    fieldClassName="w-full mb-2 flex items-center justify-between"
                    renderField={({ field }) => <Switch checked={field.value} onCheckedChange={field.onChange} />}
                />
                <FormItemContent
                    name={`${section}.subsidyRate`}
                    control={control}
                    label=""
                    fieldClassName="w-full"
                    renderField={({ field }) => (
                        <Input
                            type="number"
                            className="mt-4 w-full max-w-[350px]"
                            inputSize="md"
                            placeholder="0"
                            showSuffixIcon
                            suffixIcon={<span className="text-gray-500">%</span>}
                            disabled={!exemptionEnabled}
                            value={field.value}
                            onChange={field.onChange}
                        />
                    )}
                />
                <FormItemContent
                    name={`${section}.exemptionNotes`}
                    control={control}
                    label=""
                    fieldClassName="w-full"
                    renderField={({ field }) => (
                        <Textarea
                            className="mt-4 w-full"
                            placeholder="請輸入"
                            maxLength={250}
                            showMaxLength
                            disabled={!exemptionEnabled}
                            value={field.value}
                            onChange={field.onChange}
                        />
                    )}
                />
            </CardContent>
        </Card>
    );
};

export default ExemptionStatus;
