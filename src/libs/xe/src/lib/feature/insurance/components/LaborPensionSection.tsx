import { Alert, FormItemContent, Input, RadioGroupComponent, Select, Textarea } from '@mayo/mayo-ui-beta/v2';
import { CalendarCheck } from 'lucide-react';
import { useFormContext } from 'react-hook-form';

import { SectionHeader } from '../../../components/SectionHeader';
import { SelectOption } from '../../../utils/enum/select';
import { InsuranceFormValues } from '../form/types';

const LaborPensionSection = () => {
    const { control } = useFormContext<InsuranceFormValues>();

    return (
        <div>
            <SectionHeader title="勞退" />
            <div>
                <Alert
                    alertDesc="生效日：2025/05/05"
                    alertIcon={<CalendarCheck />}
                    showActionButton={false}
                    variant="info"
                    className="mb-4 mt-2 w-full max-w-full bg-[#f1fcfe]"
                />
                <div className="flex w-full flex-row gap-8">
                    {/* Main content: 2/3 width */}
                    <div className="flex w-2/3 flex-1 flex-col gap-6">
                        {/* Row 1: 類別 | 狀態 */}
                        <div className="flex w-full flex-row gap-6">
                            <div className="flex flex-1 flex-col">
                                <FormItemContent
                                    name="retirementFund.category"
                                    fieldClassName="w-full"
                                    control={control}
                                    label="類別"
                                    labelClassName="mb-2 text-[14px] font-normal"
                                    renderField={({ field }: { field: any }) => {
                                        const uniquePrefix = 'retirementFund-category';
                                        return (
                                            <RadioGroupComponent
                                                value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                                isHorizontal
                                                className="text-[14px]"
                                                onValueChange={(val: string) =>
                                                    field.onChange(val.substring(uniquePrefix.length + 1))
                                                }
                                                radioOptions={[
                                                    {
                                                        label: '新制',
                                                        value: `${uniquePrefix}-新制`,
                                                    },
                                                    {
                                                        label: '舊制',
                                                        value: `${uniquePrefix}-舊制`,
                                                    },
                                                ]}
                                            />
                                        );
                                    }}
                                />
                            </div>
                            <div className="flex flex-1 flex-col">
                                <FormItemContent
                                    name="retirementFund.status"
                                    fieldClassName="w-full"
                                    control={control}
                                    label="狀態"
                                    labelClassName="mb-2 text-[14px] font-normal"
                                    renderField={({ field }: { field: any }) => {
                                        const uniquePrefix = 'retirementFund-status';
                                        return (
                                            <RadioGroupComponent
                                                value={field.value ? `${uniquePrefix}-${field.value}` : undefined}
                                                isHorizontal
                                                className="text-[14px]"
                                                onValueChange={(val: string) =>
                                                    field.onChange(val.substring(uniquePrefix.length + 1))
                                                }
                                                radioOptions={[
                                                    {
                                                        label: '提繳',
                                                        value: `${uniquePrefix}-提繳`,
                                                    },
                                                    {
                                                        label: '停繳',
                                                        value: `${uniquePrefix}-停繳`,
                                                    },
                                                ]}
                                            />
                                        );
                                    }}
                                />
                            </div>
                        </div>
                        {/* Row 2: 投保單位 (full width) */}
                        <div className="flex w-full flex-col">
                            <FormItemContent
                                name="retirementFund.unit"
                                fieldClassName="w-full"
                                control={control}
                                label="投保單位"
                                labelClassName="mb-2 text-[14px] font-normal"
                                renderField={({ field }: { field: any }) => (
                                    <Select
                                        options={[
                                            {
                                                label: '54510058 鼎恆數位科技股份有限公司',
                                                value: '54510058 鼎恆數位科技股份有限公司',
                                            },
                                        ]}
                                        value={field.value ? { label: field.value, value: field.value } : null}
                                        onChange={(option: SelectOption | null) =>
                                            field.onChange(option ? option.value : '')
                                        }
                                        placeholder="請選擇"
                                        isClearable={false}
                                        className="w-full"
                                    />
                                )}
                            />
                        </div>
                        {/* Row 3: 籍別 | 投保金額 */}
                        <div className="flex w-full flex-row gap-6">
                            <div className="flex flex-1 flex-col">
                                <FormItemContent
                                    name="retirementFund.type"
                                    fieldClassName="w-full"
                                    control={control}
                                    label="籍別"
                                    labelClassName="mb-2 text-[14px] font-normal"
                                    renderField={({ field }: { field: any }) => (
                                        <Select
                                            options={[{ label: '本國人', value: '本國人' }]}
                                            value={
                                                field.value
                                                    ? {
                                                          label: field.value,
                                                          value: field.value,
                                                      }
                                                    : null
                                            }
                                            onChange={(option: SelectOption | null) =>
                                                field.onChange(option ? option.value : '')
                                            }
                                            placeholder="請選擇"
                                            isClearable={false}
                                            className="w-full"
                                        />
                                    )}
                                />
                            </div>
                            <div className="flex flex-1 flex-col">
                                <FormItemContent
                                    name="retirementFund.amount"
                                    fieldClassName="w-full"
                                    control={control}
                                    label="投保金額"
                                    labelClassName="mb-2 text-[14px] font-normal"
                                    renderField={({ field }: { field: any }) => (
                                        <Select
                                            options={[]}
                                            value={
                                                field.value
                                                    ? {
                                                          label: field.value,
                                                          value: field.value,
                                                      }
                                                    : null
                                            }
                                            onChange={(option: SelectOption | null) =>
                                                field.onChange(option ? option.value : '')
                                            }
                                            placeholder="請選擇"
                                            isClearable
                                            className="w-full"
                                        />
                                    )}
                                />
                            </div>
                        </div>
                        {/* Row 4: 雇主提繳率 | 個人提繳率 */}
                        <div className="flex w-full flex-row gap-6">
                            <div className="flex flex-1 flex-col">
                                <FormItemContent
                                    name="retirementFund.employerRate"
                                    fieldClassName="w-full"
                                    control={control}
                                    label="雇主提繳率"
                                    labelClassName="mb-2 text-[14px] font-normal"
                                    renderField={({ field }: { field: any }) => (
                                        <Input
                                            value={field.value}
                                            onChange={field.onChange}
                                            className="w-full"
                                            suffixIcon={<span className="text-gray-500">%</span>}
                                            placeholder=""
                                        />
                                    )}
                                />
                            </div>
                            <div className="flex flex-1 flex-col">
                                <FormItemContent
                                    name="retirementFund.personalRate"
                                    fieldClassName="w-full"
                                    control={control}
                                    label="個人提繳率"
                                    labelClassName="mb-2 text-[14px] font-normal"
                                    renderField={({ field }: { field: any }) => (
                                        <Input
                                            value={field.value}
                                            onChange={field.onChange}
                                            className="w-full"
                                            suffixIcon={<span className="text-gray-500">%</span>}
                                            placeholder=""
                                        />
                                    )}
                                />
                            </div>
                        </div>
                        {/* Row 5: 備註 */}
                        <div className="flex w-full flex-row gap-6">
                            <div className="flex flex-1 flex-col">
                                <FormItemContent
                                    name="retirementFund.notes"
                                    fieldClassName="w-full"
                                    control={control}
                                    label="備註"
                                    labelClassName="mb-2 text-[14px] font-normal"
                                    renderField={({ field }: { field: any }) => (
                                        <Textarea
                                            value={field.value}
                                            onChange={field.onChange}
                                            placeholder="請輸入"
                                            maxLength={250}
                                            showMaxLength
                                            disabled={false}
                                            className="w-full"
                                        />
                                    )}
                                />
                            </div>
                        </div>
                    </div>
                    {/* Empty 1/3 width block, hidden on mobile */}
                    <div className="block w-1/3" />
                </div>
            </div>
        </div>
    );
};

export default LaborPensionSection;
