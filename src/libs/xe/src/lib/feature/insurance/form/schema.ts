import { z } from 'zod';

const insuranceSubSchema = z.object({
    status: z.string(),
    amount: z.string(),
    unit: z.string(),
    type: z.string(),
    identity: z.string(),
    employerBurden: z.string(),
    personalBurden: z.string(),
    notes: z.string(),
    exemptionEnabled: z.boolean(),
    subsidyRate: z.string(),
    exemptionNotes: z.string(),
});

export const insuranceSchema = z.object({
    declarationUnit: z.string().min(1, 'This field is required'),
    partTimeSupplementaryPremium: z.enum(['yes', 'no']),
    supplementaryPremiumDisadvantaged: z.enum(['yes', 'no']),
    idType: z.string().min(1, 'This field is required'),
    withholdingMethod: z.string().min(1, 'This field is required'),
    taxNotes: z.string(),
    dependents: z.array(
        z.object({
            name: z.string(),
            relation: z.string(),
            birthday: z.any().optional(),
            idNumber: z.string(),
            address: z.string(),
            condition: z.string(),
        }),
    ),
    laborInsurance: insuranceSubSchema,
    occupationInsurance: insuranceSubSchema,
    healthInsurance: insuranceSubSchema,
    retirementFund: z.object({
        category: z.string(),
        status: z.string(),
        unit: z.string(),
        type: z.string(),
        amount: z.string(),
        employerRate: z.string(),
        personalRate: z.string(),
        notes: z.string(),
    }),
});
