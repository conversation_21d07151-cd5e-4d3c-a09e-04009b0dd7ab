import { z } from 'zod';

import { insuranceSchema } from './schema';

export type InsuranceFormValues = z.infer<typeof insuranceSchema>;

export const defaultInsuranceFormValues: InsuranceFormValues = {
    declarationUnit: '54510058 鼎恆數位科技股份有限公司',
    partTimeSupplementaryPremium: 'yes',
    supplementaryPremiumDisadvantaged: 'no',
    idType: '0本國個人',
    withholdingMethod: '依照所得稅額表扣繳',
    taxNotes: '',
    dependents: [{ name: '', relation: '', birthday: '', idNumber: '', address: '', condition: '' }],
    laborInsurance: {
        status: '加保',
        amount: '',
        unit: '54510058 鼎恆數位科技股份有限公司',
        type: '本國人',
        identity: '',
        employerBurden: '計算',
        personalBurden: '計算',
        notes: '',
        exemptionEnabled: false,
        subsidyRate: '',
        exemptionNotes: '',
    },
    occupationInsurance: {
        status: '加保',
        amount: '',
        unit: '54510058 鼎恆數位科技股份有限公司',
        type: '本國人',
        identity: '',
        employerBurden: '計算',
        personalBurden: '計算',
        notes: '',
        exemptionEnabled: false,
        subsidyRate: '',
        exemptionNotes: '',
    },
    healthInsurance: {
        status: '加保',
        amount: '',
        unit: '54510058 鼎恆數位科技股份有限公司',
        type: '本國人',
        identity: '一般',
        employerBurden: '計算',
        personalBurden: '計算',
        notes: '',
        exemptionEnabled: false,
        subsidyRate: '',
        exemptionNotes: '',
    },
    retirementFund: {
        category: '新制',
        status: '提繳',
        unit: '54510058 鼎恆數位科技股份有限公司',
        type: '本國人',
        amount: '',
        employerRate: '',
        personalRate: '',
        notes: '',
    },
};
