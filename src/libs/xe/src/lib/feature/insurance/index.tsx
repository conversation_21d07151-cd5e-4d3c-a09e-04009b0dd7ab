import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, Form, MaskLoading } from '@mayo/mayo-ui-beta/v2';
import { FormProvider,useForm } from 'react-hook-form';
import ProgressBar from 'xe/components/ProgressBar';

import { BreadcrumbItem, Breadcrumbs } from '../../components/breadcrumb';
import FamilyInsuranceSection from './components/FamilyInsuranceSection';
import InsuranceTypeSection from './components/InsuranceTypeSection';
import LaborPensionSection from './components/LaborPensionSection';
import ReportingUnitSection from './components/ReportingUnitSection';
import { insuranceSchema } from './form/schema';
import { defaultInsuranceFormValues,InsuranceFormValues } from './form/types';

const breadcrumbItems: BreadcrumbItem[] = [
    { label: '流程專區', href: '' },
    { label: '人事異動', href: '' },
    { label: '新進人員', href: '' },
    { label: '稅與保險', href: '', isCurrent: true },
];

const Insurance = ({ onboardingId }: { onboardingId: string }) => {
    const form = useForm<InsuranceFormValues>({
        resolver: zodResolver(insuranceSchema),
        defaultValues: defaultInsuranceFormValues,
    });

    const onSubmit = async (values: any) => {
        console.log('values', values);
    };

    const isLoading = false;

    return (
        <div className="flex flex-col gap-4">
            {isLoading && <MaskLoading />}

            <Breadcrumbs items={breadcrumbItems} />
            <FormProvider {...form}>
                <Form {...form}>
                    <form onSubmit={form.handleSubmit(onSubmit)}>
                        <div className="flex flex-col gap-4">
                            <h1 className="text-xl font-bold">新進人員資料</h1>
                            <ProgressBar onboardingId={onboardingId} onSave={form.handleSubmit(onSubmit)} />
                            {/* 內容卡片 */}
                            <Card className="scrollbar-none h-full w-full overflow-scroll px-10 py-12">
                                <CardContent className="flex flex-col gap-8">
                                    <ReportingUnitSection />
                                    <FamilyInsuranceSection />
                                    <InsuranceTypeSection sectionKey="laborInsurance" title="勞保" />
                                    <InsuranceTypeSection sectionKey="occupationInsurance" title="職保" />
                                    <LaborPensionSection />
                                    <InsuranceTypeSection sectionKey="healthInsurance" title="健保" />
                                </CardContent>
                            </Card>
                        </div>
                    </form>
                </Form>
            </FormProvider>
        </div>
    );
};

export default Insurance;
