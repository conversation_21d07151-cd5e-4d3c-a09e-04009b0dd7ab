import { toast } from '@mayo/mayo-ui-beta/v2';
import { useCallback, useState } from 'react';
import useSWR from 'swr';
import useSWRMutation from 'swr/mutation';
import ClientService from 'xe/utils/service/clientService';

export interface IncomeTax {
    incomeTaxReturnUnitId: string;
    incomeTaxReturnUnitName: string;
    incomeTaxNumberCode: string;
    incomeTaxNumberName: string;
    remark: string;
    withholdingTypeCode: string;
    withholdingTypeName: string;
    isSupplementaryPremiumPartTime: boolean;
    supplementaryPremiumPartTimeUnitId: string;
    supplementaryPremiumPartTimeUnitName: string;
    isDisadvantagedGroup: boolean;
}

export interface IncomeTaxDependent {
    name: string;
    dependentTitle: string;
    birthday: string;
    idNumber: string;
    address: string;
    dependentsExemptionConditionCode: string;
    dependentsExemptionConditionName: string;
}

export interface LaborInsurance {
    status: string;
    insuranceApplicantUnitId: string;
    insuranceApplicantUnitName: string;
    nationality: string;
    insuranceIdentityId: string;
    insuranceIdentityName: string;
    isDeduction: boolean;
    allowanceRate: number;
    allowanceRemark: string;
    insuranceAmountLevelId: string;
    insuranceAmountLevelValue: number;
    isEmployerBurden: boolean;
    isPersonalBurden: boolean;
    remark: string;
}

export interface OccupationalInsurance {
    status: string;
    insuranceApplicantUnitId: string;
    insuranceApplicantUnitName: string;
    nationality: string;
    insuranceIdentityId: string;
    insuranceIdentityName: string;
    isDeduction: boolean;
    allowanceRate: number;
    allowanceRemark: string;
    insuranceAmountLevelId: string;
    insuranceAmountLevelValue: number;
    isEmployerBurden: boolean;
    isPersonalBurden: boolean;
    remark: string;
}

export interface LaborPensionInsurance {
    category: string;
    status: string;
    insuranceApplicantUnitId: string;
    insuranceApplicantUnitName: string;
    nationality: string;
    insuranceAmountLevelId: string;
    insuranceAmountLevelValue: number;
    employerBurdenRate: number;
    personalBurdenRate: number;
    remark: string;
}

export interface HealthInsurance {
    status: string;
    surrenderCode: number;
    insuranceApplicantUnitId: string;
    insuranceApplicantUnitName: string;
    nationality: string;
    insuranceIdentityId: string;
    insuranceIdentityName: string;
    isDeduction: boolean;
    allowanceRate: number;
    highestAllowanceAmount: number;
    allowanceRemark: string;
    insuranceAmountLevelId: string;
    insuranceAmountLevel: number;
    isEmployerBurden: boolean;
    isPersonalBurden: boolean;
    remark: string;
    isDeductionRateType: boolean;
}

export interface InsuranceData {
    onboardingId: string;
    onboardingDate: string;
    incomeTax: IncomeTax;
    incomeTaxDependents: IncomeTaxDependent[];
    laborInsurance: LaborInsurance;
    occupationalInsurance: OccupationalInsurance;
    laborPensionInsurance: LaborPensionInsurance;
    healthInsurance: HealthInsurance;
}

export interface InsuranceGetResponse {
    data: InsuranceData;
}

type UpdateInsuranceParams = InsuranceData;

async function updateInsuranceMutation(url: string, { arg }: { arg: UpdateInsuranceParams }) {
    return ClientService.PUT(url, arg);
}

function useInsuranceHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);
    const [hasShownSuccessToast, setHasShownSuccessToast] = useState(false);

    const { data, error, mutate } = useSWR<InsuranceGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
        {
            onSuccess: (_data) => {
                if (!hasShownSuccessToast && shouldFetch) {
                    toast.success('資料獲取成功');
                    setHasShownSuccessToast(true);
                }
            },
            onError: (_error) => {
                toast.error('獲取資料失敗');
                setHasShownSuccessToast(false);
            },
        },
    );

    const {
        trigger: updateInsuranceTrigger,
        error: updateError,
        isMutating: isUpdating,
    } = useSWRMutation(endpoint, updateInsuranceMutation, {
        onSuccess: () => {
            mutate();
            toast.info('更新資料成功');
        },
        onError: (_error) => {
            toast.error('更新資料失敗');
        },
        rollbackOnError: true,
    });

    const fetchInsurance = useCallback((onboardingId: string) => {
        if (onboardingId) {
            setEndpoint(`/v2/platform-api/api/HireProcess/Insurance/${onboardingId}`);
            setShouldFetch(true);
            setHasShownSuccessToast(false);
        }
    }, []);

    const refetchInsurance = useCallback(() => {
        mutate();
    }, [mutate]);

    const updateInsurance = async (onboardingId: string, updateData: UpdateInsuranceParams) => {
        if (!onboardingId) return;
        return updateInsuranceTrigger(updateData);
    };

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error: error || updateError,
        isMutating: isUpdating,
        update: updateInsurance,
        fetch: fetchInsurance,
        refetch: refetchInsurance,
    };
}

export default useInsuranceHook;
