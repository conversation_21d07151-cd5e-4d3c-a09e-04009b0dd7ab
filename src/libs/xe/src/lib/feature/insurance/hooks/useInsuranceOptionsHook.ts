import { useCallback,useState } from 'react';
import useSWR from 'swr';
import ClientService from 'xe/utils/service/clientService';

export interface InsuranceOptionsGetResponse {
    data: {
        insuranceApplicantUnits: Array<{
            key: string;
            value: string;
        }>;
        insuranceIdentities: Array<{
            key: string;
            value: string;
        }>;
        insuranceAmountOptions: {
            labourInsuranceLevels: Array<{
                key: string;
                value: number;
            }>;
            occupationalInsuranceLevels: Array<{
                key: string;
                value: number;
            }>;
            laborPensionInsuranceLevels: Array<{
                key: string;
                value: number;
            }>;
            healthInsuranceLevels: Array<{
                key: string;
                value: number;
            }>;
        };
        incomeTaxOptions: {
            incomeTaxNumbers: Array<{
                key: string;
                value: string;
            }>;
            incomeTaxReturnUnits: Array<{
                key: string;
                value: string;
            }>;
            incomeTaxWithholdingTypes: Array<{
                key: string;
                value: string;
            }>;
            dependentsExemptionConditions: Array<{
                key: string;
                value: string;
            }>;
        };
    };
}

function useInsuranceOptionsHook() {
    const [endpoint, setEndpoint] = useState<string | null>(null);
    const [shouldFetch, setShouldFetch] = useState(false);

    const { data, error, mutate } = useSWR<InsuranceOptionsGetResponse>(
        shouldFetch && endpoint ? endpoint : null,
        endpoint ? ClientService.GET : null,
    );

    const fetchOptions = useCallback((onboardingDate?: string) => {
        let url = '/v2/platform-api/api/HireProcess/Insurance/Options';
        if (onboardingDate) {
            url += `?onboardingDate=${encodeURIComponent(onboardingDate)}`;
        }
        setEndpoint(url);
        setShouldFetch(true);
    }, []);

    const refetchOptions = useCallback(() => {
        mutate();
    }, [mutate]);

    return {
        data,
        isLoading: shouldFetch && !error && !data,
        error,
        fetch: fetchOptions,
        refetch: refetchOptions,
    };
}

export default useInsuranceOptionsHook;
