{"compilerOptions": {"jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "strict": true, "types": ["vite/client", "vitest", "node"]}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": ["../../tsconfig.base.json"]}