{"name": "domains", "version": "0.0.1", "main": "./index.js", "types": "./index.d.ts", "exports": {".": {"import": "./index.mjs", "require": "./index.js"}}, "devDependencies": {"@babel/core": "^7.14.5", "@babel/preset-react": "^7.14.5", "@hookform/resolvers": "4.1.3", "@mayo/mayo-ui-beta": "2.0.1-beta.11", "@nx/eslint": "20.7.1", "@nx/eslint-plugin": "20.7.1", "@nx/js": "20.7.1", "@nx/vite": "20.7.1", "@nx/web": "20.7.1", "@svgr/core": "^8.1.0", "@swc-node/register": "~1.9.1", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/react": "16.1.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20.0.0", "@types/react": "18", "@types/react-dom": "18", "@typescript-eslint/eslint-plugin": "^7.16.0", "@typescript-eslint/parser": "^7.16.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "axios": "^1.7.2", "classnames": "^2.5.1", "eslint": "~8.57.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "eslint-plugin-simple-import-sort": "^12.1.1", "i18next": "^24.2.3", "jiti": "2.4.2", "jsdom": "~22.1.0", "lucide-react": "^0.487.0", "prettier": "^3.2.5", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "swr": "^2.2.5", "tslib": "^2.3.0", "typescript": "^5", "usehooks-ts": "3.1.0", "vite": "^6.0.0", "vite-plugin-dts": "~4.5.0", "vite-plugin-svgr": "^2.4.0", "vitest": "^3.0.0", "zod": "^3.23.8", "zustand": "4.5.2", "autoprefixer": "^10.0.1", "postcss": "^8", "tailwindcss": "^3.3.0"}, "dependencies": {"dayjs": "^1.11.13", "jotai": "2.9.0", "js-cookie": "^3.0.5", "react": "18.3.1", "react-dom": "18.3.1"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0", "tailwindcss": ">=3.0.0"}}