module.exports = {
    root: true,
    env: { browser: true, es2020: true, node: true },
    extends: [
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:react-hooks/recommended',
        'prettier',
    ],
    ignorePatterns: ['dist', '.eslintrc.cjs'],
    parser: '@typescript-eslint/parser',
    plugins: [
        'react-refresh',
        'simple-import-sort', // 添加 plugin
        '@typescript-eslint',
    ],
    rules: {
        'simple-import-sort/imports': [
            'error',
            {
                groups: [
                    // React 相關套件
                    ['^react', '^@?\\w'],
                    // 內部別名路徑 (@/)
                    ['^@/'],
                    // Nx 相關
                    ['^@nx/'],
                    // 相對路徑
                    ['^\\.'],
                    // 樣式檔
                    ['^.+\\.s?css$'],
                ],
            },
        ],
        'react-refresh/only-export-components': ['warn', { allowConstantExport: true }],
        '@typescript-eslint/no-explicit-any': ['off'],
        'no-console': 'error',
        'react/no-unused-prop-types': 'off',
        'consistent-return': 'off',
        'no-unreachable': 'off',
        'no-console': 'warn',
        'react/jsx-props-no-spreading': 'off',
        camelcase: 'off',
        'react/require-default-props': 'off',
        'no-param-reassign': 'off',
        'global-require': 'off',
        'react/react-in-jsx-scope': 'off',
        'no-use-before-define': 'off',
        'default-param-last': 'off',
        'no-unused-vars': 'off',
        'no-underscore-dangle': 'off',
        'import/prefer-default-export': 'off',
        'react/function-component-definition': 'off',
        'no-shadow': 'off',
        '@typescript-eslint/no-shadow': 'off',
        'react-hooks/exhaustive-deps': 'off',
        '@typescript-eslint/no-unused-vars': 'warn',
    },
};
