{"compilerOptions": {"jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "lib": ["dom", "dom.iterable"], "types": ["vite/client", "vitest", "vite-plugin-svgr/client"], "baseUrl": ".", "paths": {"domains/*": ["libs/domains/src/lib/*"], "@mayo/mayo-ui": ["./node_modules/@mayo/mayo-ui-beta/dist/index.js"]}}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}], "extends": "../../tsconfig.common.json"}