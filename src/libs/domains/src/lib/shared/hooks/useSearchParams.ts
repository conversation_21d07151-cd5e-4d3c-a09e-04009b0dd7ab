import { useCallback, useEffect, useState } from 'react';

type SearchParams = URLSearchParams;
type SetSearchParams = (
    params: Record<string, string> | ((prev: SearchParams) => Record<string, string>)
) => void;

export function useSearchParams(): [SearchParams, SetSearchParams] {
    // 獲取當前 URL 的 search params
    const getSearchParams = useCallback(() => {
        if (typeof window === 'undefined') return new URLSearchParams();
        return new URLSearchParams(window.location.search);
    }, []);

    const [searchParams, setSearchParams] = useState<SearchParams>(getSearchParams());

    // 更新 URL 和狀態
    const updateSearchParams = useCallback(
        (params: Record<string, string> | ((prev: SearchParams) => Record<string, string>)) => {
            const newParams = typeof params === 'function' ? params(searchParams) : params;

            // 創建新的 URLSearchParams
            const updatedParams = new URLSearchParams(searchParams.toString());

            // 更新或刪除參數
            Object.entries(newParams).forEach(([key, value]) => {
                if (value === undefined || value === '') {
                    updatedParams.delete(key);
                } else {
                    updatedParams.set(key, value);
                }
            });

            // 更新 URL
            if (typeof window !== 'undefined') {
                const newUrl = `${window.location.pathname}${
                    updatedParams.toString() ? `?${updatedParams.toString()}` : ''
                }`;
                window.history.pushState({}, '', newUrl);
            }

            // 更新狀態
            setSearchParams(updatedParams);
        },
        [searchParams]
    );

    // 監聽瀏覽器的前進/後退
    useEffect(() => {
        const handlePopState = () => {
            setSearchParams(getSearchParams());
        };

        window.addEventListener('popstate', handlePopState);
        return () => window.removeEventListener('popstate', handlePopState);
    }, [getSearchParams]);

    return [searchParams, updateSearchParams];
}
