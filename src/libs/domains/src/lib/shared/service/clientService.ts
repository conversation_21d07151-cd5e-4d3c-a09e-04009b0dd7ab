/* eslint-disable no-useless-catch */
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';

import { COOKIE_NAMES, defaultLocale } from '../../xe/config';

// API 回應的基本介面
export interface ApiResponse<T> {
    Meta: {
        httpStatusCode: string;
    };
    Data: T;
}

// 錯誤處理介面
interface ApiError {
    code: string;
    message: string;
    status: number;
}

const axiosInstance: AxiosInstance = axios.create({
    withCredentials: true,
    responseType: 'json',
    headers: {
        'Accept-Language': Cookies.get(COOKIE_NAMES.LOCALE) || defaultLocale,
        'Content-Type': 'application/json',
    },
});

axiosInstance.defaults.headers.post['Content-Type'] = 'application/json';
axiosInstance.defaults.headers.put['Content-Type'] = 'application/json';
axiosInstance.defaults.headers.delete['Content-Type'] = 'application/json';

// 請求攔截器
axiosInstance.interceptors.request.use((config) => {
    // doc: 在 client side run time 取得 backend server
    if (typeof window !== 'undefined' && !config.baseURL) {
        const clientBaseURL = Cookies.get(COOKIE_NAMES.BACKEND_SERVER) || undefined;
        config.baseURL = clientBaseURL;
    }

    return config;
});

// 回應攔截器
axiosInstance.interceptors.response.use(
    (response) => response,
    (error: AxiosError<ApiError>) => {
        const errorResponse = {
            code: error.response?.data?.code || 'UNKNOWN_ERROR',
            message: error.response?.data?.message || 'An unexpected error occurred',
            status: error.response?.status || 500,
        };
        return Promise.reject(errorResponse);
    }
);

export const get = async <T>(url: string): Promise<T> => {
    const response: AxiosResponse<T> = await axiosInstance.get<T>(url);
    return response?.data;
};

// HTTP 方法封裝
const ClientService = {
    GET: async <T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
        try {
            const response: AxiosResponse<ApiResponse<T>> = await axiosInstance.get<ApiResponse<T>>(
                url,
                config
            );
            return {
                Data: response.data.Data,
                Meta: {
                    httpStatusCode: response.status.toString(),
                },
            };
        } catch (error) {
            throw error;
        }
    },

    POST: async <T, D = unknown>(
        url: string,
        data?: D,
        config?: AxiosRequestConfig
    ): Promise<ApiResponse<T>> => {
        try {
            const response: AxiosResponse<T> = await axiosInstance.post<T>(url, data, config);
            return {
                Data: response.data,
                Meta: {
                    httpStatusCode: response.status.toString(),
                },
            };
        } catch (error) {
            throw error;
        }
    },

    // 可以加入其他 HTTP 方法...
};

// 使用範例：
/*
try {
  const { data } = await api.get<User[]>('/users');
  console.log(data);
} catch (error) {
  console.error('API Error:', error.message);
}
*/

export default ClientService;
