import useSWR, { SWRConfiguration } from 'swr';

import ClientService from './clientService';

export interface User {
    isVerify: boolean;
    userModule: string[];
    userName: string;
    userRole: string[];
    IsSupervisor: boolean;
    IsSecretary: boolean;
    PersonalPicture: string;
    EmployeeId: string;
    CompanyId: string;
}

const useGetFdUserInfo = (config?: SWRConfiguration) => {
    const fetcher = useSWR<User>('/fd/api/userInfo', ClientService.GET, {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
        ...config,
    });

    return fetcher;
};

export { useGetFdUserInfo };
