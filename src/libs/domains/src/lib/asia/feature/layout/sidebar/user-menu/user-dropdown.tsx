import { Avatar, CompoundDropdownMenu, CompoundDropdownMenuItem } from '@mayo/mayo-ui-beta/v2';
import { envsAtom } from 'domains/shared/jotai/env';
import { useGetFdUserInfo } from 'domains/shared/service';
import { useAtomValue } from 'jotai';
import { Key, LogOut, User } from 'lucide-react';

const UserDropdown = () => {
    const { data: user } = useGetFdUserInfo({});
    const envs = useAtomValue(envsAtom);

    const userAvatar = user?.Data.PersonalPicture || '/images/avatar/<EMAIL>';

    const menuItems: CompoundDropdownMenuItem[] = [
        {
            id: 'label-1',
            element: (
                <div className="m-2 flex items-center justify-between gap-1">
                    <Avatar
                        size="sm"
                        className="cursor-pointer"
                        alt="User Avatar"
                        src={userAvatar}
                    />

                    <div className="ml-2 w-full">
                        <div className="text-sm">{user?.Data.userName}</div>
                        {/* <div className="text-accent-dark-accent text-sm">{user?.Data}</div> */}
                    </div>
                </div>
            ),
            type: 'element',
        },
        {
            id: 'sep-1',
            type: 'separator',
        },
        // {
        //     id: 'radio-1',
        //     items: [
        //         {
        //             id: 'radio-top',
        //             label: 'Top',
        //             value: 'top',
        //         },
        //         {
        //             id: 'radio-bottom',
        //             label: 'Bottom',
        //             value: 'bottom',
        //         },
        //         {
        //             id: 'radio-right',
        //             label: 'Right',
        //             value: 'right',
        //         },
        //     ],
        //     label: 'radioGroupExample',
        //     onValueChange: () => {
        //         //
        //     },
        //     type: 'radioGroup',
        //     value: 'bottom',
        // },
        // {
        //     id: 'sep-2',
        //     type: 'separator',
        // },
        // {
        //     id: 'label-2',
        //     label: 'Appearance',
        //     type: 'label',
        // },
        // {
        //     id: 'sep-3',
        //     type: 'separator',
        // },
        // {
        //     checked: true,
        //     id: 'checkbox-status',
        //     label: 'Status Bar',
        //     onCheckedChange: () => {
        //         //
        //     },
        //     type: 'checkbox',
        // },
        // {
        //     checked: false,
        //     disabled: true,
        //     id: 'checkbox-activity',
        //     label: 'Activity Bar',
        //     onCheckedChange: () => {
        //         //
        //     },
        //     type: 'checkbox',
        // },
        // {
        //     id: 'item-simple',
        //     label: 'Simple Item',
        //     onClick: () => {
        //         //
        //     },
        //     type: 'item',
        // },
        // {
        //     id: 'sub-1',
        //     label: 'More Features',
        //     subItems: [
        //         {
        //             id: 'sub-item-1',
        //             label: 'Sub Item 1',
        //             onClick: () => {
        //                 //
        //             },
        //             type: 'item',
        //         },
        //         {
        //             id: 'sub-item-2',
        //             label: 'Sub Item 2',
        //             onClick: () => {
        //                 //
        //             },
        //             type: 'item',
        //         },
        //         {
        //             id: 'sub-sep-1',
        //             type: 'separator',
        //         },
        //         {
        //             id: 'sub-item-3',
        //             label: 'Sub Item 3',
        //             onClick: () => {
        //                 //
        //             },
        //             type: 'item',
        //         },
        //     ],
        //     type: 'sub',
        // },
        // {
        //     id: 'sep-4',
        //     type: 'separator',
        // },
        {
            id: 'item-my-data',
            label: (
                <div className="flex items-center gap-2">
                    <User size={16} />
                    <span className="text-sm font-normal">My Data</span>
                </div>
            ),
            type: 'item',
        },
        {
            id: 'item-change-password',
            label: (
                <div className="flex items-center gap-2">
                    <Key size={16} />
                    <span className="text-sm font-normal">Change Password</span>
                </div>
            ),
            type: 'item',
        },
        {
            id: 'sep-2',
            type: 'separator',
        },
        {
            id: 'item-logout',
            label: (
                <div
                    className="flex items-center gap-2"
                    onClick={() => {
                        window.location.href = `${envs.SERVER_ENV_AUTH_WEB_SITE}/Account/Login?original_target=${encodeURIComponent(window.location.href)}`;
                    }}
                >
                    <LogOut size={16} />
                    <span className="text-sm font-normal">Logout</span>
                </div>
            ),
            type: 'item',
        },
    ];

    return (
        <CompoundDropdownMenu
            menuItems={menuItems}
            triggerLabel="open"
            placement="bottom-end"
            triggerProps={{ variant: 'outline', className: 'w-52' }}
            trigger={
                <Avatar size="sm" className="cursor-pointer" alt="User Avatar" src={userAvatar} />
            }
        />
    );
};

export default UserDropdown;
