import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
} from '@mayo/mayo-ui-beta/v2';
import { ChevronRight, LucideIcon } from 'lucide-react';

// import FunctionDropdown from './function-dropdown';

interface NavMainProps {
    items: {
        title: string;
        url: string;
        icon: LucideIcon;
        isActive?: boolean;
        items?: {
            title: string;
            url: string;
        }[];
    }[];
}

const NavMain = ({ items }: NavMainProps) => {
    return (
        <SidebarGroup>
            {/* <FunctionDropdown /> */}
            <SidebarGroupLabel>流程專區</SidebarGroupLabel>
            <SidebarMenu>
                {/* <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                        <a
                            onClick={() => push(`${prodPath}/xe/foundation/hire`)}
                            className="cursor-pointer"
                        >
                            <Bot />
                            <span>新進人員流程</span>
                        </a>
                    </SidebarMenuButton>
                </SidebarMenuItem> */}
                {items.map((item) => (
                    <Collapsible
                        key={item.title}
                        asChild
                        defaultOpen={item.isActive}
                        className="group/collapsible"
                    >
                        <SidebarMenuItem>
                            <CollapsibleTrigger asChild>
                                <SidebarMenuButton tooltip={item.title}>
                                    {item.icon && <item.icon />}
                                    <span>{item.title}</span>
                                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                                </SidebarMenuButton>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <SidebarMenuSub>
                                    {item.items?.map((subItem) => (
                                        <SidebarMenuSubItem key={subItem.title}>
                                            <SidebarMenuSubButton asChild>
                                                <a href={subItem.url}>
                                                    <span>{subItem.title}</span>
                                                </a>
                                            </SidebarMenuSubButton>
                                        </SidebarMenuSubItem>
                                    ))}
                                </SidebarMenuSub>
                            </CollapsibleContent>
                        </SidebarMenuItem>
                    </Collapsible>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
};

export default NavMain;
