import React from 'react';
// 導入 logo 圖片
import XeLogo from 'domains/xe/assets/xe-logo-blue.svg?react';
// import XeLogo from 'domains/';

interface LogoProps {
    className?: string;
    alt?: string;
}

/**
 * Logo 組件，用於顯示 XE 的 logo
 *
 * @param {string} className - 可選的 CSS 類名
 * @param {string} alt - 可選的替代文本
 * @returns {JSX.Element} Logo 組件
 */
export const Logo: React.FC<LogoProps> = ({ className = 'h-8' }) => {
    return (
        <div>
            <XeLogo className={className} />
        </div>
    );
};

export default Logo;
