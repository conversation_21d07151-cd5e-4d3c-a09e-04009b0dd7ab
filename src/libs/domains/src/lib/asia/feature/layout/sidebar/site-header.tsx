import { Button, Separator, useSidebar } from '@mayo/mayo-ui-beta/v2';
import prodPath from 'domains/shared/utils/prodPath';
import { SidebarCloseIcon, SidebarOpenIcon } from 'lucide-react';

import Logo from './logo';
// import { SearchForm } from './search-form';
import UserMenu from './user-menu';

export function SiteHeader() {
    const { open, toggleSidebar } = useSidebar();

    return (
        <header className="fle border-sidebar-border bg-sidebar text-sidebar-primary sticky top-0 z-50 w-full items-center border-b">
            <div className="flex h-[--header-height] w-full items-center gap-2 px-4">
                <Button
                    variant="ghost"
                    size="icon"
                    className="text-primary-foreground"
                    onClick={toggleSidebar}
                >
                    {open ? (
                        <SidebarCloseIcon className="size-4" />
                    ) : (
                        <SidebarOpenIcon className="size-4" />
                    )}
                </Button>
                <Separator dark orientation="vertical" className="mr-2 h-4" />
                <div className="flex w-[260px] items-center justify-center gap-2">
                    <div className="w-full">
                        <a onClick={() => console.log(`${prodPath}/xe`)} className="cursor-pointer">
                            <Logo alt="XE Logo" />
                        </a>
                    </div>
                </div>

                <div className="flex w-full justify-center">
                    {/* <SearchForm className="text-foreground w-[300px] sm:ml-auto sm:w-auto" /> */}
                </div>
                <div>
                    <UserMenu />
                </div>
            </div>
        </header>
    );
}
