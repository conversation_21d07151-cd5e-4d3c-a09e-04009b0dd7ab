import { SidebarInset, SidebarProvider } from '@mayo/mayo-ui-beta/v2';

import { AppSidebar } from './app-sidebar';
import { SiteHeader } from './site-header';

function Sidebar({ children }: { children: React.ReactNode }) {
    return (
        <SidebarProvider className="">
            <SiteHeader />
            <div className="flex flex-1">
                <AppSidebar />
                <SidebarInset>
                    <div className="h-[calc(100vh-55px)] bg-gray-50 p-6">
                        {children}

                        <footer className="py-4">
                            <p className="text-end text-sm text-gray-500">
                                Copyright © 2025 MAYO Human Capital Inc. All rights reserved.
                            </p>
                        </footer>
                    </div>
                </SidebarInset>
            </div>
        </SidebarProvider>
    );
}

export default Sidebar;
