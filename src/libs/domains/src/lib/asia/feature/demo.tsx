/* eslint-disable no-console */

import { useTranslation } from 'react-i18next';
import { Button } from '@mayo/mayo-ui-beta/v2';
import { useAtom } from 'jotai';
import useSWR from 'swr';

import { envsAtom } from '../../shared/jotai/env';
import { jotaiStore } from '../../shared/jotai/store';
import ClientService, { ApiResponse } from '../../shared/service/clientService';
import useCountStore from '../zustand/count';

interface User {
    isVerify: boolean;
    userModule: string[];
    userName: string;
    userRole: string[];
    IsSupervisor: boolean;
    IsSecretary: boolean;
    PersonalPicture: string;
    EmployeeId: string;
    CompanyId: string;
}

export function Demo() {
    const { t } = useTranslation();

    // Jotai state
    const [envs, setEnvs] = useAtom(envsAtom, { store: jotaiStore });

    // SWR data fetching
    const { data: user } = useSWR<ApiResponse<User>>('/fd/api/userInfo', ClientService.GET, {
        revalidateOnFocus: false,
        revalidateOnReconnect: false,
    });

    // Navigation hook

    // Zustand state
    const count = useCountStore((state: any) => state.count);
    const total = useCountStore((state: any) => state.total);
    const increment = useCountStore((state: any) => state.increment);
    const decrement = useCountStore((state: any) => state.decrement);
    const setCount = useCountStore((state: any) => state.setCount);
    const setTotal = useCountStore((state: any) => state.setTotal);

    // Log user data
    console.log(user);

    return (
        <div className="space-y-8 p-4">
            {/* Welcome Section */}
            <section>
                <h1 className="text-2xl font-bold mb-4">Welcome to ModuleXe!</h1>
                <Button>{t('AH_ForgetPassword')}</Button>
            </section>

            {/* Jotai Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">Jotai Demo</h1>
                <p>Locale: {JSON.stringify(envs)}</p>
                <button
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                    onClick={() => setEnvs({ locale: 'zh-TW' })}
                >
                    Set Envs
                </button>
            </section>

            {/* Zustand Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">Zustand Demo</h1>
                <p>Count: {count}</p>
                <p>Total: {total}</p>
                <div className="space-x-2">
                    <button
                        className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                        onClick={() => increment()}
                    >
                        Increment
                    </button>
                    <button
                        className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                        onClick={() => decrement()}
                    >
                        Decrement
                    </button>
                    <button
                        className="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
                        onClick={() => setCount(count + 1)}
                    >
                        Set Count
                    </button>
                    <button
                        className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
                        onClick={() => setTotal(total + 1)}
                    >
                        Set Total
                    </button>
                </div>
            </section>

            {/* Navigation Demo Section */}
            <section className="space-y-2">
                <h1 className="text-xl font-semibold">useNavigation Demo</h1>
                <div className="space-x-2">
                    <button
                        className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                        onClick={() => null}
                    >
                        Push
                    </button>
                    <button
                        className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                        onClick={() => null}
                    >
                        Replace
                    </button>
                    <button
                        className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
                        onClick={() => null}
                    >
                        Back
                    </button>
                </div>
            </section>
        </div>
    );
}

export default Demo;
