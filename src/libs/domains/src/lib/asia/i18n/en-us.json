﻿{
    "Version": "2024-00025",
    "AH_ForgetPassword": "Forgot password",
    "FD_AbandonOrder": "Give up the order",
    "FD_AbnormalDataFollows": "Exception data as below",
    "FD_Accepting": "Verifying acceptance",
    "FD_AccountActivationFailed": "account active process failed, please contact system administrator",
    "FD_AccountHasActivationError": "This account has an activation error, the People+ administrator will need to reset the account at account management page in order to activate the account.",
    "FD_AccountIsExist": "Account already exists",
    "FD_AccountManagement": "Account Management",
    "FD_AccountWillCreatedOnRegistrationDay": "User Account will create on registration date",
    "FD_ActivationEmailSentOut": "The activation email has been sent out",
    "FD_ActiveEmployeeList": "Active employee list",
    "FD_ActivityMessage": "Activity information",
    "FD_ActualEndDate": "Actual end date",
    "FD_ActualStartDate": "Actual start date",
    "FD_ActualTerminationDate": "Actual resignation date",
    "FD_AddAnnouncement": "Create announcement",
    "FD_AddDepartmentIsNotExist": "The department to be modified does not exist",
    "FD_AddDesignatedWork": "Create designate work",
    "FD_AdditionalFields": "Add field",
    "FD_AddLeavedStaff": "add",
    "FD_AddNews": "Create news",
    "FD_AddOrder": "Create order",
    "FD_AddPolicy": "Create policy",
    "FD_AddStayStaff": "Add",
    "FD_AddSticker": "Create notes",
    "FD_AddUnitReasonOnlySpecified": "When adding a new unit, you can only select the transaction reason for the unit to add or split the unit.",
    "FD_AddVendor": "Create a new vendor",
    "FD_Adjunct": "Adjunct",
    "FD_AdministrativeAnnouncement": "Administration announcement",
    "FD_AdvertisementDescription": "Advertisement Description",
    "FD_AdvertisementManagement": "Advertisement Management",
    "FD_AffiliatedJobPositionCode": "Affiliated job position code",
    "FD_AffiliatedJobPositionCodeBlank": "Affiliated job position code is blank",
    "FD_AffiliatedJobPositionCodeError": "Affiliated job position code is incorrect",
    "FD_AffiliatedUnit": "Affiliated unit",
    "FD_AffiliatedUnitBlank": "Affiliated unit is blank",
    "FD_AffiliatedUnitError": "Affiliated unit is required",
    "FD_AffiliatedUnitHasDefunct": "The affiliated unit has been expired or will be expired",
    "FD_AffiliatedUnitWillDefunct": "The affiliated unit will be expired",
    "FD_AfterESEndDStatusIsER": "The estimated effective day for in leave without pay area has been overdue, but the status have not changed.",
    "FD_AfterESEndDStatusIsStaying": "The estimated effective day in leave without pay area has been overdue, but the status have not changed.",
    "FD_AfterESStartDStatusIsES": "The estimated effective day in leave without pay area has been overdue, but the status have not changed.",
    "FD_AfterETDStatusIsET": "Some employee(s) estimated resignation date has been overdue,please confirm.",
    "FD_AllowedGuestNumber": "Allowed number of guests",
    "FD_AllowedGuestNumberPerPerson": "Allowed number of guests per employee",
    "FD_AllShortcuts": "All shortcuts",
    "FD_AlreadyExistHireNoShowList": "This employee already exists in the Hire no show list",
    "FD_AlreadyExistPA": "This employee already exists in People+",
    "FD_AnticipatedEndDate": "Anticipated end date",
    "FD_AnticipatedExpatriateEndDate": "Anticipated expatriate end date",
    "FD_AnticipatedExtensionPeriod": "Anticipated extension period",
    "FD_AnticipatedLOAPeriod": "expected the leave without pay period",
    "FD_AnticipatedProbationEndDate": "Anticipated probation end date",
    "FD_ApplicationStatus": "Application status",
    "FD_AppointedDate": "Appointed date",
    "FD_AppointedObjects": "Appointee",
    "FD_AppointedWork": "Appointed work",
    "FD_ApprovalDate": "Approval date and time",
    "FD_AsAbove": "As above",
    "FD_AssistantManager": "Assistant manager",
    "FD_AttendanceDataCreationHasFailed": "Attendance data creation has failed, please contact the attendance administrator",
    "FD_AttendanceDataIsRequiredOrNot": "Attendance data is required or not",
    "FD_AttendanceDataIsRequiredOrNotBlank": "Attendance data is required or not is blank",
    "FD_AttendanceDataIsRequiredOrNotError": "Attendance data is required or not is incorrect",
    "FD_AttendanceHasChangeRecords": "Attendance has change records, please contact the attendance administrator",
    "FD_AuthorizationType": "Authorization type",
    "FD_AuthorizingSupervisor": "Signing supervisor",
    "FD_BatchExport": "Batch Export",
    "FD_BatchImport": "Batch import",
    "FD_BatchImportTemplate": "Batch Import Template",
    "FD_BeforeArrivalInternalCompanyYOS": "Approved year of service before on board",
    "FD_BeforeArrivalJobBandYos": "Approved year of service(job grade) before on board",
    "FD_BeforeArrivalYOSorProbationData": "Seniority/Probation data before on board",
    "FD_BirthDateCodeError": "Date of birth code is incorrect",
    "FD_Birthplace": "Birthplace",
    "FD_BirthplaceCodeError": "Birthplace code is incorrect",
    "FD_BlacklistSettings": "Blacklist settings",
    "FD_Branches": "Branches",
    "FD_CancelLeaved": "Cancel resignation",
    "FD_CancelOrder": "Order cancellation",
    "FD_CancelRegistration": "Cancel registration",
    "FD_CancelStay": "Cancel leave without pay status",
    "FD_CanNotDeleteVendor": "The vendor cannot be deleted with an existing order information",
    "FD_CannotFindTagData": "No corresponding tab information found",
    "FD_CannotFindUploadfiles": "Cannot find uploaded file",
    "FD_CareerRecognizedSeniorityWrong": "Employee No. {0} career recognizes the format of the seniority (year);",
    "FD_CareerSeniorityWrong": "Employee No. {0} career year (year) format error;",
    "FD_CaseStatus": "Case status",
    "FD_CensusBlank": "Residential address is blank",
    "FD_CensusCountryArea": "Residential address (county/area)",
    "FD_CensusCountryAreaBlank": "Residential address (county/area) is blank",
    "FD_CensusCountryAreaError": "Residential address (county/area) is incorrect",
    "FD_CensusCountyCity": "Residential address (province/city)",
    "FD_CensusCountyCityBlank": "Residential address (province/city) is blank",
    "FD_CensusCountyCityError": "Residential address (province/city) is incorrect",
    "FD_CertificateExpirationDate": "Certificate expiration date",
    "FD_CertificateExpired": "Expiring certificate",
    "FD_CertificateInfoNotes": "Remarks for certificate data",
    "FD_CertificationCategory": "Certification Category",
    "FD_CertificationLevel": "Certification level",
    "FD_ChangeAction": "Change action",
    "FD_ChangeActionMultiLineDisplay01": "for more than one change behavior record or manager change record, use multiple lines to display",
    "FD_ChangeActionMultiLineDisplay02": "for more than one change behavior record, use multiple lines to display",
    "FD_ChangeActionNotes": "Remarks for change action",
    "FD_ChangeActionRules01": "The employee no. can be changed when the change action happens on employment type/change a legal entity. The employee no. will change back to the original one if the change action was deleted.",
    "FD_ChangeAffiliation": "Change affiliation",
    "FD_ChangeAfterAdjustment": "Please make adjustments before making changes.",
    "FD_ChangeAreaOrFactory": "Change area/factory type",
    "FD_ChangeCertificateInfoEform": "Change certificate data eForm",
    "FD_ChangeContactInfoEform": "Change contact data eForm",
    "FD_ChangeCostCenter": "Change cost center",
    "FD_ChangeData": "Change data",
    "FD_ChangeDottedLineManager": "Change dotted line manager",
    "FD_ChangeEducationInfoEform": "Change educational background eForm",
    "FD_ChangeEform": "Change eForm",
    "FD_ChangeEmployeeNumberRule": "Change employee ID only allow transcation record in employment type or legal entity.",
    "FD_ChangeHeadcount": "Change headcount number",
    "FD_ChangeHistory": "Change history",
    "FD_ChangeJobBandAndJobGrade": "Change job grade and job level",
    "FD_ChangeJointManagers": "Change joint managers",
    "FD_ChangeManager": "Change manager",
    "FD_ChangeNotes": "Remarks for changes",
    "FD_ChangeNotesError": "Remarks for changes is incorrect",
    "FD_ChangePeriod": "Change period",
    "FD_ChangePersonalInfoEform": "Change personal data eForm",
    "FD_ChangePhoto": "Change photo",
    "FD_ChangeProfitCenter": "Change profit center",
    "FD_ChangeReasonBlank": "Change reason is blank",
    "FD_ChangeRecords": "Change history",
    "FD_ChangeRecordsHasDeleted": "The change record has been deleted and so did the employee account. Please go to edit account at account management page in order to activate the account.",
    "FD_ChangeRemarks": "Remarks for change",
    "FD_ChangeUnitCode": "Change unit code",
    "FD_ChangeUnitLevel": "Change unit level",
    "FD_ChangeUnitManager": "Change unit manager",
    "FD_ChangeUnitSecretary": "Change unit secretary",
    "FD_ChangeUnitType": "Change unit type",
    "FD_ChangeUpDottedLineUnit": "Change upper level for dotted line unit",
    "FD_ChangeWorkExperiencesEform": "Change working experience eForm",
    "FD_CheckIncorrectData": "Incorrect data check",
    "FD_CloseOrder": "Discontinued orders",
    "FD_CodeAlreadyExists": "The code already exists",
    "FD_CommentsToCompany": "Any comments to the company",
    "FD_CompanyAbbrNotMoreThanCharators": "Company short name cannot exceed {0} characters",
    "FD_CompanyCode": "Company code",
    "FD_CompanyCodeNotMoreThanCharators": "Company code length cannot exceed {0} characters",
    "FD_CompanyEmailError": "Company email address is incorrect",
    "FD_CompanyEmailIsLoginAccount01": "Your log in account is your company email address, and if you want to change the account you can reset at account management page.",
    "FD_CompanyEmailIsLoginAccount02": "Your Apollo account is your company email address",
    "FD_CompanyError": "Company invalid",
    "FD_CompanyLocation": "Company location",
    "FD_CompanyLocationError": "Company location is incorrect",
    "FD_CompanyMailBlank": "Company email address is blank",
    "FD_CompanyMailIsLoginAccount": "You log in account is Company email, please contact Apollo service contact if you would like to change.",
    "FD_CompanyNameError": "Company name invalid",
    "FD_CompanyPolicies": "Company Policies",
    "FD_CompanyPolicy001": "I agree if I fail to follow the company policy for requesting reinstatement on time, this will be considered as an unconditional resignation of my employment after the end of my leave without pay period.",
    "FD_CompanyPolicy002": "Please be mindful for this expiring contract list that contains employees who are going to renew their second time fixed term employment contracts.",
    "FD_CompanyPolicy003": "This employee is still in the role of the unit manager, please adjust the unit position first before making any changes",
    "FD_CompanyTypeBlank": "Legal Entity is blank",
    "FD_CompanyTypeCodeBlank": "Code of Legal Entity is blank",
    "FD_CompanyTypeCodeError": "Code of Legal Entity is incorrect",
    "FD_CompanyTypeError": "Legal Entity is incorrect",
    "FD_CompanyTypeSettings": "Legal Entity settings",
    "FD_ConductAmount": "Rewards and discipline amount",
    "FD_ConductDate": "Rewards and discipline date",
    "FD_ConductReason": "Rewards and discipline reason",
    "FD_ConductRecord": "Rewards and discipline record",
    "FD_ConductRecordlist": "Rewards and discipline record",
    "FD_ConductType": "Rewards and discipline type",
    "FD_ConfirmAbandonOrder": "Are you sure you want to give up the order? You can still come back to order before the deadline.",
    "FD_ConfirmOnBoard": "Confirm on board",
    "FD_ConfirmOnBoardDocument": "Confirm on board document",
    "FD_ConfirmOrder": "Confirm to place the order",
    "FD_ConfirmStay": "Confirm",
    "FD_ConfirmTermination": "Determined to resign",
    "FD_ConfirmTerminationDate": "Determined the date of resign",
    "FD_ConfirmTerminationDateOrNot": "Confirm resignation?",
    "FD_ContactInfo": "Contact information",
    "FD_ContactInfoNotes": "Remarks for contact data",
    "FD_ContactPhoneNumberError": "Contact number invalid",
    "FD_ContainStayStaff": "include employee status in leave without pay",
    "FD_ContentNotBlank": "The content is required",
    "FD_ContractedUnit": "Contracted unit",
    "FD_ContractedUnitBlank": "Contracted unit is blank",
    "FD_ContractedUnitError": "Contracted unit is incorrect",
    "FD_ContractEndDate": "Contract end date is {0}, {1}",
    "FD_ContractEndDate02": "Contract due date",
    "FD_ContractEndDateBlank": "Contract due date is blank",
    "FD_ContractEndDateError": "Contract due date is incorrect",
    "FD_ContractExpired": "Expiring contract",
    "FD_ContractExpiredList": "Expiring contract list",
    "FD_ContractInfo": "Contract data",
    "FD_ContractInfolist": "Contract list",
    "FD_ContractNotes": "Remarks for contract",
    "FD_ContractNumber": "Contract number",
    "FD_ContractNumberError": "Contract number invalid",
    "FD_ContractsEmpSignUpWithCompany": "The employee has signed a contract with your company as follows",
    "FD_ContractSignsNumberError": "The frequency of contract renewal invalid",
    "FD_ContractStartDate": "Contract start date",
    "FD_ContractStartDateBlank": "Contract start date is blank",
    "FD_ContractStartDateError": "Contract start date is incorrect",
    "FD_ContractTerminationDate": "Contract termination date",
    "FD_ContractType": "Contract type",
    "FD_ContractTypeBlank": "Contract type is blank",
    "FD_ContractTypeError": "Contract type invalid",
    "FD_CopmanyNameBlank": "Copmany name is blank",
    "FD_CostCenterOccupiedConfirm": "Cost center is occupied. Please comfirm the status by cost center report and make the necessary changes.",
    "FD_CostCenterReport": "Cost center report",
    "FD_CountryAreaBlank": "Country/Area is blank",
    "FD_CountryAreaCodeBlank": "Country/Area code is blank",
    "FD_CountryAreaCodeError": "Country/Area code is incorrect",
    "FD_CountryAreaError": "Country/Area is incorrect",
    "FD_CountyCityBlank": "county/city is blank",
    "FD_CountyCityCodeBlank": "county/city code is blank",
    "FD_CountyCityCodeError": "county/city code is incorrect",
    "FD_CountyCityError": "county/city is incorrect",
    "FD_CreateAccount": "Create an account",
    "FD_CreateAccountDescription": "Remarks for creating an account",
    "FD_CreateAttendance": "Create attendance data",
    "FD_CreateAttendanceDescription": "Remarks for attendance data",
    "FD_CreateChangeRecord": "Create a new change record",
    "FD_CreateIncomeTax": "Create income tax data",
    "FD_CreateIncomeTaxDependant": "add Dependents information for income tax",
    "FD_CreateIncomeTaxDependantDescription": "add Dependents information remarks for income tax",
    "FD_CreateIncomeTaxDescription": "Notes for creating income tax data",
    "FD_CreateLaborInsurance": "Create labor insurance data",
    "FD_CreateLaborInsuranceDescription": "Remarks for creating labor insurance data",
    "FD_CreateLaborPension": "Create labor pension data",
    "FD_CreateLaborPensionDescription": "Remarks for creating labor pension data",
    "FD_CreateLeaveHoursDescription": "Remarks for creating leave hours",
    "FD_CreateLeaveHoursFailed": "Failed to create leave hours, please contact the attendance administrator",
    "FD_CreateNewAdvertisement": "Create a new advertisement",
    "FD_CreateNewUnit": "Create a new unit",
    "FD_CreateNewUnit02": "Create a new unit",
    "FD_CreateNHI": "Create NHI data",
    "FD_CreateNHIdependentsEnrollment": "Create NHI dependents enrollment data",
    "FD_CreateNHIdependentsEnrollmentDescription": "Remarks for creating NHI dependents enrollment data",
    "FD_CreateNHIDescription": "Remarks for creating NHI data",
    "FD_CreateProvidentFund": "Create provident fund data",
    "FD_CreateProvidentFundDescription": "Remarks for creating provident fund data",
    "FD_CreateRegister": "Create on board information",
    "FD_CreateSalary": "Create payroll data",
    "FD_CreateSalaryDescription": "Remarks for creating payroll data",
    "FD_CreateSocialSecurity": "Create social security data",
    "FD_CreateSocialSecurityDescription": "Remarks for creating social security data",
    "FD_CredentialsData": "ID information",
    "FD_CredentialsExpireDateBlank": "ID expiration date {0} is blank",
    "FD_CredentialsNumberBlank": "ID number {0} is blank",
    "FD_CredentialsPersonnelData": "ID and personnel information",
    "FD_CredentialsTypeBlank": "ID type {0} is blank",
    "FD_CustomizeAdvertisement": "Customize Advertisement",
    "FD_DataHasConfirmed": "Data has been confirmed",
    "FD_DataImportPAEnableAccountFailure": "Employee No. {0} data has been imported, but the account activation was fail.",
    "FD_DataImportPAEnableNoAccount": "Employee No. {0} data has been imported, but no account is provided.",
    "FD_DefaultEmployeeNumber": "Preset employee number",
    "FD_DelegatedBuyer": "Delegated buyer",
    "FD_DelegatedBuyerDesc": "If you are a delegated buyer, please note the name of original buyer. (You need to order separately if there are more than one buyers) : ",
    "FD_DelegationStatus": "Appointed  status",
    "FD_DeleteEmployeeNumber01": "You will not be able to use nor create any changes after you delete this employee no., are you sure you want to delete it?",
    "FD_DeleteUnitCode01": "You will not be able to use nor create any changes after you delete this unit code, are you sure you want to delete it?",
    "FD_DeptAnnouncement": "Department announcement",
    "FD_DeptCodeAlreadyUsed": "Unit code {0} is already in use",
    "FD_DeptCodeCannotBeRepeated": "Department code cannot be repeated",
    "FD_DeptCodeIsNotExistInDeptData": "The department code or upper department code {0} does not exist in the department data, please check the {1} data;",
    "FD_DeptCodeLimitEnglishNumber": "Unit code limit to letter or numberic characters",
    "FD_DeptCodeMustHasOneData": "Department code {0} must have a {1} data, please check it;",
    "FD_DeptDataCountyWrong": "Department information No. {0} The name of the county is incorrect or does not belong to the country filled in;",
    "FD_DeptDataDeptCategoryCodeWrong": "Department data item row {0} department category code invalid;",
    "FD_DeptDataDeptLeaveCodeWrong": "Department data No. row {0} department level code invalid;",
    "FD_DeptDataFactoryTypeCodeWrong": "Department data No. row {0} factory code invalid;",
    "FD_DeptDataNationalCodeWrong": "Department data No. {0} country code error;",
    "FD_DeptDataProfitCenterCodeWrong": "Department data item row {0} profit center invalid;",
    "FD_DeptEffectiveDate": "Unit effective date",
    "FD_DeptIdIsSameUsUpperDept": "Department Id is the same as the superior department Id",
    "FD_DeptIsExistAddDeptChange": "The unit already exists. Please use the change form.",
    "FD_DeptLevelHigherFewLevelsThanUpper": "Department [{0}] level [{1}] is higher than or equal to his upper department [{2}] level [{3}]",
    "FD_DeptNotFoundOppositeSupervisor": "Department [{0}] could not find corresponding supervisor [{1}]",
    "FD_DeptNotFoundOppositeUpperNote": "Department [{0}] could not find the corresponding upper department [{1}], if department [{0}] is the highest level, please fill in NA",
    "FD_DeptNoUpperDirectlyDeptZero": "Department {0} has no parent department, and the port number (the position for direct manager), please fill in 0;",
    "FD_DesignatedWork": "Designated work",
    "FD_DesignatedWorkContent": "Designated work contents",
    "FD_DesignateTo": "Designate to",
    "FD_Designator": "Designator",
    "FD_DetermineReinstatement": "determined to reinstate",
    "FD_DirectlyDeptWrong": "The port number of the department code {0} ((the position for direct manager) is incorrect;",
    "FD_DirectSubordinate": "My direct subordinate",
    "FD_DisplayContent": "Display Content",
    "FD_DisplayManager": "Display the manager who's position number is 00、01、02",
    "FD_DisplayManagerPic": "Display the manager's photo ",
    "FD_DisplayName": "display name",
    "FD_DisplayNameBlank": "Display name is blank",
    "FD_DisplayNameError": "Display name incorrect",
    "FD_DivideTheUnit": "Divide the unit",
    "FD_DocumentExpired": "The following documents are about to expire, please handle them as soon as possible, thank you.",
    "FD_DocumentNotSubmit01": "You have not submited your {0} on board document, please submit it as soon as possible. Thank you.",
    "FD_DocumentNotSubmit02": "The employees below have not submitted the on board document, relevant information as follow:",
    "FD_DottedLineManager": "Dotted line manager",
    "FD_DottedLineManagerBlank": "Dotted line manager is blank",
    "FD_DottedLineManagerError": "Dotted line manager is invalid",
    "FD_DottedLineManagerId": "Dotted line manager ID",
    "FD_DottedLineManagerIdError": "Dotted line manager ID is invalid",
    "FD_DottedLineManagerInactive": "Dotted line manager is inactive",
    "FD_DottedLineManagerName": "Dotted line manager name",
    "FD_DottedLineManagerType": "Dotted line manager type",
    "FD_DottedLineManagerTypeBlank": "Type of Dotted line manager is blank",
    "FD_DottedLineManagerTypeError": "Dotted line manager type is invalid",
    "FD_DownloadOnBoardResult": "Download the on board result",
    "FD_DownloadRegistrationDetail": "Download the registration sheet",
    "FD_DownloadSelectedItems": "Download selected items",
    "FD_DuplicateEmployeeData": "Duplicate employee data",
    "FD_DynamicMessage": "News feed",
    "FD_EarliestWorkDate": "Earliest working experience",
    "FD_EarliestWorkDateError": "Earliest working experience is incorrect",
    "FD_EditAnnouncement": "Edit announcement",
    "FD_EditCertificateInfo": "Edit license or certificate data",
    "FD_EditConductRecord": "Edit rewards and discipline records",
    "FD_EditContactInfo": "Edit contact data",
    "FD_EditContractInfo": "Edit contract data",
    "FD_EditDate": "Modify date",
    "FD_EditEducationInfo": "Edit educational background ",
    "FD_EditExpatriateInfo": "modify expatriate information",
    "FD_EditNews": "Edit news",
    "FD_EditOrder": "Edit order",
    "FD_EditPersonalInfo": "Edit personal data",
    "FD_EditPolicy": "Edit policy",
    "FD_EditProbationInfo": "Edit probation data",
    "FD_EditSeniorityInfo": "Edit year of service data",
    "FD_EditStaffStatusToExpectedToLeave": "Change resignation status?",
    "FD_EditStaffStatusToExpectedToStay": "Are you sure to change the status of the person to leave without pay?",
    "FD_EditStayingPeriods": "Modify leave without pay period",
    "FD_EditTerminationDate": "modify resignation date",
    "FD_EditTerminationDay": "modify resignation date",
    "FD_EditToExpectedToLeave": "change to expected resignation",
    "FD_EditToExpectedToStay": "modify to leave without pay status",
    "FD_EditUnitInfo": "Edit unit informatiion",
    "FD_EditVendor": "Edit vendors",
    "FD_EditWorkExp": "Edit working experience",
    "FD_EducationalPeriodError": "Duration of education is incorrect",
    "FD_EducationalStatusBlank": "Educational status is blank",
    "FD_EducationalStatusError": "Educational status is incorrect",
    "FD_EducationalTypeError": "Educational type is incorrect",
    "FD_EducationBlank": "Education is blank",
    "FD_EducationCertificate": "Education, License or Certificate",
    "FD_EducationDataMultiLineDisplay": "for more than one educational background use multiple lines to display",
    "FD_EducationDegree": "Academic degree",
    "FD_EducationError": "Education is incorrect",
    "FD_EducationNotes": "Note for educational background ",
    "FD_EduDataDeptCategoryWrong": "educational background No. row {0} Department of major invalid;",
    "FD_EduDataEduCategoryWrong": "educational background row {0} education category invalid;",
    "FD_EduDataSchoolAttendanceWrong": "educational background : {0} data is in an incorrect state of attendance;",
    "FD_EffectiveDateBlank": "Effective date is blank",
    "FD_EffectiveDateError": "Effective date is incorrect",
    "FD_EffectiveDateForTerminationOrLOA": "The effective date will be the day after the last working day for Resignation or leave without pay",
    "FD_EffectiveDateNotBehindTheLatest": "The effective date is not later than the latest change record",
    "FD_EffectiveDayError": "Effective date is incorrect",
    "FD_EformForLOA": "leave without pay form",
    "FD_EformForLOAExtension": "eForm for leave without pay extension",
    "FD_EformForReturnToWork": "reinstatement form",
    "FD_EformForTerminationOrLOA": "eForm for Resignation or leave without pay",
    "FD_ElectronicFile": "Electronic file",
    "FD_EmailAddress": "Email address",
    "FD_EmergencyContactError": "Emergency contact is incorrect",
    "FD_EmpDataCareerCategoryWrong": "Employee data row {0} :  Invalid job type;",
    "FD_EmpDataCareerGradeWrong": "Employee data row {0} :  Invalid job level;",
    "FD_EmpDataCareerGroupWrong": "Employee data row {0} :  Invalid job role;",
    "FD_EmpDataCareerLevelWrong": "Employee data row {0} : invalid job level;",
    "FD_EmpDataCareerPositionWrong": "Employee data row {0} :  Invalid job title;",
    "FD_EmpDataCareerSeriesWrong": "Employee data row {0} : invalid job family;",
    "FD_EmpDataCompanyOtherNameWrong": "Employee data row {0} : name of Legal Entity invalid;",
    "FD_EmpDataCountyWrong": "Employee data row {0} :  Invalid city name or not in the country group;",
    "FD_EmpDataExternalPositionWrong": "Employee data row {0} : Invalid external job title；",
    "FD_EmpDataFactoryTypeCodeWrong": "Employee data row {0} : Invalid factory Code;",
    "FD_EmpDataIDCategoryWrong": "Employee data row {0} :  Employment Type invalid;",
    "FD_EmpDataIDSubCategoryWrong": "Employee data row {0} :  Employment Subtype invalid;",
    "FD_EmpDataNationalCodeWrong": "Employee data row {0} : Invalid country code；",
    "FD_EmpIDAtMostOneHighestEducation": "The employee number {0} can only have a maximum of one grade of information;",
    "FD_EmpIDCompanyDaysBeforeHireWrong": "Employee No. {0} day of recongnize company days before hiring is invalid;",
    "FD_EmpIDHasOneData": "Employee No. {0} must have at least {1} information, please check;",
    "FD_EmpIDIsNotExist": "Employee No. {0} does not exist;",
    "FD_EmpIDIsNotExistInEmpDatas": "Employee No. {0} does not exist in employee data, please check {1};",
    "FD_EmpIDMustHasOneHighestEducation": "Employee number {0} must have a piece of information for the highest level of education;",
    "FD_EmpIDSeniorityBeforeHireWrong": "Employee No. {0} : Invalid format for Approved year of service(job grade)  before hiring",
    "FD_EmployedCertificate": "Employed Certificate",
    "FD_EmployedTerminationCertificate": "Employed & Termination Certificate",
    "FD_EmployeeChangeEform": "Employee change eForm",
    "FD_EmployeeChangeRecord": "Employee Change Record",
    "FD_EmployeeChangeReport": "Employee change report",
    "FD_EmployeeComments": "Employee comments",
    "FD_EmployeeDataConfirmed": "The employee data has been confirmed",
    "FD_EmployeeDataError01": "Employee data - the company email address already exists, please change the data and try the on board process again",
    "FD_EmployeeDataError02": "Employee data - the ID number already exists, please change the data and try the on board process again",
    "FD_EmployeeDataError03": "Employee data - the affiliated unit has expired or about to be expire please modify the data and try the on board process again",
    "FD_EmployeeDataError04": "Employee data - the direct manager does not exist, please change the data and try the on board process again",
    "FD_EmployeeDataError05": "The People+ employee data import has failed, please connect with Apollo service contact",
    "FD_EmployeeDataIsCreated": "employee information has been generated, please verify in the Hire list",
    "FD_EmployeeDataIsExistPlsSortByEffectiveDate": "the personnel data already exsit, The same personnel data should be sorted according to the effective date.",
    "FD_EmployeeDataSuccessfullyImported": "The employee data has successfully imported",
    "FD_EmployeeIDBlank": "Employee ID is blank",
    "FD_EmployeeIDInitialCode": "Employee ID initial code",
    "FD_EmployeeIDInitialCodeBlank": "Employee ID initial code is blank",
    "FD_EmployeeIDInitialCodeError": "Employee ID initial code is incorrect",
    "FD_EmployeeIdSeriesNumber": "Employee ID series number",
    "FD_EmployeeIdSeriesNumberBlank": "Employee ID series number is blank",
    "FD_EmployeeIdSeriesNumberError": "Employee ID series number is incorrect",
    "FD_EmployeeIdUsed": "This employee no. has been used",
    "FD_EmployeeNoNeedMaintainAttendanceOrPayroll": "no need to maintain attendance or payroll setting",
    "FD_EmployeeNumberIsDuplicatedPleaseReconfirm": "the employee number is duplcated, please reconfirm",
    "FD_EmployeeNumberIsLoginAccount": "Employee ID is Apollo login account",
    "FD_EmployeeOrgData": "employee and Organization information",
    "FD_EmployeesUnconfirmedOnBoardDate": "these member has not confirm the on board date, confirm to proceed?",
    "FD_EmploymentType": "Type of employment",
    "FD_EmploymentTypeBlank": "Type of employment is blank",
    "FD_EmploymentTypeError": "Type of employment is incorrect",
    "FD_EnableDisableStatus": "Active/Inactive Status",
    "FD_EndOfLOA": "The period of leave without pay has been finish.",
    "FD_EndOfTheProbationaryList": "The employee list on probation",
    "FD_EndOfTheProbationPeriod": "probation period ending",
    "FD_EndTime": "End time",
    "FD_EnglishNameError": "English name incorrect",
    "FD_EntitlementHoursLessThenUsed": "The entitlement hours is less than used hours, please contact the attendance administrator.",
    "FD_EstimatedDepartureDateInterval": "Estimate resignation period",
    "FD_EstimatedStayEndDateInterval": "Estimate leave without pay end date",
    "FD_EstimatedStayStartDateInterval": "Estimate leave without pay start date",
    "FD_EstimatedTimeCloseOrder": "Estimated time to close this order",
    "FD_Ethnicity": "Ethnicity",
    "FD_EthnicityError": "Ethnicity is incorrect",
    "FD_Example": "Example",
    "FD_ExampleData": "Braised pork on rice, 40<br />Green tea, M 40, L 50<br />{0}Milk tea selections{1}<br />Coconut jelly milk tea, 40<br />Hazelnut milk tea, 45<br />{0}Special flavor selections{1}<br />Passion fruite green tea, 30<br />Plum green tea, 30<br />Spicy fried squid rings, 50",
    "FD_ExpatriateData": "expatriate data",
    "FD_ExpatriateEmployee": "expatriate employee",
    "FD_ExpatriateEndDate": "expatriate end date",
    "FD_ExpatriateExpired": "expatriate expire",
    "FD_ExpatriateInfo": "expatriate information",
    "FD_Expatriatelist": "expatriate data",
    "FD_ExpatriateManager": "expatriate manager",
    "FD_ExpatriateManagerId": "expatriate manager ID",
    "FD_ExpatriateManagerName": "expatriate manager name",
    "FD_ExpatriateNotes": "expatriate remarks",
    "FD_ExpatriatePeriod": "expatriate period",
    "FD_ExpatriateStartDate": "expatriate start date",
    "FD_ExpatriateTo": "expatriate to",
    "FD_ExpectedEndPeriods": "Estimated end interval",
    "FD_ExpectedReinstatementDate": "Expected reinstatement day",
    "FD_ExpectedReinstatementNotify": "Expected reinstatement notice",
    "FD_ExpectedStartDate": "Estimated start date",
    "FD_ExpectedStartPeriods": "Estimated start interval",
    "FD_ExpectedStayEffectiveDate": "Expected effective date of leave without pay",
    "FD_ExpectedStayEndDate": "Expected end date of  leave without pay",
    "FD_ExpectedStayingPeriods": "expected period of  leave without pay",
    "FD_ExpectedStayNotify": "leave without pay notification",
    "FD_ExpectedTerminationDate": "Estimate resignation date",
    "FD_ExpectedTerminationNotify": "Notice for estimate resignation employee(s)",
    "FD_ExpectedToLeave": "Estimate Resignation",
    "FD_ExpectedToLeaveArea": "Expected resignation area",
    "FD_ExpectedToReinstate": "Expected reinstatement",
    "FD_ExpectedToStay": "expected leave without pay",
    "FD_ExpirationDate": "Expiration date",
    "FD_ExpirationDate01": "Expiration Date",
    "FD_ExpirationDateError": "Expiration date is incorrect",
    "FD_ExpirationPeriod": "Expiration period",
    "FD_ExpirationSearch": "Expiration search",
    "FD_ExpirationTime": "Expiration Time",
    "FD_ExpiredMailNotify": "Notification email for expiring documents",
    "FD_ExpiryDateError": "due date invalid",
    "FD_ExtBlank": "Extension number is blank",
    "FD_Extended": "Extended",
    "FD_ExtendedPeriods": "Extension period",
    "FD_ExtendProbationPeriod": "Extend probation period",
    "FD_ExtensionData": "Extension data",
    "FD_ExtensionOfTheProbationPeriod": "Extension date of the probation period",
    "FD_ExternalJobTitle": "External job title",
    "FD_ExternalJobTitleBlank": "External job title is blank",
    "FD_ExternalJobTitleError": "External job title is incorrect",
    "FD_ExtError": "Extension number invalid",
    "FD_FactoryTypeError": "Factory type is incorrect",
    "FD_FamilyMember": "Family member",
    "FD_FieldName": "Name of Field",
    "FD_FileLink": "File Link",
    "FD_FileNo": "Document number",
    "FD_FillInInsuranceData": "Insurance information needs to be filled in",
    "FD_FirstNameBlank": "first name is blank",
    "FD_FirstNameError": "first name incorrect",
    "FD_FormatLimitJPGJPEGGIFPNG": "Format Limit: JPG, JPEG, GIF, PNG",
    "FD_ForWork": "Homework",
    "FD_Friend": "Friend",
    "FD_GenderCodeBlank": "Gender code is blank",
    "FD_GenderCodeError": "Gender code is incorrect",
    "FD_GiveUpOnBoard": "withdraw on board",
    "FD_GiveUpOnBoardReason": "reason for withdraw on board",
    "FD_GuestNumber": "Number of guests",
    "FD_HaveOrdered": "You joined [{0}]. The vendor was {1}, and below item was what you have ordered. Please leave a rating :",
    "FD_HaveSubEmployees": "There are subordinate units and employees",
    "FD_HaveSubUnitAndEmployees": "There are subordinate units and employees, please process relevant adjustment first",
    "FD_Headcount": "Headcount",
    "FD_HighestEducationBlank": "Highest degree is blank",
    "FD_HighestEducationError": "Highest degree invalid",
    "FD_HighestLevel": "Highest reporting level",
    "FD_HireBatchImportErrorDescription": "Hire batch import exception description",
    "FD_HireBatchImportTemplate": "Hire batch import template",
    "FD_IDDocExpiryDate": "ID {0} expiration date",
    "FD_IDDocNumber": "document {0} number",
    "FD_IDDocType": "ID type {0}",
    "FD_IdentityTypeBlank": "Employment type is blank",
    "FD_IdentityTypeError": "Employment type is incorrect",
    "FD_IDExpirationDate": "ID expiration date",
    "FD_IDExpirationDateError01": "ID expiration date {0} is incorrect",
    "FD_IDExpirationDateError02": "ID expiration date is incorrect",
    "FD_IDExpirationList": "ID expiration list",
    "FD_IdExpired": "Expiring ID",
    "FD_IDIsExist": "ID number already exists",
    "FD_IDName": "ID name",
    "FD_IDNumberBlank": "ID number is blank",
    "FD_IDNumberError01": "ID number {0} is incorrect",
    "FD_IDNumberError02": "ID number is incorrect",
    "FD_IDPersonnelInformation": "ID and personnel information",
    "FD_IdSubtypeBlank": "Identity is blank",
    "FD_IdSubtypeError": "Employment subtype is incorrect",
    "FD_IdType": "ID type",
    "FD_IDTypeBlank": "ID type is blank",
    "FD_IDTypeError01": "ID type is {0} incorrect",
    "FD_IDTypeError02": "ID type is incorrect",
    "FD_IDWillBeExpired": "Your ID will be expired soon",
    "FD_Import": "Import",
    "FD_ImportForWork": "Import job",
    "FD_ImportSystemStaff": "Import system personnel",
    "FD_InactiveParameterCannotEdit": "Can't edit Inactive  Parameter",
    "FD_IncludingExpirationUnit": "Including expiration unit",
    "FD_IncompleteDocumentsList": "Incomplete on board documents list",
    "FD_IncompleteDocumentsNotify": "Notification for incomplete on board documents",
    "FD_IncompleteOnBoardDocuments": "Incomplete on board documents",
    "FD_IncorporateAnotherUnit": "Incorporate into another unit",
    "FD_IndustrySubtype": "Industry subtype",
    "FD_IndustrySubtypeError": "Industry subtype is incorrect",
    "FD_IndustryTypeError": "Industry type is incorrect",
    "FD_InformationChangReimport": "The existing information has been changed. The error message as follows,Please re-import.",
    "FD_InitialJobBand": "Initial job grade",
    "FD_Initiator": "Initiator",
    "FD_InProbationPeriod": "During probation period",
    "FD_InstantTransferFailedEmail": "Instant transfer failed, the error reason has been sent to the performer's email address, please adjust and re-execute.",
    "FD_InterfaceTransferError": "Interface conversion error: {0}",
    "FD_InternalCompanyYOSBlank": "Approved year of service is blank",
    "FD_InternalCompanyYOSError": "Approved year of service is incorrect",
    "FD_InternalLink": "Internal Link",
    "FD_InternalYOSStartDate": "year of service (internal) begin date",
    "FD_InvalidOrderTime": "Not in a valid ordering time, please select other meal ordering activities.",
    "FD_IsAnyRestrictionForBuyers": "Set eligibility for audiences to order",
    "FD_IsCheckInBlank": "Check in/out is blank or not",
    "FD_IsCheckInError": "Is check in/out incorrect",
    "FD_IsNeedRegister": "Registration needed or not",
    "FD_IsObtainedEducationDegree": "Higher education degree has been obtained",
    "FD_IsObtainedEducationDegreeError": "Higher education degree has been obtained is incorrect",
    "FD_IsOrderClose": "The order [ {1} ] initiated by {0} has ended on the anticipated closing time, do you want to close this order?<br />(This order will be deleted after you click closing an order and will be no longer retained)",
    "FD_IsOrderCloseMailScoreToBuyer": "Send review to the purchaser when order is closed",
    "FD_IsRehiringEmployeesWhetherCopyExistingData": "This person is a rehired employee. Do you need to copy existing data ?",
    "FD_IssuingAuthortyError": "Issuing authority invalid",
    "FD_Items": "Items",
    "FD_IWantToOrder": "Order now",
    "FD_JobAssignmentError": "Job role is invlaid",
    "FD_JobAssignmentYOSStartDate": "year of service (Job Role) begin date",
    "FD_JobBandError": "Job grade is incorrect",
    "FD_JobBandYOSStartDate": "year of service (Job Grade) begin date",
    "FD_JobBankBlank": "Job grade is blank",
    "FD_JobFamilyBlank": "Job family is blank",
    "FD_JobFamilyError": "Job family is incorrect",
    "FD_JobGradeError": "Job level in incorrect",
    "FD_JobGradeYOSStartDate": "year of service (Job level) begin date",
    "FD_JobPositionCodeError": "Job position code is incorrect",
    "FD_JobPositionInfoNotes": "Remarks for job position data",
    "FD_JobPositionSeriesNumber": "Job position series number",
    "FD_JobResponsibilityDescription": "Job function",
    "FD_JobStreamBlank": "Job series is blank",
    "FD_JobStreamError": "Job series is incorrect",
    "FD_JobTitleBlank": "Job title is blank",
    "FD_JobTitleError": "Job title is incorrect",
    "FD_LaborContract": "Labor contract",
    "FD_LanguageOpenSetting": "Language accessible settings",
    "FD_LastNameBlank": "last name is blank",
    "FD_LastNameError": "last name incorrect",
    "FD_Leaved": "resigned",
    "FD_LeaveWithoutPayReport": "Leave without pay report",
    "FD_LegalPersonEffectiveDate": "Legal entity effective date",
    "FD_Level": "Level",
    "FD_LevelInfo": "Level data",
    "FD_LevelUpDottedLineUnit": "upper of the dotted line unit",
    "FD_LicenseNameError": "Certificate Name invalid",
    "FD_Limit": "Limited",
    "FD_LimitedNumberOfPeople": "Limited number of participants",
    "FD_LimitPeopleNumber": "Limited number of participants",
    "FD_LineManagerId": "Direct manager ID",
    "FD_LOAInfo": "leave without pay data",
    "FD_LOAReason": "reason for leave without pay",
    "FD_MainCostCenter": "Primary Cost Center",
    "FD_MainCredentials": "Primary Identification",
    "FD_MainCredentialsOnlyValueIdentifiesExistence": "the main document is the unique value of the system in indentify the employee",
    "FD_Major": "Education major",
    "FD_MajorNameBlank": "Name of education major is blank",
    "FD_MajorNameError": "Major invalid",
    "FD_MajorTypeBlank": "Department of major is blank",
    "FD_MajorTypeError": "Department of major is incorrect",
    "FD_ManageExpectedStayStaff": "Manage employee(s) whom request leave without pay ",
    "FD_ManageLeavedStaff": "Manage resignation employees",
    "FD_ManagementOrders": "Managing orders",
    "FD_ManagerComments": "Manager comments",
    "FD_ManagerId": "Manager ID",
    "FD_ManagerInfo": "Manager data",
    "FD_ManagerNameError": "Manager name invalid",
    "FD_ManagerTitle": "Manager title",
    "FD_ManagerWillBeLeaveOrStay": "Manager will be in status of resignation or leave without pay",
    "FD_ManageStayingStaff": "Manage employee(s) whom in the status of  leave without pay ",
    "FD_ManagingCompanyPolicies": "Company policy management",
    "FD_ManagingNews": "News management",
    "FD_ManagingPolicies": "Policy management",
    "FD_ManagingRegistration": "Managing registration",
    "FD_MaritalStatusError": "Marital status is incorrect",
    "FD_MaterialStatusBlank": "Marital status is blasnk",
    "FD_MealOrderingSystem": "Meal ordering system",
    "FD_Mentor": "Mentor",
    "FD_MentorDisplayName": "Mentor's display name",
    "FD_MentorEnglishName": "Mentor's English name",
    "FD_MentorID": "Mentor ID",
    "FD_MentorIDWrong": "Mentor ID is wrong",
    "FD_MentorInactive": "Mentor is inactive",
    "FD_MenuQuantity": "Quantity of menu",
    "FD_MiddleName": "Middle name",
    "FD_MiddleNameError": "middle name incorrect",
    "FD_MilitaryServicePeriodError": "Military service period is incorrect",
    "FD_MilitaryServiceStatusBlank": "Military status is blank",
    "FD_MilitaryServiceTypeError": "Type of military service is incorrect",
    "FD_MilitaryStatusError": "Military status is incorrect",
    "FD_ModifyLegalPerson": "Change legal entity",
    "FD_ModifyNotOnBoard": "change to not on board yet",
    "FD_ModifyReinstatementDate": "modify reinstatement date",
    "FD_ModifyReinstatementDay": "modify reinstatement day",
    "FD_MyShortcuts": "My shortcuts",
    "FD_NameBlank": "Name is blank",
    "FD_NameError": "Name invalid",
    "FD_NationalityCodeError": "Nationality code is incorrect",
    "FD_NewChangeHasNoData": "No additional information to be added",
    "FD_NewOrder": "Latest Order",
    "FD_NewsCategory": "News category",
    "FD_NewsKinds": "Message category",
    "FD_NewsMsg": "Latest News",
    "FD_NewUnitHasNoData": "No units to be added",
    "FD_No": "No.",
    "FD_NoLicenseInformation": "No license or certificate data",
    "FD_NoLimit": "Unlimited",
    "FD_NoMatchesEmployeeData": "Cannot find the corresponding person data",
    "FD_NonInitialJobBand": "Non initial job grade",
    "FD_NoPermission": "no data permission on {0}",
    "FD_NoPersonnelJobInformation": "No job role found",
    "FD_NotConfirmData": "Not yet confirm data",
    "FD_NotEffectiveYet": "Not effective yet",
    "FD_NoteforAndPunishment": "Note for rewards and discipline",
    "FD_NotFoundOppositeDeptLevelData": "Cannot get the corresponding DeptLevel data",
    "FD_NotIncludedInSystemCompleteOnBoarding": "not include employee that already on board.",
    "FD_NotOnBoard": "Not yet on board",
    "FD_NumberAlreadyExists": "Number already exists",
    "FD_NumbersOfContractSignatures": "The frequency of contract renewal",
    "FD_NumbersOfContractSignaturesBlank": "The frequency of contract renewal is blank",
    "FD_Official": "Official",
    "FD_OfficialManager": "Unit manager",
    "FD_OfficialMobileBlank": "Company mobile is blank",
    "FD_OfficialMobileError": "Company mobile invalid",
    "FD_OMPADataTransferException": "OMPA data transfer exception, please correct the data.",
    "FD_OMPADataTransferExceptionNotice": "OMPA data transfer exception notice",
    "FD_OnBehalfOf": "On behalf of",
    "FD_OnBoardDateBlank": "Onboard date is blank",
    "FD_OnBoardDateError": "Onboard date invalid",
    "FD_OnBoardDatePeriod": "Period of on board date",
    "FD_OnBoardDayExecuteDataTransport": "Transport data on the on board day",
    "FD_OnBoardEmployeeIdDuplicate": "On board documents-duplicate employee no., please change the data and try the on board process again",
    "FD_OnBoardResult": "On board result",
    "FD_OnBoardResultFailed01": "The attendance data has not yet been created due to the unsuccessful on board result",
    "FD_OnBoardResultFailed02": "The payroll data has not yet been created due to the unsuccessful on board result",
    "FD_OnBoardResultFailed03": "The personnel file transfer has not yet processed due to the unsuccessful on board result",
    "FD_OnBoardResultInfo": "Remarks for on board result",
    "FD_OnceWorkDays": "The number of employment days",
    "FD_OneDepartmentOneData": "Only one piece of information can exist in each department (0}",
    "FD_OneEmpOneCommunicationData": "An employee can only have one communication information {0}",
    "FD_OneEmpOneEmpData": "An employee can only have one employee profile ({0}",
    "FD_OneEmpOnePersonalData": "An employee can only have one profile {0}",
    "FD_OneEmpOneSeniorityData": "An employee can only have one annual information {0}",
    "FD_OneLevelUpManager": "upper level Manager",
    "FD_OneLevelUpManagerId": "upper level Manager employee no.",
    "FD_OneLevelUpManagerName": "upper level Manager name",
    "FD_OneLevelUpManagerWillBeInvalid": "The upper manager is about to expire",
    "FD_OneLevelUpUnitWillBeInvalid": "the upper level unit is about to expire",
    "FD_OneOrgAtMostSixPositionData": "A department can only have a maximum of 6 job profiles {0}",
    "FD_OngoingOrder": "Ongoing orders",
    "FD_OnlyEditLastChange": "Cannot modify non-latest transaction orders",
    "FD_OnlyOneEmergencyContact": "Only one emergency contact is needed",
    "FD_OnlyOneHighestEducation": "Only one highest education is needed",
    "FD_OpenRegister": "Allow dependents to register",
    "FD_OperationExecutionTime": "Operation execution time",
    "FD_Order": "Orders",
    "FD_Order02": "Order",
    "FD_OrderAnnouncement": "Order announcement and notification",
    "FD_OrderAnnouncement02": "Order announcement",
    "FD_OrderClosed": "Closed",
    "FD_OrderDate": "Order date",
    "FD_OrderDetails": "Ordered details",
    "FD_OrderingNumber": "Number of orders",
    "FD_OrderNotice": "Order notification",
    "FD_OrgChart": "Organizational chart",
    "FD_OrgCostCenterSigningSupervisorShouldBeNull": "Administration cost center signing Ssupervisor should be null",
    "FD_OrgParameterSettings": "Org+parameter settings",
    "FD_OrgPlusData": "Org+ data",
    "FD_OrgPlusPeoplePlusData": "Org+, People+ data",
    "FD_OriginalRegistrationDate": "Original date",
    "FD_OriginalStayingPeriods": "Original leave without pay period",
    "FD_OriginUnit": "Original Unit",
    "FD_OtherManager": "Other manager",
    "FD_OverallResults": "Overall results",
    "FD_OverallResultsDetails": "OverallResultsDetails",
    "FD_OverGuestLimit": "Limited number of participants cannot be less than allowed number of guests",
    "FD_OverRegistrationLimit": "Limited number of participants cannot be less than number of registered participants",
    "FD_PAAccountActivate01": "The People+ administrator will need to reset the account at account management page on the on board date in order to activate the account.",
    "FD_PAAccountActivate02": "The People+ administrator will need to reset the account at account management page after receive the email information in order to activate the account.",
    "FD_PABatchChangeErrorDescription": "People+ batch change exception description",
    "FD_PABatchChangeTemplate": "People+ batch change template",
    "FD_PADataTransferHasFailed": "The People+ data transfer has failed, please connect with Apollo service contact",
    "FD_Paid": "Paid",
    "FD_PAParameterSettings": "People+parameter settings",
    "FD_ParameterDisabledConfirm": "{0} has been disabled. Please confirm the status of Org+ and People+ by report. If {1} is still in use, please make the necessary changes.",
    "FD_ParameterManagement": "Parameter Management",
    "FD_PayConfirm": "Confirm to pay",
    "FD_PayFailed": "Failed to pay",
    "FD_PaymentManagement": "Payment management",
    "FD_PaymentManagementDetails": "PaymentManagementDetails",
    "FD_PaymentStatus": "Payment status",
    "FD_PayrollDataCreationHasFailed01": "Data creation has failed, please contact the payroll administrator",
    "FD_PayrollDataIsRequiredOrNot": "Payroll data is required or not",
    "FD_PayrollDataIsRequiredOrNotBlank": "Payroll data is required or not is blank",
    "FD_PayrollDataIsRequiredOrNotError": "Payroll data is required or not is incorrect",
    "FD_PeopleEligibleToOrder": "Employees eligible to order",
    "FD_PeoplePlusData": "People+ data",
    "FD_PermanentAddressError": "Residential address invalid",
    "FD_PermissionsSubjectYourCurrentAuthorization": "data permission depens on the scope of your authorization",
    "FD_Person": "Person(s)",
    "FD_PersonalEmailError": "Personal email address is incorrect",
    "FD_PersonalGenderError": "Profile {0} is incorrectly gendered;",
    "FD_PersonalInfoNotes": "Remarks for personal data",
    "FD_PersonalInformationProtectionPolicy": "We have the obligations according to Personal Information Protection Policy and our Company Policy to inform you below terms before you (assist to) provide us your personal information for the activity events that you registered. Please read below details carefully :<br />　Purposes for collecting: Insurance and related administrative management for this activity.<br />　Classification of the personal information:<br />Identify personnel: such as name, contact, etc.<br />　Identify governing information: such as identification card number, passport number, ARC number, etc.<br />　Personal description: such as date of birth, etc.<br />　Time period, area, target and way of the use of personal information:<br />　Time period of the use: until the purpose for collecting no longer exists.<br />　Area of the use: unless the purposes for collecting involve with international business or activity, we will only use your personal information inside the territory of Taiwan.<br />　Target and way of the use: within the purposes for collecting are in necessary scope to use your personal information.<br />　The impact if you chose not to provide your personal information correctly: if you chose not to provide your personal information correctly, we will not be able to provide you related service for specific purpose.<br />I hereby acknowledge and understand above information and agree the purposes for collecting are in necessary scope, which the collection, processing and use of personal information are from and provided under my assistant.",
    "FD_PersonalMarriageStatusError": "Profile {0} marital status error;",
    "FD_PersonalMilitaryServiceError": "Personal data item {0} data military status error;",
    "FD_PersonalMilitaryTypeError": "Personal data section {0} information is incorrect;",
    "FD_PersonalNationalityError": "Personal data row {0} nationality code invalid;",
    "FD_PersonalPaperworkError": "Personal data item {0} has a invalid document type;",
    "FD_PersonnelFile": "Personnel file",
    "FD_PersonnelOrganizationDetails": "Personnel  and organization details",
    "FD_PersonnelStatusError": "Employee status invalid (must be in the status of new hire/resiged/leave without pay)",
    "FD_PersonnelSupervisorDataError": "The manager setting of the personnel  is incorrect, please modify.",
    "FD_PersonnelUnitDataError": "The unit setting of the personnel  is incorrect, please modify.",
    "FD_PlsAdjustDeptAndOnBoardDate": "{0} has not existed in {1}. Please adjust the unit first and then adjust the registration date.",
    "FD_PlsAdjustPositionFirstAndThenChange": "The employee is still in charge of the unit manager or secretary. Please make adjustments to the unit position before making changes.",
    "FD_PlsEnterStatusMessage": "Current status message",
    "FD_PlsFillSchoolName": "Please input school name",
    "FD_PlsSelectCancelOrder": "Please select to cancel {0}'s order",
    "FD_PlsSelectVendor": "Please select a vendor",
    "FD_PlsSetEffectiveDateGreater": "Please set the effective date to be greater than {0}",
    "FD_PlsSetQueryCondition": "Please set the query condition",
    "FD_PolicyAdvocacy": "Policy promotion",
    "FD_PolicyCategory": "Policy category",
    "FD_PolicyContents": "Policy contents",
    "FD_PoliticalStatus": "Political status",
    "FD_PoliticalStatusError": "Political status is incorrect",
    "FD_Position": "Position",
    "FD_PositionDepartmentError": "Department invalid",
    "FD_PositionsOfEmp": "This employee has these job positions",
    "FD_PossitionSort": "Job title",
    "FD_PresentAddressError": "Contact address invalid",
    "FD_Primary": "Main",
    "FD_PrintPersonnelFile": "Print personnel file",
    "FD_PrivateMailBlank": "Personal email address is blank",
    "FD_ProbationEndDate": "Probation end date",
    "FD_ProbationInfo": "Probation data",
    "FD_ProbationNotes": "Remarks for probation",
    "FD_ProbationPeriod": "Probation period",
    "FD_ProbationPeriodError": "Probation period is incorrect",
    "FD_ProbationResult": "Probation performance result",
    "FD_ProbationStartDate": "Probation begin date",
    "FD_ProbationStartDateMustAfterOnBoardDay": "Probation begin date must later the Original Hire date",
    "FD_ProbationStartDateMustBeforeEndDate": "Probation begin date must before the Probation end  date",
    "FD_ProcessingCompleted": "Processing completed",
    "FD_ProfessionalYOSBlank": "Professional Seniority is blank",
    "FD_ProfessionalYOSError": "Professional Seniority is incorrect",
    "FD_ProfilePhoto": "Profile photo",
    "FD_ProgressDesc": "Progress summary",
    "FD_ProvideInformationAccordingToConditions01": "Provide the unit list based on the search criteria",
    "FD_ProvideInformationAccordingToConditions02": "Provide the employee list based on the search criteria",
    "FD_ProvideInformationAccordingToConditions03": "Provide the list of employee in leave without pay based on the search critera",
    "FD_ProvideInformationAccordingToConditions04": "Generate the employee list of resignation and leave without pay based on the date ranges, units(subunits), status conditions.",
    "FD_ProvideInformationAccordingToConditions05": "Generate the unit transcaction list based on the date ranges, units(subunits), behavior conditions.",
    "FD_ProvideInformationAccordingToConditions06": "Generate the employee transcaction list based on the date ranges, units(subunits), behavior conditions.",
    "FD_ProvideInformationAccordingToConditions07": "Provide the expiring contracts based on the search criteria",
    "FD_ProvideInformationAccordingToConditions08": "Provide the expatriate information based on the search critera",
    "FD_ProvideInformationAccordingToConditions09": "Provide the employee list on probation based on the search criteria",
    "FD_ProvideInformationAccordingToConditions10": "Provide the list of the documents are about to expire based on the search criteria ",
    "FD_ProvideInformationAccordingToConditions11": "Provide the on board waiting list base on the search criteria",
    "FD_ProvideInformationAccordingToConditions12": "Provide the expiring certificate list base on the search criteria",
    "FD_ProvideInformationAccordingToConditions13": "Provide the rewards and discipline record list based on the search criteria",
    "FD_ProvideInformationAccordingToConditions14": "Provide complete working experience based on the search critera",
    "FD_ProvideInformationAccordingToConditions15": "Provide complete educational background based on the search critera",
    "FD_ProvideInformationAccordingToConditions16": "Provide the list of employees have not submitted on board documents based on the search critera",
    "FD_ProvideInformationAccordingToConditions17": "Generate the employee list of expected resignation based on the date ranges, units(subunits), status conditions.",
    "FD_ProvideInformationAccordingToConditions18": "Provide the list of estimated effective day in leave without pay area based on the search critera",
    "FD_ProvideInformationAccordingToConditions19": "Provide the list of employee in leave without pay area based on the search critera",
    "FD_ProvideInformationAccordingToConditions20": "Generate the employee list of resignation based on the date ranges, units(subunits), status conditions.",
    "FD_PublicationRemoved": "Inactive",
    "FD_Publisher": "Publisher",
    "FD_Quantity": "Quantity",
    "FD_Reactivate": "Reactivate",
    "FD_RecognizedJobBlank": "Approved year of service(job grade) is blank",
    "FD_RecognizedJobError": "Approved year of service(job grade) is incorrect",
    "FD_RecommandedSalary": "Recommended salary",
    "FD_RegisteredNumber": "Number of registered participants",
    "FD_RegisteredResidenceLocation": "Registered residence location",
    "FD_RegistrationClosed": "Registration is not available",
    "FD_RegistrationCompleted": "You have completed the registration at {0} (Total {1} people)",
    "FD_RegistrationData": "Registration information",
    "FD_RegistrationDeadline": "Registration deadline",
    "FD_RegistrationDetail": "Registration sheet",
    "FD_RegistrationPeopleLimit": "We have reached the maximum of registration, please do not increase any registrations.",
    "FD_RegistrationTime": "Registration period",
    "FD_Reinstated": "Reinstated",
    "FD_ReinstateEffectiveDate": "Effective date of reinstatement",
    "FD_RelationshipisError": "Relationship is incorrect",
    "FD_Relative": "Relative",
    "FD_ReleaseDate": "Release Date",
    "FD_ReleaseSubmitSticker": "Publish and send out notes",
    "FD_ReleaseTime": "Release Time",
    "FD_RemainingPlaces": "Places remaining",
    "FD_Remind01": "Gentle reminder, this employee has signed below contracts, sure to proceed?",
    "FD_Remind02": "Gentle reminder, this employee has signed the business strife limitation agreement",
    "FD_Remind03": "Gentle reminder, this employee has signed the training agreement, and the contract will end on {0}",
    "FD_Remind04": "Gentle reminder, this employee is still under the employment contract period, the contract will end on {0}",
    "FD_RemindYou": "Remind",
    "FD_ReplaceDepartmentName": "Change unit name",
    "FD_ReportCenterEncryptionSettings": "Report Center encryption settings",
    "FD_ReportedResultFailed": "on board process failed, system cannot create account.",
    "FD_ReportProgress": "Progress update",
    "FD_ReportRecords": "Update records",
    "FD_RequestEditEmployeeData": "Request to edit employee data",
    "FD_RequiredDocuments": "Required documents",
    "FD_ResignationAndLeaveWithoutPayReport": "Resignation and leave without pay report",
    "FD_ResignationAreaReport": "Resignation report",
    "FD_ResignationDateCannotBeModifiedAfterResign": "In order to change the resignation date after resignation, you need to delete the change record and apply the resignation process again.",
    "FD_ResignationRecordNotIncluded.": "If emplyee take the leave without pay first, and then apply the resignation within the period, the resignation record is not included.",
    "FD_ResignReasonError": "Resignation reason invalid",
    "FD_ResponsibleUnit": "Responsible unit",
    "FD_RetainedSuccess": "Counter offer success",
    "FD_ReturnToWork": "Leave without Pay & reinstatement",
    "FD_ReturnToWorkManageList": "Employee list of leave without pay",
    "FD_ReturnWorkData": "reinstatement informtaion",
    "FD_ReturnWorkDate": "reinstatement date",
    "FD_ReviewCompletedUpdate": "Completed review and has been updated",
    "FD_RevisionDate": "Revised date",
    "FD_SalaryNotice": "Payroll Notification",
    "FD_SchoolError": "School invalid",
    "FD_SchoolLocationBlank": "School location is blank",
    "FD_SchoolLocationError": "School location is incorrect",
    "FD_SchoolNameBlank": "School name is blank",
    "FD_SearchDate": "Search Date",
    "FD_Secondary": "Secondary",
    "FD_SecondaryCostCenter": "Secondary cost center",
    "FD_SecondaryCostCenterBlank": "Secondary cost center is blank",
    "FD_SecondaryCostCenterError": "Secondary cost center invalid",
    "FD_SecondaryCostCenterPercentLimit": "The secondary cost center ratio must be less than or equal to 100%",
    "FD_SecondaryCredentials": "Secondary Identification",
    "FD_Secretary": "Secretary",
    "FD_SecretaryDetail": "Secretary data",
    "FD_SEDIsTheDayAfterFWD": "Effective date will be the day after the last working day",
    "FD_SelectPeopleNumber": "Number of people you can select",
    "FD_SelectVendor": "Select vendor",
    "FD_SendIn": "Send In",
    "FD_SendOut": "Send Out",
    "FD_ServiceYear": "Year of service",
    "FD_SetMasterCategory": "Set as main category",
    "FD_SettingNewEmployeeNumber": "new employee number",
    "FD_ShowRepeatItems": "{0} duplicate items",
    "FD_SigningSupervisorInactive": "Signing supervisor is inactive",
    "FD_SiteBlank": "Factory type is blank",
    "FD_SizeLimit300KB": "Size Limit: 300KB",
    "FD_SizeSuggestion1140": "Size Suggestion: 1140*180",
    "FD_SocialSeniority": "Normal duration of citizen working years",
    "FD_SocialSeniorityAdjust": "Adjusted value for normal duration of citizen working years",
    "FD_SocialSeniorityAdjustError": "Adjusted value for normal duration of citizen working years is incorrect",
    "FD_Sort": "Sort",
    "FD_SparePhoneNumber": "Alternate phone number",
    "FD_SparePhoneNumberBlank": "Alternate phone number is blank",
    "FD_SparePhoneNumberError": "Alternate phone number invalid",
    "FD_SpecifiedUnitLeaveWrong": "The specified unit level is incorrect",
    "FD_StaffEditRegistrationDate": "{0} registration date has been modified, please know, thank you",
    "FD_StaffFieldNotFoundOppositeDepartment": "Person {0} field {1} could not find corresponding department {2}",
    "FD_StaffGaveUpRegistration": "{0} has quit the report, please know, thank you",
    "FD_StaffHasMultipleHighestEducation": "Personnel {0} has multiple materials for the highest degree",
    "FD_StaffHasNoHighestEducation": "Personnel {0} has no information for the highest degree",
    "FD_StaffIsNotExist": "Personnel {0} does not exist",
    "FD_StaffWorkPeriodsError": "Person {0} error during working period",
    "FD_StationedAssignmentOrNot": "expatriate or not",
    "FD_StayArea": "Leave without pay Area",
    "FD_StayAreaReport": "Leave without pay report center list",
    "FD_StayExtend": "Extend leave without pay  period",
    "FD_Staying": "status in leave without pay",
    "FD_StayingNotify": "leave without pay notification",
    "FD_StayStartDate": "Startdate of leave without pay",
    "FD_Sticker": "Notes",
    "FD_StorageLocation": "Storage location",
    "FD_SubordinateUnit": "Subordinate units",
    "FD_Subscriber": "Buyer",
    "FD_Subtype": "Subtype",
    "FD_SubtypeBlank": "Subtype is blank",
    "FD_SubtypeError": "Subtype is incorrect",
    "FD_SuccessfulIDVerification": "Identity card verified successfully",
    "FD_SuccessfullyPaid": "Successfully paid",
    "FD_SuperiorUnitInPersonnelListBasedOnDateRange": "The upper unit of employee based on the date period",
    "FD_SupervisorPhoto": "photo of manager",
    "FD_TaxIDNumFormatWrong": "tax ID Number format error",
    "FD_TaxIDNumNotMoreThanCharators": "tax ID Number cannot exceed {1} characters",
    "FD_TEDIsTheDayAfterFWD": "Resignation effective date would be the date after the last working date",
    "FD_TerminationCertificate": "Termination Certificate",
    "FD_TerminationDate": "End date",
    "FD_TerminationEform": "eForm for resignation",
    "FD_TerminationList": "Resignation List",
    "FD_TerminationOrStayArea": "Resignation/Leave Without pay Area",
    "FD_ThisDateUnitNotExist": "The affiliated unit is not exsit, please modify affiliated unit before changing the hire date.",
    "FD_ThisEmployeeHaveSubUnitAndEmployees01": "This employee still have subordinate units and employees, you cannot select resignation as an option.",
    "FD_ThisEmployeeHaveSubUnitAndEmployees02": "This employee still have subordinate employees, you cannot select leave without pay as an option.",
    "FD_ThisEmployeeIsActive01": "This employee is still an active employee, you cannot select return to work as an option.",
    "FD_ThisEmployeeIsActive02": "This person is still an active employee",
    "FD_ThisEmployeeIsExistInResignArea": "Employee already exsit",
    "FD_ThisEmployeeIsLeaveOfAbsence01": "This employee status is in leave without pay, you cannot adjust employment type.",
    "FD_ThisEmployeeIsLeaveOfAbsence02": "This employee is on leave without pay, you cannot select leave of absence as an option.",
    "FD_ThisEmployeeStillSecretary01": "This employee is still in the role of the secretary, you cannot select resignation as an option.",
    "FD_ThisEmployeeStillSecretary02": "This employee is still in the role of secretary, you cannot select leave without pay as an option.",
    "FD_ThisPersonInBlacklist": "This person is in blacklist",
    "FD_ThisPersonIsRehired": "This person is a rehired employee",
    "FD_Thumbnail": "Thumbnail",
    "FD_Todo": "Assigned work",
    "FD_TodoContent": "Assigned work contents",
    "FD_TopFloorUnitHasNoUpperUnit": "When specified as the uppermost unit level, there is no upper unit",
    "FD_TotalMoney01": "Total {0} people ordered, total amount is NT${1}",
    "FD_TotalPeopleAmount": "Total number of participants",
    "FD_TotalScoreOfReviews": "Overall review score",
    "FD_TransferedStay": "status changed to leave without pay",
    "FD_TransferedToLeave": "change to resignation",
    "FD_TransferPersonalData": "Transfer personal data",
    "FD_TransferPersonnelDataFailed": "employee information implement failed, system cannot create account.",
    "FD_TransferPersonnelFile": "Transfer personnel file",
    "FD_TransferPersonnelFileNotes": "Remarks for transferring personnel file",
    "FD_TransferToLeave": "change to resignation",
    "FD_TransferToStay": "status change to leave without pay",
    "FD_UnitChangeRecord": "Unit Change Record",
    "FD_UnitChangeReport": "Unit change report",
    "FD_UnitCodeLimit": "Unit code limit letter、number and hyphen.",
    "FD_UnitData": "Unit data",
    "FD_UnitDataNotes": "Remarks for unit data",
    "FD_UnitDefunct": "The unit is expired",
    "FD_UnitDefunctDate": "Unit expiration date",
    "FD_UnitEmployee": "Unit employee",
    "FD_UnitEmployeeNumber": "Unit employee number",
    "FD_UnitEmployees": "Unit employees",
    "FD_UnitInfo": "Unit information",
    "FD_UnitInfoNotes": "Remarks for unit information",
    "FD_UnitLevelCannotHigherUpperDottedLineUnit": "the level of the unit does not allow higher than the upper unit of the dotted line, please modify the data before delete",
    "FD_UnitLevelCode": "Unit level code",
    "FD_UnitLevelError": "Unit level corresponding error",
    "FD_UnitLevelHigherThanLowerUnit01": "The unit level cannot be equal to the unit under its jurisdiction, or lower than the unit under its jurisdiction. Please modify the data and delete it.",
    "FD_UnitLevelHigherThanLowerUnit02": "The unit level is equal to the lower unit, or lower than the lower unit. Please modify the data.",
    "FD_UnitLevelList": "Unit level list",
    "FD_UnitLevelLowerThanUpperUnit01": "The unit level cannot be equal to the upper unit or higher than the upper unit. Please modify the data and delete it.",
    "FD_UnitLevelLowerThanUpperUnit02": "The unit level is equal to the upper unit, or higher than the upper unit. Please modify the data.",
    "FD_UnitList": "Unit list",
    "FD_UnitManagerJobTitle": "Unit manager job title",
    "FD_UnitManagerSeriesNumber": "Series number",
    "FD_UnitManagerSeriesNumber02": "Unit manager position number",
    "FD_UnitNameMultilanguage": "Unit name in multilanguage",
    "FD_UnitNotExist": "Unit does not exist",
    "FD_UnitPrice": "Unit price",
    "FD_UnitSecretaryId": "Unit secretary ID",
    "FD_UnitSecretaryName": "Unit secretary name",
    "FD_UnitsOrPersonnelInUse": "The unit or employee is enable, for futher informaion please check the unit list or active employee list",
    "FD_UnitStatusIsClosed": "The unit status was expired. Please reactive the unit status.",
    "FD_UnitSupervisorDataError": "The unit manager is invalid, please check and make a correction.",
    "FD_UnitSupervisorMoreThanSix": "The maximum number of the unit manager is exceed.",
    "FD_Unpaid": "Unpaid",
    "FD_UnpaidAmount": "Unpaid amount",
    "FD_UnpaidQuantity": "Unpaid quantity",
    "FD_UpdateAbnormal": "Update abnormal",
    "FD_UPIDNotOppositeUpperDepartment": "Cannot find the corresponding Upper Department from the specified Upper Department Id.",
    "FD_UploadCertificateFile": "Upload supporting documents",
    "FD_UploadPicture": "Upload Picture",
    "FD_UpperPositionSort": "Upper position number",
    "FD_UpperUnitCannotBeSelf": "The upper unit of the unit cannot be itself",
    "FD_UpperUnitError": "Unit level corresponding error",
    "FD_UpperUnitHasExist": "The upper unit must exist first",
    "FD_UseDefaultEmployeeNumber": "use preset employee number",
    "FD_VendorManagement": "Vendors management",
    "FD_VendorName": "Vendor name",
    "FD_VerificationFailed": "Verification failed, please enter again.",
    "FD_WaitOnBoardList": "On board list",
    "FD_WaivedReasonError": "Reason for exemption from military service invalid",
    "FD_WeekContractExpiredNotify01": "Notification for the expiring contracts this week",
    "FD_WeekContractExpiredNotify02": "Please be aware this is the second reminder. The following link is the list of expiring contract by this week, click here to view detailed information. Thank you",
    "FD_WeeklyNotify": "Weekly notification for {0}",
    "FD_WeelEmployeeAdjustNotify01": "The following link is the list of {0} by this week, click here to view detailed information. Thank you",
    "FD_WeelEmployeeAdjustNotify02": "Please be aware this is the second reminder. The following link is the list of {0} by this week, click here to view detailed information. Thank you",
    "FD_WhetherEmergencyContact": "is emergency contact",
    "FD_WhetherEmergencyContactError": "is emergency contact invalid",
    "FD_WhetherSureToDeleteAllChangeAction": "all the transaction records will be cancelled, confirm to delete ?",
    "FD_WhetherSureToModifyNotOnBoard": "change {0} to not on board yet, confirm to proceed?",
    "FD_WhichWayToCreateEmployeeData": "Please select to create employee information",
    "FD_WillBeExpired": "Your {0} will be expired on {1}, please renew it as soon as possible. Thank you.",
    "FD_Witness": "Reference",
    "FD_WitnessContact": "Reference contact",
    "FD_WitnessContactError": "Reference contact invalid",
    "FD_WitnessError": "Reference invalid",
    "FD_WordCountExceedsLimit": "{0}Word count exceeds limit",
    "FD_WorkContentError": "Job description invalid",
    "FD_WorkExperienceMultiLineDisplay": "for more than one working experience, use multiple lines to display",
    "FD_WorkExperienceNotes": "Remarks for work experience",
    "FD_WorkLocation": "Work location",
    "FD_WorkLocationBlank": "Work location is blank",
    "FD_WorkLocationError": "Work location is incorrect",
    "FD_WorkLocationGroup": "Work location group",
    "FD_WorkYOSBlank": "Work Seniority is blank",
    "FD_WorkYOSError": "Work Seniority is incorrect",
    "FD_YosInfoNotes": "Remarks for Seniority data",
    "FD_YOSNotes": "Remarks for Seniority",
    "FD_YOSProbationInfo": "Seniority/Probation data",
    "PT_AttendanceSummary": "Conclude Attendance Work",
    "PT_EmployeeInformation": "Employee Information",
    "PT_GenerateLeaveHours": "Create leave hours",
    "PT_ManagingFormsRecords": "Manage Form Records",
    "PT_OvertimeApplication": "Apply overtime",
    "PT_SetUpTimetable": "Arranging work timetable",
    "PT_StaffSetting": "Staff setting",
    "PY_EmpSalaryAndInsuranceData": "Employee salary and insurance",
    "PY_MissedEmployeeCheck": "missed employee check",
    "PY_SalaryCalculate": "payroll calculation",
    "SH_10Days": "10th",
    "SH_11Days": "11th",
    "SH_12Days": "12th",
    "SH_13Days": "13th",
    "SH_14Days": "14th",
    "SH_15Days": "15th",
    "SH_16Days": "16th",
    "SH_17Days": "17th",
    "SH_18Days": "18th",
    "SH_19Days": "19th",
    "SH_1Day": "1st",
    "SH_20Days": "20th",
    "SH_21Days": "21st",
    "SH_22Days": "22nd",
    "SH_23Days": "23rd",
    "SH_24Days": "24th",
    "SH_25Days": "25th",
    "SH_26Days": "26th",
    "SH_27Days": "27th",
    "SH_28Days": "28th",
    "SH_29Days": "29th",
    "SH_2Days": "2nd",
    "SH_30Days": "30th",
    "SH_30Days02": "30 days",
    "SH_31Days": "31st",
    "SH_3Days": "3rd",
    "SH_4Days": "4th",
    "SH_5Days": "5th",
    "SH_6Days": "6th",
    "SH_7Days": "7th",
    "SH_8Days": "8th",
    "SH_9Days": "9th",
    "SH_AbnormalDescription": "Exception notice",
    "SH_AbnormalReason": "Warning reason",
    "SH_AbnormalRecord": "Warning report",
    "SH_Abolished": "Abolished",
    "SH_AboveCollege": "專科以上",
    "SH_AboveFourYearTechnicalSchool": "四技以上",
    "SH_AboveHours": "Above {0} hours",
    "SH_AboveMastersDegree": "碩士以上",
    "SH_AboveTwoYearTechnicalSchool": "二技以上",
    "SH_AboveUniversity": "大學以上",
    "SH_AboveVocationalHighSchool": "高中職以上",
    "SH_Absenteeism": "absenteeism",
    "SH_AbsenteeismNMinutes": "absenteeism for {0} minutes",
    "SH_Accept": "Accept",
    "SH_Accepted": "Pass",
    "SH_AccordingDistanceWay": "By brackets",
    "SH_AccordingFixedRate": "By fixed rate {0} %",
    "SH_AccordingPartTimeSalaryRatio": "Pro-rata by hourly wage",
    "SH_AccordingToDepartment": "By Unit",
    "SH_AccordingToEmployee": "By employee ",
    "SH_Account": "Account",
    "SH_AccountCodeIsEmpty": "subject code is blank",
    "SH_AccountCodeNotExist": "subject code not exsit",
    "SH_AccountEnabledMsg": "Congratulations! You have activated your account and ID verification.",
    "SH_AccountNotActivatedPlsContactHR": "This account has not been activated, please connect with HR administrator",
    "SH_Acting": "Acting",
    "SH_Action": "action",
    "SH_Active": "Active",
    "SH_ActualWorkDays": "Actual days at work",
    "SH_Add": "Add",
    "SH_Add02": "Create at {0}",
    "SH_Add03": "Add",
    "SH_Add04": "Additional approval",
    "SH_AddCategory": "Create category",
    "SH_AddChangeEform": "New change form",
    "SH_AddForm": "Add",
    "SH_AddItem": "Add item",
    "SH_AdditionalInsurance": "Enrollment",
    "SH_Address": "Address",
    "SH_AddressBlank": "address is blank",
    "SH_AddressError": "address invalid",
    "SH_AddSalaryAccount": "Add payroll subject",
    "SH_AdjustBurdenSetting": "adjust burden setting",
    "SH_AdjustCertificateNumberType": "adjust certificate number",
    "SH_AdjustEmployerContributionRate": "adjust employer withholding rate",
    "SH_AdjustInsurance": "adjust insurance type",
    "SH_AdjustInsuranceAmount": "adjust Insured amount",
    "SH_AdjustInsuranceAreaOrType": "adjust Insured area or classification",
    "SH_AdjustInsuranceBurdenSetting": "adjust burden setting",
    "SH_AdjustInsuranceIdentity": "adjust Insured identity",
    "SH_AdjustLaborPensionType": "Adjust labor pension type",
    "SH_AdjustNationality": "adjust indentity",
    "SH_AdjustPaymentCardinalNumbers": "Adjust payment base number ",
    "SH_AdjustPersonalContributionRate": "adjust employee withholding rate",
    "SH_AdjustReductionSetting": "Adjust reduction setting",
    "SH_AdjustRelief": "adjust exemption",
    "SH_AdjustReliefIdentity": "adjust exemption identity",
    "SH_AdjustSupplementaryPremiumSetting": "adjust Supplementary premium",
    "SH_AdjustWithholdingType": "adjust withholding mode",
    "SH_AdministrationCostCenter": "Administration Cost Center",
    "SH_AdministrationSpace": "Administration Space",
    "SH_AdministrativeOrganization": "Administration organization",
    "SH_AdminRecruiter": "總招募人員",
    "SH_AED": "AED",
    "SH_AffiliatedUnitCode": "Affiliated unit code",
    "SH_AffiliatedUnitName": "Affiliated unit name",
    "SH_Age": "Age",
    "SH_Agree": "Agree",
    "SH_AlertMessage": "Prompt message",
    "SH_All": "All",
    "SH_AllEmployees": "All employees",
    "SH_AllEmployees02": "All employees",
    "SH_AllowanceItem": "Payroll: allowance item",
    "SH_Amount": "Amount",
    "SH_AmountBlank": "amount is blank",
    "SH_AmountError": "amount invalid",
    "SH_AmountLimited": "amount limited",
    "SH_AndAbove": "and above",
    "SH_AnnouncementMethod": "Announcement method",
    "SH_AnnouncementTime": "Announcement period",
    "SH_Announcer": "Announced person",
    "SH_AnnounceTime": "Announced time",
    "SH_AnnualBonusCN": "Annual bonus(CN)",
    "SH_TocolysisLeave": "Tocolysis leave",
    "SH_AnnualLeave": "Annual leave",
    "SH_AnnualLeaveEncashment": "Annual leave encashment",
    "SH_AnnualSalary": "Annual salary",
    "SH_AnnualSalarySystem": "Annual pay",
    "SH_AnnualTerminationDate": "Annual end date",
    "SH_Applicant": "Applicant",
    "SH_Applicant02": "Applicant :",
    "SH_ApplicantPerson": "Applicant",
    "SH_ApplicantRequired": "The applicant field is required",
    "SH_ApplicationRecord": "Application record",
    "SH_ApplyDateTime": "eForm application date/time",
    "SH_ApplyForExtension": "Extension",
    "SH_ApplyForExtensionTo": "Apply for extension to ",
    "SH_AppointmentApprovedForm": "Employment and salary Form",
    "SH_ApportionmentRatio": "Cost center allocation rate",
    "SH_Approval": "Approval",
    "SH_Approval03": "Approval",
    "SH_ApprovalByDelegated": "On behalf of approver",
    "SH_ApprovalCenter": "Approval Center",
    "SH_ApprovalCount": "Approval results：{0}",
    "SH_ApprovalRecords": "Approval records",
    "SH_ApprovalReminderRecord": "Approval reminder record",
    "SH_ApprovalResult": "Approval results",
    "SH_ApprovalSequence": "Approval sequence",
    "SH_Approve": "Approved",
    "SH_Approve02": "Approve",
    "SH_Approved": "Approved",
    "SH_ApprovedRecord": "Approval record",
    "SH_ApprovedWaittingConfirm": "Approved and waiting for confimation",
    "SH_Approver": "Approver",
    "SH_ApproverNname": "Approver name",
    "SH_Approving": "Pending for approval",
    "SH_Apr": "APR",
    "SH_Area": "Area",
    "SH_AreYouSureToResetPassword": "Are you sure to reset your password?",
    "SH_ARS": "ARS",
    "SH_AsiaPolicy01": "Apollo privacy policy",
    "SH_AsiaPolicy02": "MAYO HUMAN CAPITAL INTERNATIONAL PTE. LTD. (以下亦稱我們)",
    "SH_AsiaPolicy03": "為了進一步完善客戶(您的雇主)在使用Apollo的體驗、向您提供更好的人力資源管理服務，我們會在為您提供服務的過程中蒐集必要數據，並透過數據分析與加值，為您提供更升級、貼心的服務。",
    "SH_AsiaPolicy04": "一、經由您的雇主授權同意，授權Apollo對您個人資料進行蒐集、儲存、處理及利用",
    "SH_AsiaPolicy05": "導入Apollo時所蒐集的個人及企業資料，包括但不限於姓名、員工編號、身份證字號、性別、生日、學歷、聯絡方式、電子郵件、薪資、稅務及保險資料等。",
    "SH_AsiaPolicy06": "您在使用Apollo服務過程中，服務器可能會紀錄包括但不限終端設備及瀏覽器資料、設備所在位置資料、IP地址、網域名、操作日誌資料、或您主動輸入及上傳的各類資料。",
    "SH_AsiaPolicy07": "我們使用cookie技術，紀錄用戶使用Apollo的習慣與偏好，用於相關服務的初始化設定。若您取消瀏覽器內的cookies功能，將導致無法順利使用完整功能。",
    "SH_AsiaPolicy08": "當您與我們聯繫時，我們會紀錄您的通信/通話紀錄和內容、聯繫方式、相關問題的處理方案及結果等資料，以便與您聯繫或幫助您解決問題。",
    "SH_AsiaPolicy09": "我們可對所獲得的用戶資料進行數據分析與研究，以提供更優質服務。",
    "SH_AsiaPolicy10": "Apollo將依據當地個人資料保護法，不會進行特定個人資料的蒐集、儲存、處理及利用，其類別包括但不限於病歷、醫療、基因、性生活、健康檢查、犯罪前科、政治、宗教、信仰、生物特徵及未成年人資料等。除非您的雇主書面授權同意，在不違反法律的情況下始得進行。",
    "SH_AsiaPolicy11": "Apollo不會在未經您的雇主授權之前披露或與第三方共享您的用戶資料，除非有下列情況：",
    "SH_AsiaPolicy12": "與國家安全、國防安全有關的；",
    "SH_AsiaPolicy13": "與公共安全、公共衛生、重大公共利益有關的；",
    "SH_AsiaPolicy14": "與犯罪偵查、起訴、審判和判決執行等有關的；",
    "SH_AsiaPolicy15": "出於維護個人資料主體或其他個人的生命、財產等重大合法權益但又很難得到您的雇主同意；",
    "SH_AsiaPolicy16": "所蒐集的個人資料是您自行向社會大眾公開的；",
    "SH_AsiaPolicy17": "從合法公開資料中蒐集的個人資料，如合法的新聞報導、政府公開資料等管道；",
    "SH_AsiaPolicy18": "獲得您的雇主的明確同意；",
    "SH_AsiaPolicy19": "Apollo部分服務將與第三方夥伴合作，以提供更好的客戶服務和用戶體驗。我們可能會與合作夥伴共同處理您的個人資料，但僅限於特定、明確的目的及必要內容。我們的合作夥伴可能通過您的個人資料與您取得聯繫並向您提供服務，但無權將您的個人資料用於任何其他用途；如您拒絕提供上述資料或拒絕授權，可能無法使用上述相關產品或服務；",
    "SH_AsiaPolicy20": "在我們發生合併、收購或破產清算情形，或其他涉及合併、收購或破產清算情形時，如涉及到個人資料轉讓，我們會要求新的持有您個人資料的公司或組織繼續受本政策的約束，否則我們將要求該公司或組織重新向您的雇主徵求授權同意。",
    "SH_AsiaPolicy21": "對我們與之共享個人資料的公司或組織，我們會與其簽署嚴格的數據保護協定，要求他們按照本隱私政策以及其他任何相關的保密和安全措施來處理個人資料。",
    "SH_AsiaPolicy22": "當您的雇主取消Apollo服務時，可選擇刪除或轉移所有的個人資料，Apollo將刪除或轉移您的個人資料。唯當您從我們的服務中刪除資料後，我們可能不會立即從備份系統中刪除相應的資料，但會在備份更新時刪除這些資料。",
    "SH_AsiaPolicy23": "如我們停止運營Apollo服務，我們將即時停止蒐集您個人資料，將停止運營的通知以公告形式通知您的雇主，並對所持有的個人資料進行刪除或去識別化處理。",
    "SH_AsiaPolicy24": "二、Apollo非常重視資料安全",
    "SH_AsiaPolicy25": "我們已採取符合業界標準、合理可行的安全防護措施保護您提供的個人資料安全，防止個人資料遭到未經授權訪問、公開披露、使用、修改、損壞或丟失。例如，在您的瀏覽器與服務器之間交換數據時受SSL（Secure Socket Layer）協議加密保護；在終端設備的App進行加密封裝保護；我們同時對Apollo提供HTTPS（Hyper Text Transfer Protocol over Secure Socket Layer）協議安全瀏覽方式；我們會使用加密及遮罩技術提高個人資料的安全性；我們會佈署訪問控制機制，盡力確保只有授權人員才可訪問個人資料；以及我們會舉辦安全和隱私保護培訓課程，加強員工對於保護個人資料重要性的認識。",
    "SH_AsiaPolicy26": "我們非常重視資料安全，通過相應的安全保護措施保護您的個人資料不被洩漏，但同時也請您注意在資料網絡上不存在絕對完善的安全措施。在不幸發生個人資料安全事件後，我們將按照法律法規的要求，在72小時內以郵件、信函、電話、推送通知、官網公告等方式告知您的雇主。",
    "SH_AsiaPolicy27": "III、transnational transmission",
    "SH_AsiaPolicy28": "在您的雇主授權同意的情況下，我們將符合當地的個人資料保護法，將所蒐集境外的個人資料(如歐洲經濟區)，傳輸到我們的雲端平台服務商及合作夥伴。",
    "SH_AsiaPolicy29": "四、您的權利",
    "SH_AsiaPolicy30": "如您為歐盟公民或個人資料屬歐盟境內，依「一般資料保護規則」(GDPR, General Data Protection Regulation) 之規定，您有權向雇主要求存取、修正、遺忘、清除、限制處理、反對處理您的個人資料，或要求移轉您的個人資料(可攜性)。如需深入瞭解這些權利，請造訪歐盟執行委員會的 GDPR 相關頁面。?",
    "SH_AsiaPolicy31": "如您的個人資料非屬歐盟境內，則依據當地法律行使權利。",
    "SH_AsiaPolicy32": "如您對自己的隱私權有疑問，請聯繫您雇主的人力資源部門。",
    "SH_AsiaPolicy33": "五、Apollo隱私權政策如何更新",
    "SH_AsiaPolicy34": "為了因應Apollo服務的變更，或依照客戶的意見，又或者為了任何其他目的，我們有權隨時修改本隱私權政策，我們會在專門頁面({0})上發布對隱私權政策所做的任何變更。對於重大變更，我們會提供更為顯著的通知（透過電子郵件或任何其他方式）。",
    "SH_AsiaPolicy35": "若您對Apollo的隱私權政策或資料處理方式有任何問題或疑慮，請與{0}。",
    "SH_AsiaPolicy36": "我們聯絡",
    "SH_AsiaVersionNoSupportsMultiEnterpriseSelection": "The Asia version on longer supports multi-enterprise. Please contact the HR administrator to unbind other enterprise accounts.",
    "SH_AssignmentGroup": "job role category",
    "SH_AssignmentHistory": "job role experience",
    "SH_AssignmentType": "Type of job role",
    "SH_AssignmentYOS": "Year of service (Job role)",
    "SH_AssociateDegree": "Associate degree",
    "SH_Attachment01": "Attachment : ",
    "SH_Attachment02": "Attachment",
    "SH_AttachmentOver01": "Attachment cannot exceed 3 files",
    "SH_Attendance": "Attendance",
    "SH_AttendanceAbnormal": "Attendance warning",
    "SH_AttendanceAdministrator": "Attendance administrator",
    "SH_AttendanceConclusion": "Concluded attendance",
    "SH_AttendanceData": "Attendance data",
    "SH_AttendanceDetail": "Attendance detail",
    "SH_AttendancePolicies": "Attendance Policies",
    "SH_AttendancePolicyBlank": "Attendance policy is blank",
    "SH_AttendancePolicyError": "Attendance policy is incorrect",
    "SH_AttendanceRange": "Attendance range",
    "SH_AttendanceRangeBlank": "The attendance range is blank",
    "SH_AttendanceRangeError": "The attendance range is incorrect",
    "SH_AttendanceStatus": "attendance status",
    "SH_AUD": "AUD",
    "SH_Aug": "AUG",
    "SH_AuthorizationSetting": "Authorization settings",
    "SH_AuthorizationSettings": "Authorization settings",
    "SH_AuthorizationSettingsDiscription": "Authorization settings description",
    "SH_AutomaticallyGeneratedEmail": "This is an automatically generated email, please do not reply.",
    "SH_AverageMonthlyScore": "Average monthly salary",
    "SH_AverageScore": "Average score",
    "SH_Bachelor": "Bachelor",
    "SH_Back": "After",
    "SH_BankAccount": "Bank account",
    "SH_BasedOnPeoplePlus": "based on setting of People+",
    "SH_BaseField": "Basic fields",
    "SH_BasicPay": "Base salary",
    "SH_BasicSettings": "Basic setting",
    "SH_BatchAlter": "Batch alter",
    "SH_BatchApply": "Batch application",
    "SH_BatchApprove": "Batch approval",
    "SH_BatchChangeErrorDescription": "Batch change exception description",
    "SH_BatchGenerate": "Create all",
    "SH_BatchImportIncomeTaxCN": "Batch import Income Tax(CN)",
    "SH_BatchImportTemplate": "Batch Import Template",
    "SH_BatchManagement": "Batch control",
    "SH_BatchOperation": "Batch Operation",
    "SH_BehalfOf": "Approve on behalf of {0}",
    "SH_BelongYear": "Vesting year/month",
    "SH_BGN": "BGN",
    "SH_BillingStaff": "Billing staff",
    "SH_BillingStatus": "Status",
    "SH_BillingWay": "settlement method",
    "SH_Biography": "Biography",
    "SH_BirthDateBlank": "Date of birth is blank",
    "SH_BirthDateError": "Date of birth is incorrect",
    "SH_BND": "BND",
    "SH_BOB": "BOB",
    "SH_Bonus": "Bonus",
    "SH_BonusRole": "Bonus group",
    "SH_BookedOnBoardingDate": "Anticipated on board date",
    "SH_BranchOfficesNotes": "Remarks for headquarters and branch offices",
    "SH_BreastfeedingLeave": "Nursing Leave",
    "SH_BRL": "BRL",
    "SH_BrowsableLevel": "Access level",
    "SH_BrowseChangeHistory": "Browse the change history",
    "SH_CAD": "CAD",
    "SH_Calculate": "calculate",
    "SH_NotCalculate": "Not calculate",
    "SH_CalculateByProportion": "calculate by proportion",
    "SH_CalculateByTotalAmount": "calculate by total amount",
    "SH_CalculateResult": "calculate result",
    "SH_CalculateSupplementaryPremiumPartTime": "calculate part-time income Supplementary premium",
    "SH_CalculationCompleted": "Calculation complete",
    "SH_CalculationException": "Calculation exception",
    "SH_CalculationFormula01": "每月基準科目總額/{月工時}",
    "SH_CalculationFormula02": "每月基準科目總額/總工作日*{日工時}",
    "SH_CalculationFormula03": "每月基準科目總額/21.75*{日工時}",
    "SH_CalculationFormula04": "每月基準科目總額/日曆日*{日工時}",
    "SH_CalculationType": "Calculation type",
    "SH_CalculationWay": "Calculating type",
    "SH_Calendar": "Calendar",
    "SH_CalendarDay": "Calendar day",
    "SH_Cancel": "Cancel",
    "SH_Cancel02": "Canceled",
    "SH_CancelFailed": "Cancellation failed",
    "SH_CancelForm": "Withdraw the application eForm",
    "SH_CancelSelectAll": "Cancel select all",
    "SH_CancelSuccess": "Successfully canceled",
    "SH_CannotDeleteWhenUsed": "item has been active and cannot delete",
    "SH_CannotDisableWhenUsed": "Not allowed to disable",
    "SH_CannotFindDomainPlsContactHR": "Cannot find the domain, please enter again or contact HR administrator",
    "SH_CannotGetCompanySettings": "Cannot load your settings",
    "SH_CannotImportExpatriateEmployee": "Not allow to import expatriate employee",
    "SH_CannotModifyEffectiveForm": "不可以修改已生效異動單",
    "SH_CannotReprocessing": "This information has been processed and cannot be process again",
    "SH_CardNumber": "Check in/out number",
    "SH_CardNumberError": "Card number invalid",
    "SH_CareForTheElderly": "Care for the elderly",
    "SH_CareForTheElderlyBlank": "贍養老人(元)未填寫",
    "SH_CareForTheElderlyError": "贍養老人(元)有誤",
    "SH_CarouselSeconds": "輪播秒數：",
    "SH_Category": "Category",
    "SH_Category02": "Category",
    "SH_CertificateNumberType": "certificate number",
    "SH_Certifications": "Certifications",
    "SH_ChangeAction": "Change action",
    "SH_ChangeActionBlank": "Change action is blank",
    "SH_ChangeActionError": "Change action is incorrect",
    "SH_ChangeApprover": "Change approver",
    "SH_ChangeBach": "Change Batch",
    "SH_ChangeBehavior": "Change action",
    "SH_ChangeCompany": "Switch companies",
    "SH_ChangeCostCenter": "Change cost center",
    "SH_ChangeCreatedSuccess": "Change e-form created",
    "SH_ChangeData": "change information",
    "SH_ChangeDate": "Change date",
    "SH_ChangeDetails": "Change details",
    "SH_ChangeEffectiveDate": "Change effective date",
    "SH_ChangeHireStatusFailed": "Foundation-Hire has an error occurred while changing the status, please contact Apollo service contact",
    "SH_ChangeHistory": "Change history",
    "SH_ChangeHistory02": "Change history",
    "SH_ChangeInsuranceIdentity": "modify insured identity",
    "SH_ChangeInsuranceUnit": "modify insured unit",
    "SH_ChangeName": "change name",
    "SH_ChangePassword": "Change password",
    "SH_ChangePasswordError": "Due to 10 failed password attempts, you will not be able to log in again. Please reset your password or contact HR administrator.",
    "SH_ChangePasswordFailed": "Failed to change password",
    "SH_ChangePasswordMsg02": "Your password has been changed, the system will log out automatically, please use your new password to log in again.",
    "SH_ChangePayrollRange": "Change payroll range",
    "SH_ChangeReason": "Change reason",
    "SH_ChangeReasonBlank": "Change reason is blank",
    "SH_ChangeReasonError": "Change reason is incorrect",
    "SH_ChangeSettings": "Change setting",
    "SH_Charge": "charge",
    "SH_ChargeMode": "Billing method",
    "SH_Check": "check",
    "SH_CheckAgain": "Recheck",
    "SH_CheckIn": "Check in/out",
    "SH_CheckIn02": "Check in/out",
    "SH_CheckInAbnormal": "Clock in/out warning",
    "SH_CheckInFailed": "Unsuccessfully checked in/out!!",
    "SH_CheckInReminder": "Check in/out reminder",
    "SH_CheckInReminderBlank": "Check in/out reminder is blank",
    "SH_CheckInReminderError": "Check in/out reminder is incorrect",
    "SH_CheckInSuccess": "Successfully checked in/out!!",
    "SH_CheckSQLSyntax": "Please confirm that the SQL syntax is correct or does not support this syntax",
    "SH_CheckUnusedLeave": "Remaining Leave",
    "SH_CheckUploaData": "check upload information",
    "SH_CheckUploadData": "verify upload information",
    "SH_CHF": "CHF",
    "SH_Child": "Child",
    "SH_ChildrenEducation": "Children's education",
    "SH_ChildrenEducationBlank": "子女教育(元)未填寫                   ",
    "SH_ChildrenEducationError": "子女教育(元)有誤",
    "SH_ChinaARC": "Mainland foreigner residence permit",
    "SH_ChinaDisabledPersonCard": "Continental disabled card",
    "SH_ChinaID": "Mainland ID card",
    "SH_ChinaMilitaryCard": "Continental military certificate",
    "SH_ChinaMTP": "MTP(Mainland Travel Permit for Taiwan Residents)",
    "SH_ChinaPassport": "Continental passport",
    "SH_ChinaPoliceCard": "Continental armed police officer ",
    "SH_ChinaSoldierCard": "Continental soldier certificate",
    "SH_ChineseCitizen": "Chinese citizen",
    "SH_ChineseSpouse": "Mainland spouse",
    "SH_ChooseItem": "Please select at least one item",
    "SH_Citizenship1": "Mainland China",
    "SH_Citizenship10": "Singaporean",
    "SH_Citizenship11": "Thai",
    "SH_Citizenship12": "Vietnamese",
    "SH_Citizenship13": "Indian",
    "SH_Citizenship14": "Russian",
    "SH_Citizenship15": "Australian",
    "SH_Citizenship16": "New Zealand",
    "SH_Citizenship17": "French",
    "SH_Citizenship18": "German",
    "SH_Citizenship19": "Dutch",
    "SH_Citizenship2": "Taiwan",
    "SH_Citizenship20": "Swiss",
    "SH_Citizenship21": "UK",
    "SH_Citizenship22": "Italian",
    "SH_Citizenship23": "Spanish",
    "SH_Citizenship24": "Danish",
    "SH_Citizenship25": "Finish",
    "SH_Citizenship26": "Norwegian",
    "SH_Citizenship27": "Swedish",
    "SH_Citizenship28": "Polish",
    "SH_Citizenship29": "Canadian",
    "SH_Citizenship3": "Hong Kong",
    "SH_Citizenship30": "US",
    "SH_Citizenship31": "Mexican",
    "SH_Citizenship32": "Argentinian",
    "SH_Citizenship33": "Brazilian",
    "SH_Citizenship34": "Chilean",
    "SH_Citizenship4": "Japanese",
    "SH_Citizenship5": "Korean",
    "SH_Citizenship6": "Macao",
    "SH_Citizenship7": "Indonesian",
    "SH_Citizenship8": "Malaysian",
    "SH_Citizenship9": "Philippine",
    "SH_ClockIn": "clock in",
    "SH_ClockInBlank": "Clock in reason is blank",
    "SH_ClockInError": "Clock in failed",
    "SH_ClockOffBlank": "Clock out reason is blank",
    "SH_ClockOffError": "Clock out invalid",
    "SH_ClockOut": "clock out",
    "SH_Close": "Close",
    "SH_Close02": "Close",
    "SH_Closed": "Closed",
    "SH_Closed03": "Accounting Closing Complete",
    "SH_CloseDate": "結案日期",
    "SH_CLP": "CLP",
    "SH_CN": "CN",
    "SH_CNDeptCategory01": "Philosophy",
    "SH_CNDeptCategory02": "Economics",
    "SH_CNDeptCategory03": "Management Science and Engineering",
    "SH_CNDeptCategory04": "Business management",
    "SH_CNDeptCategory05": "Administration",
    "SH_CNDeptCategory06": "Public management",
    "SH_CNDeptCategory07": "Book archives",
    "SH_CNDeptCategory08": "Language and literature",
    "SH_CNDeptCategory09": "Journalism and Communication",
    "SH_CNDeptCategory10": "Art class",
    "SH_CNDeptCategory11": "Electrical information",
    "SH_CNDeptCategory12": "Computer science and technology machinery and civil engineering",
    "SH_CNDeptCategory13": "Material class",
    "SH_CNDeptCategory14": "Instrumentation",
    "SH_CNDeptCategory15": "Energy power",
    "SH_CNDeptCategory16": "Water conservancy",
    "SH_CNDeptCategory17": "Survey and Mapping Class/Division/Type",
    "SH_CNDeptCategory18": "Pharmaceutical engineering",
    "SH_CNDeptCategory19": "Transportation",
    "SH_CNDeptCategory20": "Naval Architecture and Ocean Engineering",
    "SH_CNDeptCategory21": "Aerospace class",
    "SH_CNDeptCategory22": "Weapons",
    "SH_CNDeptCategory23": "Light Textile Food",
    "SH_CNDeptCategory24": "Public security technology",
    "SH_CNDeptCategory25": "Biomedical engineering",
    "SH_CNDeptCategory26": "Law class",
    "SH_CNDeptCategory27": "Historical Sciences",
    "SH_CNDeptCategory28": "Mathematics",
    "SH_CNDeptCategory29": "Physics",
    "SH_CNDeptCategory30": "Chemical and chemical engineering",
    "SH_CNDeptCategory31": "Biological sciences and biotechnology",
    "SH_CNDeptCategory32": "Astronomical geography",
    "SH_CNDeptCategory33": "Mechanics",
    "SH_CNDeptCategory34": "Electronic information science",
    "SH_CNDeptCategory35": "System science",
    "SH_CNDeptCategory36": "Environmental Science and Safety",
    "SH_CNDeptCategory37": "Medical",
    "SH_CNDeptCategory38": "Psychology",
    "SH_CNDeptCategory39": "Agricultural",
    "SH_CNEthnic01": "Han nationality",
    "SH_CNEthnic02": "Mongol nationality",
    "SH_CNEthnic03": "Hui nationality",
    "SH_CNEthnic04": "Tibetan nationality",
    "SH_CNEthnic05": "Uighur nationality",
    "SH_CNEthnic06": "Miao nationality",
    "SH_CNEthnic07": "Yi nationality",
    "SH_CNEthnic08": "Zhuang nationality",
    "SH_CNEthnic09": "Buyi nationality",
    "SH_CNEthnic10": "Chosen nationality",
    "SH_CNEthnic11": "Manchu nationality",
    "SH_CNEthnic12": "Dong nationality",
    "SH_CNEthnic13": "Yao nationality",
    "SH_CNEthnic14": "Bai nationality",
    "SH_CNEthnic15": "Tujia nationality",
    "SH_CNEthnic16": "Hani nationality",
    "SH_CNEthnic17": "Kazak nationality",
    "SH_CNEthnic18": "Dai nationality",
    "SH_CNEthnic19": "Li nationality",
    "SH_CNEthnic20": "Lisu nationality",
    "SH_CNEthnic21": "Va nationality",
    "SH_CNEthnic22": "She nationality",
    "SH_CNEthnic23": "Lahu nationality",
    "SH_CNEthnic24": "Sui nationality",
    "SH_CNEthnic25": "Dongxiang nationality",
    "SH_CNEthnic26": "Naxi nationality",
    "SH_CNEthnic27": "Jingpo nationality",
    "SH_CNEthnic28": "Kirgiz nationality",
    "SH_CNEthnic29": "Tu nationality",
    "SH_CNEthnic30": "Daur nationality",
    "SH_CNEthnic31": "Mulao nationality",
    "SH_CNEthnic32": "Qiang nationality",
    "SH_CNEthnic33": "Blang nationality",
    "SH_CNEthnic34": "Salar nationality",
    "SH_CNEthnic35": "Maonan nationality",
    "SH_CNEthnic36": "Gelao nationality",
    "SH_CNEthnic37": "Xibe nationality",
    "SH_CNEthnic38": "Achang nationality",
    "SH_CNEthnic39": "Pumi nationality",
    "SH_CNEthnic40": "Tajik nationality",
    "SH_CNEthnic41": "Nu nationality",
    "SH_CNEthnic42": "Uzbek nationality",
    "SH_CNEthnic43": "Russ nationality",
    "SH_CNEthnic44": "Ewenki nationality",
    "SH_CNEthnic45": "Deang nationality",
    "SH_CNEthnic46": "Bonan nationality",
    "SH_CNEthnic47": "Yugur nationality",
    "SH_CNEthnic48": "Gin nationality",
    "SH_CNEthnic49": "Tatar nationality",
    "SH_CNEthnic50": "Derung nationality",
    "SH_CNEthnic51": "Oroqen nationality",
    "SH_CNEthnic52": "Hezhen nationality",
    "SH_CNEthnic53": "Monba nationality",
    "SH_CNEthnic54": "Lhoba nationality",
    "SH_CNEthnic55": "Jino nationality",
    "SH_CNEthnic56": "Gaoshan nationality",
    "SH_CNIndustryName01": "Agriculture, forestry, animal husbandry and fishery",
    "SH_CNIndustryName02": "Mining Industry",
    "SH_CNIndustryName03": "manufacturing",
    "SH_CNIndustryName04": "Electricity/heat/gas and water production and supply",
    "SH_CNIndustryName05": "Building/Construction Industry",
    "SH_CNIndustryName06": "Wholesale and retail trade",
    "SH_CNIndustryName07": "Transportation / warehousing and postal services",
    "SH_CNIndustryName08": "Accommodation and catering industry",
    "SH_CNIndustryName09": "Information transmission / software and information technology services",
    "SH_CNIndustryName10": "Financial industry",
    "SH_CNIndustryName11": "Real estate industry",
    "SH_CNIndustryName12": "Leasing and business services",
    "SH_CNIndustryName13": "Scientific Research and Technology Services Industry",
    "SH_CNIndustryName14": "Water conservancy/environment and public facilities management",
    "SH_CNIndustryName15": "Resident services/repair and other services",
    "SH_CNIndustryName16": "education",
    "SH_CNIndustryName17": "Sanitation and Social Work",
    "SH_CNIndustryName18": "Culture / Sports and Entertainment",
    "SH_CNIndustryName19": "Public administration / social security and social organization",
    "SH_CNIndustryName20": "International organizations",
    "SH_Code": "Code",
    "SH_Comment03": "Comment",
    "SH_Comments": "Comments",
    "SH_Company": "Legal Entity",
    "SH_Company02": "Company",
    "SH_CompanyAnnualLeave": "Company annual leave",
    "SH_CompanyCode": "Company code",
    "SH_CompanyCodeFormatError": "Company code format invalid.",
    "SH_CompanyMail": "Company email address",
    "SH_CompanyName": "Company name",
    "SH_CompanyNotUsedModule": "Your company has not activated this module yet",
    "SH_CompanyPaysPersonalFee": "individual expense-company payment",
    "SH_CompanySpace": "Company Space",
    "SH_CompanyTypeCode": "Code of Legal Entity",
    "SH_CompanyTypeTitle": "Name of Legal Entity",
    "SH_CompanyYOS": "Year of service (internal)",
    "SH_CompanyYOSOfRecognition": "Approved year of service",
    "SH_CompensationPackage": "Compensation Package",
    "SH_Complete": "Complete",
    "SH_Complete02": "Completion",
    "SH_Completed": "Completed",
    "SH_CompleteTime": "Completion time",
    "SH_CompleteWithdraw": "Withdrawal insurance process complete",
    "SH_CompositeWorkHour": "Integrate working hour rules",
    "SH_ConcludedPeriod": "Concluded period",
    "SH_Conclusion02": "Conclusion",
    "SH_ConclusionIsExistAndLaterThenDate": "Concluded data have existed on {0}, new data cannot be created earlier than {0}",
    "SH_Confirm": "Confirm",
    "SH_Confirm02": "Confirm",
    "SH_ConfirmBelowInfo": "Are you sure you want to submit below information？",
    "SH_ConfirmCancel": "Are you sure to cancel?",
    "SH_ConfirmComplete": "Confirm completion",
    "SH_ConfirmConcluded": "Confirm to conclude",
    "SH_ConfirmDelete": "Are you sure to delete?",
    "SH_ConfirmDeleteDataSet": "Are you sure you want to delete these {0} data?",
    "SH_Confirmed": "Confirmed",
    "SH_ConfirmEmployeeData": "Confirm employee information",
    "SH_ConfirmingApprovalProcess": "Confirming approval process",
    "SH_ConfirmLogout": "Are you sure you want to log out？",
    "SH_ConfirmNewPassword": "Please confirm your new password",
    "SH_ConfirmRelease": "Confirm to release",
    "SH_ConfirmSendUrgeApproveMail": "Are you sure you want to send out the notification of approval reminder?",
    "SH_ConfirmSubmit": "Are you sure to submit?",
    "SH_ConfirmToImport": "Confirm to import",
    "SH_ConfirmWithdraw": "Confirm to withdraw",
    "SH_ConsultantPleaseContactCustomerDescription24Hours": "客服/顧問請跟催進度，並於24小時內(工作日)盡快聯繫客戶說明",
    "SH_ContactAddressBlank": "Contact address is blank",
    "SH_ContactAddressCountryArea": "Contact address country/area",
    "SH_ContactAddressCountryAreaBlank": "Contact address country/area is blank",
    "SH_ContactAddressCountryAreaError": "Contact address country/area is incorrect",
    "SH_ContactAddressCountryCity": "Contact address county/city",
    "SH_ContactAddressCountryCityBlank": "Contact address county/city is blank",
    "SH_ContactAddressCountryCityError": "Contact address county/city is incorrect",
    "SH_ContactInformation": "Contact Information",
    "SH_ContactMethod": "Contact method",
    "SH_ContactPerson": "Contact",
    "SH_ContactPersonName": "Contact person",
    "SH_ContactPhoneNumber": "Contact number",
    "SH_ContactPhoneNumberBlank": "Contact number is blank",
    "SH_ContactUs": "Contact us",
    "SH_ContainLeaveWithoutPay": "employees include leave without pay",
    "SH_ContainLeaveWithoutPay02": "include leave without pay",
    "SH_ContainTermination": "Inactive employees",
    "SH_Content": "Contents",
    "SH_ContinueEducation": "Continuation education",
    "SH_ContinueEducationBlank": "繼續教育(元)未填寫",
    "SH_ContinueEducationError": "繼續教育(元)有誤",
    "SH_ContinuePartSeniority": "Recognize partial Year of Service (days)",
    "SH_ContinueSeniority": "Recognize all Year of Service",
    "SH_COP": "COP",
    "SH_Copy": "Copy",
    "SH_CostCenter": "Cost center",
    "SH_CostCenterBasedOnPeoplePlusSetting": "cost center follow the setting of people+",
    "SH_CostCenterBasePASettingBlank": "cost center follow the setting of people+ is blank",
    "SH_CostCenterBasePASettingError": "cost center follow the setting of people+ invalid",
    "SH_CostCenterBlank": "Cost center is blank",
    "SH_CostCenterCategoryBlank": "Cost center type is blank",
    "SH_CostCenterCategoryError": "Cost center type invalid",
    "SH_CostCenterCode": "Cost center code",
    "SH_CostCenterCode02": "Cost center code",
    "SH_CostCenterCodeBlank": "cost center code is blank",
    "SH_CostCenterCodeError": "cost center code invalid",
    "SH_CostCenterCodeNotExist": "cost center code does not exsit",
    "SH_CostCenterError": "Cost center is incorrect",
    "SH_CostCenterListOfSalaryAccount": "CostCenterListOfSalaryAccount",
    "SH_CostCenterName": "Cost center name",
    "SH_CostCenterPercent": "Ratio of cost cenete",
    "SH_CostCenterPercentBlank": "Ratio of cost ceneter is blank",
    "SH_CostCenterPercentError": "Ratio of cost ceneter invalid",
    "SH_CostCenterRatioMustEqualToOneHundred": "員工同一生效日、科目的成本中心比例加總需 = 100",
    "SH_CostCenterSettings": "Cost center settings",
    "SH_CostCenterType": "Cost center type",
    "SH_CostCenterTypeDetail": "Cost center type(1=By department，2=By project，Unfilled=By personal)",
    "SH_CostRate": "Rate",
    "SH_Country": "Country",
    "SH_CountryArea": "Country/Area",
    "SH_Counts": "{0} records",
    "SH_Counts02": "Row {0}",
    "SH_County": "City/County",
    "SH_CountyCity": "county/city",
    "SH_Cover": "Overwrite",
    "SH_CreateForm": "Create sheet",
    "SH_CreateFormOf": "Create a {0} eForm",
    "SH_CreateNewUnit": "Create a new unit",
    "SH_CreateStaff": "People",
    "SH_CreateTime": "Created time",
    "SH_Creator": "Created by",
    "SH_CriticalIllnessInsurance": "major illness insurance",
    "SH_Currency": "Currency",
    "SH_CurrencyCode": "currency code",
    "SH_CurrencyName": "currency",
    "SH_CurrentAddress": "address",
    "SH_CurrentApprover": "Current approver",
    "SH_CurrentPageNumber": "Page {0} / {1}, total {2} item(s)",
    "SH_CurrentYear": "This year",
    "SH_CustomApprovalList": "Custom approval process",
    "SH_CustomList": "customer list",
    "SH_Cycle": "Cycle",
    "SH_CZK": "CZK",
    "SH_Daily": "daily",
    "SH_DailyHours": "daily hours",
    "SH_Dashboard": "Dashboard",
    "SH_DashedUpperUnitCode": "upper code of the dotted line unit",
    "SH_DataCalculation": "Data calculation",
    "SH_DataConfirm": "Confirm",
    "SH_DataCount": "Item number",
    "SH_DataError03": "Data error",
    "SH_DataIsChangedSaveFailed": "Date have been modified, save failed",
    "SH_DataPeriods": "Data interval",
    "SH_DataPermissionError": "Data permission error",
    "SH_DataProcessingCompleted": "This data has been processed",
    "SH_DataSource": "data source",
    "SH_DataTransferFailure": "Date intergation fail, please contact Apollo administator.",
    "SH_DataType": "Data type",
    "SH_DataWrong": "The information is incorrect, please enter again or contact the Apollo service contact for further assistance.",
    "SH_Date": "Date",
    "SH_Date02": "Date : ",
    "SH_DateEnteredTheCountry": "Date enter the country",
    "SH_DateEnteredTheCountryError": "Date enter the country is incorrect",
    "SH_DateOfBirth": "Date of birth",
    "SH_DateOfEntry": "Date entered country",
    "SH_DatePeriods": "Time period",
    "SH_DateTime": "Date / Time :",
    "SH_DateTimeFormatNotMatch": "date/time is invalid",
    "SH_DaughtersChildren": "Grandchildren",
    "SH_Day01": "days",
    "SH_DayHourAndMinute": "{0} days {1} hrs {2} mins",
    "SH_DayOff": "Day-off",
    "SH_Days": "Days",
    "SH_Deadline": "Deadline",
    "SH_DeadlineDate": "Due date",
    "SH_Death": "Death",
    "SH_Dec": "DEC",
    "SH_Decimal": "Decimal",
    "SH_DecimalRule": "Decimal rule",
    "SH_Deduct": "Deduction",
    "SH_DeductedAccordingIncomeTax": "Withholding tax will be followed according to law",
    "SH_DeductionMethodBlank": "withholding type is blank",
    "SH_DeductionMethodError": "withholding type invalid",
    "SH_DeductionRadio": "The ratio of deduction salary",
    "SH_Default": "Default",
    "SH_DefaultPassword": "Default password",
    "SH_Defer": "Extension",
    "SH_DeferredLeave": "Compensatory leave",
    "SH_DeferredLeaveEncashment": "Compensatory leave encashment",
    "SH_DeferredWay": "Extension option",
    "SH_DegreeCountByHours": "Spacing {0} in {1} hours",
    "SH_DegreeNum": "Number of steps",
    "SH_DelayedTo": "extent to ",
    "SH_DelegatedApply": "On behalf of the applicant",
    "SH_DelegatedApplyByEmployee": "{0} on behalf of the applicant",
    "SH_Delete": "Delete",
    "SH_DeleteAll": "delete all?",
    "SH_DeleteChange": "Delete movement records ",
    "SH_DeleteChangeEform": "Delete change form",
    "SH_Deleted": "deleted",
    "SH_DeletedCount": "Delete items：{0}",
    "SH_DeleteFailed": "Failed to delete",
    "SH_DeleteForm": "delete form",
    "SH_DeleteSuccess": "Successfully deleted",
    "SH_Demotion": "Demotion",
    "SH_Denominator": "Denominator",
    "SH_Department": "Department",
    "SH_Department002": "Department",
    "SH_DepartmentCode": "Unit code",
    "SH_DepartmentCodeOrName": "Code or unit name",
    "SH_DepartmentDataInverification": "department information is verifying",
    "SH_Dependents": "Dependents",
    "SH_DependentsEnrollmentCount": "Dependents enrollment nubmers",
    "SH_DependentsExemptionConditions01": "Lineal Ascendant over the age of 60",
    "SH_DependentsExemptionConditions02": "Lineal Ascendant over the age of 60 and being Incapable of Earning a Livelihood",
    "SH_DependentsExemptionConditions03": "Children under the age of 20",
    "SH_DependentsExemptionConditions04": "Children over the age of 20, studying",
    "SH_DependentsExemptionConditions05": "Children over the age of 20, disabled ",
    "SH_DependentsExemptionConditions06": "Children over the age of 20, being Incapable of Earning a Livelihood",
    "SH_DependentsExemptionConditions07": "Siblings under the age of 20",
    "SH_DependentsExemptionConditions08": "Siblings over the age of 20, studying",
    "SH_DependentsExemptionConditions09": "Siblings over the age of 20, disabled",
    "SH_DependentsExemptionConditions10": "Siblings over the age of 20, being Incapable of Earning a Livelihood",
    "SH_DependentsExemptionConditions11": "Artucle 1114, paragraph 4 of the civil code, under the age of 20 or over the age of 60 and being Incapable of Earning a Livelihood",
    "SH_DependentsExemptionConditions12": "Artucle 1123, paragraph 3 of the civil code, under the age of 20 or over the age of 60 and being Incapable of Earning a Livelihood",
    "SH_DependentsIDCardNumber": "Dependents ID card numbers",
    "SH_DependentsInsuranceData": "Dependents Insured information",
    "SH_DependentsName": "Dependents name",
    "SH_DeptCategory": "Department category",
    "SH_Desc": "Description",
    "SH_DetermineReinstatementDate": "Confirm Reinstatement Date",
    "SH_DeuctedProportionOverAmount": "Withholding tax rate will be 5% when monthly salary over NT$40,000",
    "SH_DirectEmployee": "Direct subordinates",
    "SH_DirectManager": "Direct manager",
    "SH_DirectManagerName": "Direct manager name",
    "SH_DirectUnit": "Direct unit",
    "SH_DisabilityManual": "Disability manual",
    "SH_DisabilityNote": "physically challenged remarks",
    "SH_DisabilityStatus": "physically challenged",
    "SH_Disable": "Inactive",
    "SH_DisAgree": "Disagree",
    "SH_Disapprove": "Disapproved",
    "SH_Disapprove02": "Disapprove",
    "SH_DischargingInsurance": "withdrawal insurance",
    "SH_DisplayCount": "display results",
    "SH_DividedTwoTypesCalculations": "依日數分二類計算",
    "SH_DKK": "DKK",
    "SH_DockPay": "Deduction",
    "SH_Domain": "Domain",
    "SH_DomainIsEmailAfterAt": "Domain name is in your email address that after @, e.g. your email <NAME_EMAIL> and then your domain name will be mayohr.com",
    "SH_Done": "Done",
    "SH_Download": "Download",
    "SH_DownloadAttendanceDetail": "Download Attendance Detail",
    "SH_DownloadDetails": "Download the details",
    "SH_DownloadEmployeeData": "export employee data",
    "SH_DownloadExample": "export template",
    "SH_DownloadFailedResult": "Download error messages",
    "SH_DownloadGoogleChrome": "Download Google Chrome",
    "SH_DownloadInvalid": "export invalid information",
    "SH_DownloadReport": "Download report",
    "SH_DownloadTemplate": "Download the template",
    "SH_DownloadTimeout": "Large amount of data caused a downloading timeout, please adjust the data period",
    "SH_DueDate": "due date",
    "SH_DurationOfEmploymentError": "Duration of employment is incorrect",
    "SH_DurationOfStudy": "Duration of study",
    "SH_DuringExpatriateAssignment": "During expatriate",
    "SH_Edit": "Edit",
    "SH_EditCategory": "Edit category",
    "SH_EditField": "Edit fields",
    "SH_EditForm": "modify",
    "SH_EditOnboardingDate": "Modify the on board date",
    "SH_Editor": "editor",
    "SH_EditTimesLimit": "Edit limit：{0}",
    "SH_EducationalInformation": "Education Information",
    "SH_EducationalInformationlist": "Education Information",
    "SH_EducationDegree": "Education background Type",
    "SH_Effective": "Effective",
    "SH_EffectiveDate": "Effective date",
    "SH_EffectiveDateCannotEarlierLatestChangeForm": "effective date cannot ealier than last change request",
    "SH_EffectiveDateCannotEarlierOnboardDate": "effective day cannot be earlier than the Original Hire date",
    "SH_EffectiveDateIsExist": "effective date already exsit",
    "SH_EffectiveDay": "Effective date",
    "SH_EffectiveDayCannotBeforeNewest": "effective date cannot ealier than last change request",
    "SH_EffectiveMonthCannotEarlierEndMonth": "The effective month cannot be less than or equal to the terminate month",
    "SH_EffectiveTime": "Valid period",
    "SH_EformApply": "Apply",
    "SH_eFormApproval": "eForm Approval",
    "SH_EformDeleted": "eForm has been deleted",
    "SH_EformManage": "Query",
    "SH_EformRecord": "Advance Query",
    "SH_EGP": "EGP",
    "SH_ElementarySchool": "Primary school",
    "SH_Email": "E-mail",
    "SH_EmailAddress": "Email address",
    "SH_EmailFormatWrong": "Incorrect email address format",
    "SH_EmergencyContact": "Emergency contact",
    "SH_EmergencyContactPhone": "Emergency contact number (Home phone)",
    "SH_EmergencyContactRelationship": "Relationship with emergency contact",
    "SH_EmpInformation": "Basic Information",
    "SH_Employee": "Employee",
    "SH_EmployeeAttendance": "Employee Attendance",
    "SH_EmployeeBenefitsDeduction": "Employee benefits deduction",
    "SH_EmployeeChangeEformExist": "these employee already exist {0}/1 transcation form({1})",
    "SH_EmployeeData": "Employee Information",
    "SH_EmployeeDataTransferPayrollFailed": "Employee data transfer to Payroll failed, please contact Payroll function administrator.",
    "SH_EmployeeHaveEformCannotAdded": "Form already exsit, cannot create new",
    "SH_EmployeeID": "Employee ID",
    "SH_EmployeeID02": "Employee ID",
    "SH_EmployeeIDOrName01": "Employee ID/Name",
    "SH_EmployeeIDOrName02": "Employee ID/Name :",
    "SH_EmployeeNumberCannotEmpty": "Please input employee number",
    "SH_EmployeeNumberFormatError": "Employee number format invalid.",
    "SH_EmployeeNumberIsEmpty": "Employee ID is blank",
    "SH_EmployeeNumberLeaved": "Employee ID has been resigned",
    "SH_EmployeeNumberNotExist": "Employee ID already exsit",
    "SH_EmployeeNumberOrName": "Employee ID or Name",
    "SH_EmployeeNumberRepeat": "Employee ID have repeated",
    "SH_EmployeePersonalInfo": "Employee's personal information",
    "SH_EmployeeRequired": "The employee field is required",
    "SH_EmployeesHaveBeenResignationSettlement": "以下員工工號已離職結算",
    "SH_EmployeesNotExist": "these employee not exsit : {0}",
    "SH_Employer": "Employer",
    "SH_EmployerContributionRate": "employer withholding rate",
    "SH_EmploymentContract": "Employment contract",
    "SH_EmploymentMethod01": "Full-time employees",
    "SH_EmploymentMethod02": "intern",
    "SH_EmploymentMethod03": "Labor outsourcing",
    "SH_EmploymentMethod04": "Group dispatch",
    "SH_EmploymentMethod05": "Retirement",
    "SH_Empty": "Empty",
    "SH_Enable": "active",
    "SH_EnableCookiesAndReload": "It's likely your browser's cookies are disabled. Please turn cookies on and reload the page, the system will automatically direct you back to the log in page.",
    "SH_EnableDisable": "Enable & Disable",
    "SH_EnableOrDisable": "Active/Inactive",
    "SH_EndDate": "End date",
    "SH_EndDateMoreThenStartDateError": "End time must not be less than the start time",
    "SH_EndMonthCannotEarlierStartMonth": "End month cannot be earlier than the start month",
    "SH_EndTime": "End time",
    "SH_EndTimeMustLaterThanStartTime": "The end time must be later than the start time",
    "SH_English": "English",
    "SH_EnglishName": "preferred name",
    "SH_EnglishOrNumber": "English number",
    "SH_EnrollmentDate": "insurance effective date",
    "SH_EnrollmentDay": "insurance effective date",
    "SH_EnrollmentItem": "Insurance item",
    "SH_EnrollmentRule": "1. 若不加保，則欄位留白  2. 若有加保，則繳費基數需 >= 0",
    "SH_EnterIdentifyNumber": "Please enter your ID No. / Residence Permit No. / Passport No.",
    "SH_EnterKeyword": "Please enter keywords",
    "SH_EnterPasswordWrong": "The password you entered is incorrect, please enter again.",
    "SH_EnterpriseName": "企業名",
    "SH_Entitlement": "Entitlement",
    "SH_ErrorMessage": "Error message",
    "SH_ErrorReason": "failure reason",
    "SH_EstimatePerformanceBonus01": "Estimate performance bonus",
    "SH_EstimatePerformanceBonus02": "Estimate achievement bonus",
    "SH_EstimateYearEndBonus": "Estimate annual bonus",
    "SH_EUR": "EUR",
    "SH_Examine": "Proposed salary",
    "SH_ExceedMonthCalculateMethod": "破月計算方式",
    "SH_ExceptionBelongsSOSLevel": "本異常屬於SOS等級，請立刻處理",
    "SH_ExcessiveBreakTime": "Excessive break time",
    "SH_ExchangeRate": "exchange rate",
    "SH_Exclude": "exclude",
    "SH_ExcludungSomething": "Excluding {0}",
    "SH_ExpatriateContactAddress": "expatriate contact address",
    "SH_ExpatriateTo": "expatriate to {0}",
    "SH_ExpatriateUnit": "expatriate unit",
    "SH_ExpatriateUnitCode": "expatriate unit code",
    "SH_ExpatriateUnitName": "expatriate unit name",
    "SH_ExpirationDate": "Expiration date",
    "SH_Expire": "Expired",
    "SH_Expired": "Invalid",
    "SH_ExpireSoon": "About to expire",
    "SH_Ext": "Extension number",
    "SH_ExtendedWorkHours": "overtime",
    "SH_External": "Overseas",
    "SH_Failed": "Failed",
    "SH_Failed002": "Failed",
    "SH_FailedCount": "Unsuccessful results：{0}",
    "SH_FailedItem": "Unsuccessful items",
    "SH_FailedReason": "Reason failed",
    "SH_FailureTransfer": "Transfer failed",
    "SH_FamilyCareLeave": "Family care leave",
    "SH_Feb": "FEB",
    "SH_Female": "Female",
    "SH_FieldAdditionalBlock": "Field selection",
    "SH_FieldFormatWrong": "{0} wrong format",
    "SH_FieldIsNotExist": "{0} not exsit",
    "SH_FieldIsOddFindOutReasons": "{0} is abnormal, please find out the reason as soon as possible",
    "SH_FieldIsRequired": "{0} required field",
    "SH_FieldLengthLimit01": "The length of the field cannot exceed {0}.",
    "SH_FieldNotFound": "{0} not exsit",
    "SH_FieldRequired": "This field is required.",
    "SH_Fields": "field",
    "SH_FieldSetting": "Field settings",
    "SH_FieldsNumberError": "Incorrect column number",
    "SH_FieldTypeWrong": "{0} input failed",
    "SH_File": "Files",
    "SH_FileAmount": "Number of files",
    "SH_FileExtensionWrong": "The file format is incorrect",
    "SH_FileFormatLimit01": "The file format limit {0}, size limit {1} MB",
    "SH_FileFormatLimit02": "The file format limit {0}",
    "SH_FileLimit01": "File types only allow Microsoft Office, TXT, Compressed file or PDF",
    "SH_FileLimit5": "You can upload maximum {0} files",
    "SH_FileSizeLimit": "Maximum upload file size : {0}",
    "SH_FileSizeLimit02": "Files must be less than {0} , no limit on file numbers",
    "SH_FinalCalculate": "settlement",
    "SH_FinalWorkDate": "Last working date",
    "SH_FirstImportNotCompleted": "Initial import data has not been completed",
    "SH_FirstName": "First name",
    "SH_FirstPage": "First page",
    "SH_FixedAmount": "Fixed amount",
    "SH_FixedAmount02": "Fixed amount {0}",
    "SH_FixedLaborContract": "Labor contract - fixed term",
    "SH_FixedRate": "Fixed ratio",
    "SH_FixedTime": "Fixed period",
    "SH_FJD": "FJD",
    "SH_FNLengthNotMoreThan30Charators": "The total length of FunctionName cannot exceed 30 characters.",
    "SH_FoodAllowanceCannotOverAmount": "total amount of Meal allowance cannot over {0}",
    "SH_ForbiddenSamePassword": "不可與原先密碼相同",
    "SH_ForeignAllowance": "Foreign allowance",
    "SH_ForeignLanguageStudies": "Foreign language",
    "SH_ForeignPeople": "Aliens",
    "SH_ForeignSpouse": "Foreign spouse",
    "SH_ForgetCheckInForm": "Failure to check in/out eForm",
    "SH_FormatMustBeNumbers": "input format should be in numberic",
    "SH_FormatNotConform": "Format incorrect",
    "SH_FormDate": "eForm date",
    "SH_FormNumber": "eForm number",
    "SH_FormParameter": "Form parameters",
    "SH_FormStatus": "eForm status",
    "SH_FormType": "eForm type",
    "SH_Foundation": "Foundation",
    "SH_FourDecimalPlaces": "Four decimal places",
    "SH_Frequency": "frequency",
    "SH_FrequencyOfLeaveExtension": "The frequency of leave extension",
    "SH_Fri": "Fri",
    "SH_Fri02": "Friday",
    "SH_Front": "Before",
    "SH_FullSalarySickLeave": "Full pay sick leave",
    "SH_Function": "{0} function",
    "SH_FuneralLeave": "Bereavement leave",
    "SH_Gender": "Gender",
    "SH_General": "General public",
    "SH_GeneralSetting": "一般設定",
    "SH_Generate": "Create",
    "SH_GenerateLeaveHours": "Create leave hours",
    "SH_GermanyID": "German ID card",
    "SH_GetData": "To obtain data ",
    "SH_GoToVerified": "Go get verified",
    "SH_GraduationStatus": "Graduation status",
    "SH_Grandparent": "Grandparent",
    "SH_Greetings": "greetings",
    "SH_HandInContribution": "Pension contribution",
    "SH_HasCompleted": "{0} {1} {2} has completed",
    "SH_HealthInsuranceDependentsData": "NHI Dependents Info",
    "SH_HighestAcademicDegree": "Highest degree",
    "SH_HighestReductionRate": "Maximum reduction amount",
    "SH_Hint": "tips",
    "SH_HKD": "HKD",
    "SH_Home": "Home",
    "SH_Home02": "Home",
    "SH_HomeADCarouselSeconds": "秒/首頁廣告輪播秒速",
    "SH_HongKongID": "Hong Kong ID card",
    "SH_HourlyWage": "Hourly wage",
    "SH_Hours": "Hours",
    "SH_Hours01": "hr(s)",
    "SH_Hours02": "hr(s)",
    "SH_HousingLoanInterest": "Housing loan interest",
    "SH_HousingLoanInterestBlank": "住房貸款利息(元)未填寫",
    "SH_HousingLoanInterestError": "住房貸款利息(元)有誤",
    "SH_HousingProvidentFund": "Housing provident fund",
    "SH_HRComments": "HR comments",
    "SH_HRK": "HRK",
    "SH_HuaNuage": "HuaNuage",
    "SH_HUF": "HUF",
    "SH_Icon": "Icon",
    "SH_IdDocumentInfo": "ID document information",
    "SH_IdentificationDocumentExpiryDate": "ID expiration date",
    "SH_IdentificationDocumentNumber": "document number",
    "SH_IdentificationDocumentNumberIsExist": "ID card number already exsit",
    "SH_IdentificationDocumentType": "ID type",
    "SH_IdentifyNumber": "ID",
    "SH_IdentityCardNumber": "Identification number",
    "SH_IdentityCardNumberFormatError": "ID card number invalid format",
    "SH_IdentityCardNumberFormatWrong": "Incorrect ID number format",
    "SH_IDIsInvalid": "This ID is invalid",
    "SH_IDNumber02": "ID No. / Residence Permit No. / Passport No.",
    "SH_IDR": "IDR",
    "SH_IDSubclasses": "Employment Subtype",
    "SH_IDType": "Employment Type",
    "SH_ILS": "ILS",
    "SH_ImageFormat": "*Limited format：JPG,JPEG,GIF,PNG ",
    "SH_Import": "Import",
    "SH_ImportDataRepeat": "Import data already exsit",
    "SH_ImportTemplateFormatError": "import format is invalid, please download the template again",
    "SH_InCaculation": "Calculating",
    "SH_IncludingSubUnits": "Including subordinate units",
    "SH_IncomeDataError": "income inforamtion incorrect",
    "SH_IncomeTax": "Income tax",
    "SH_IncomeTaxChangeRecord": "income tax change history",
    "SH_IncomeTaxData": "income tax information",
    "SH_IncomeTaxDependentsData": "Dependents Info for Income tax",
    "SH_IncomeTaxDependentsDataError": "Dependents information incorrect",
    "SH_IncomeTaxTypes01": "50 Non-fixed salary (including income from part-time income)",
    "SH_IncomeTaxTypes02": "9A Income from Professional Practice",
    "SH_IncomeTaxTypes03": "9B Income Derived from Published Articles, Copyright Books,\nMusical Compositions, Musical Productions, Dramas,\nCartoons, or as Remuneration for Speeches and Lectures on\nan Hourly Basis",
    "SH_IncomeTaxTypes04": "91 Income from Contests and Games and from Prizes and Awards Won by Chance",
    "SH_IncomeTaxTypes05": "92 Other Income",
    "SH_IncomeTaxTypes06": "93 Separation income",
    "SH_IncomeTaxTypes07": "94 Employee stock income",
    "SH_IncomeTaxTypes08": "54 Dividend or Earnings income",
    "SH_IncomeTaxWithholdingType": "Income tax withholding mode",
    "SH_IncorrectDataCheck": "Incorrect data check",
    "SH_IncorrectTime": "Time error",
    "SH_IndustryType": "Type of industry",
    "SH_InjuryInsurance": "employment injury insurance",
    "SH_InProgress": "In progress",
    "SH_Input": "input",
    "SH_InputLimit01": "limited to {0} words",
    "SH_InputLimit02": "must be alphabic or numberic format, limit to {0} characters",
    "SH_InputLimit03": "limited to {0} characters",
    "SH_InputLimit04": "maximum {0} characters ",
    "SH_InputWithdrawDate": "Please enter the withdrawal date",
    "SH_INR": "INR",
    "SH_InRating": "Rating",
    "SH_InSettlement": "Executing",
    "SH_Insurance02": "insurance type",
    "SH_Insurance03": "insurance",
    "SH_InsuranceAmount": "amount insured",
    "SH_InsuranceData": "Insurance information",
    "SH_InsuranceDataError": "insurance date incorrect",
    "SH_InsuranceType": "Insured classification",
    "SH_InsuranceUnit": "insured unit",
    "SH_InsuranceUnitCannotOverlap": "Unit and affiliated unit must be in differecnt unit",
    "SH_InterDepartmentalSupport": "Inter-departmental support",
    "SH_InterfaceTransferOddFindOutReasons": "Interface is abnormally transferred, please find out the cause as soon as possible.",
    "SH_InterfaceTransferWrong": "Interface conversion error",
    "SH_Interim": "Interim",
    "SH_Internal": "Domestic",
    "SH_InternalVacancies": "Internal Job Posting",
    "SH_Invalid": "Void",
    "SH_IPAddressRequired": "The IP address field is required",
    "SH_IsBlank": "{0}is blank",
    "SH_IsConfirmRelease": "Are you sure you want to release?",
    "SH_IsIncorrect": "{0}is incorrect",
    "SH_IsItPrimaryCostCenter": "Is it primary cost center",
    "SH_IsItVerified": "Verified",
    "SH_IssueDate": "Date of issue",
    "SH_IssuingAuthority": "Issuing authority",
    "SH_Item": "Item",
    "SH_Jan": "JAN",
    "SH_JobApplicationRecords": "Job Application Records",
    "SH_JobBand": "Job Grade",
    "SH_JobBandYOS": "Year of service (Job Grade)",
    "SH_JobCategory": "Job series",
    "SH_JobFamily": "Job family",
    "SH_JobGrade": "Job level",
    "SH_JobGradeError": "Job level is blank",
    "SH_JobGradeExperienceYears": "Approved year of service(job grade) before onboard",
    "SH_JobGradeOfRecognition": "Approved year of service(job grade) ",
    "SH_JobGradeYOS": "Year of service (Job level)",
    "SH_JobInformation": "job role information",
    "SH_JobTitle": "Job title",
    "SH_JoiningDate": "Original Hire date",
    "SH_JPY": "JPY",
    "SH_JRManagementAdminRecruiter": "職缺需求管理-總招募",
    "SH_Jul": "JUL",
    "SH_Jun": "JUN",
    "SH_JuniorHighSchool": "Secondary school",
    "SH_KES": "KES",
    "SH_KeyInNotAllowed": "< or \">\" is not allowed",
    "SH_Keyword": "Keywords",
    "SH_Keywords": "Keywords",
    "SH_KRW": "KRW",
    "SH_LaborInsurance": "Labor insurance",
    "SH_LaborInsuranceChangeRecord": "labor insurance change history",
    "SH_LaborInsuranceIdentity": "labor insurance identity",
    "SH_LaborInsuranceRenewal": "Continuation of labor insurance",
    "SH_LaborInsuranceSalary": "labor insurance insured salary",
    "SH_LaborPension": "Labor pension",
    "SH_LaborPensionChangeRecord": "Labor pension movement records ",
    "SH_LaborPensionEmployerContributionRate": "Labor Pension - employer withholding rate",
    "SH_LaborPensionInsuranceSalary": "Labor Pension",
    "SH_LaborPensionPersonalContributionRate": "Labor Pension - employee withholding rate",
    "SH_LaborReduceIdentity": "labor insurance exemption identity",
    "SH_LaborRetirementReserveFundsRate": "The allocation rate(%) of the labors' retirement reserve funds",
    "SH_Language": "Language",
    "SH_LanguageFamily": "Language Family",
    "SH_LanguageSkill": "Language & Skill",
    "SH_LassoStatusModifyErrorPlsContactApollo": "process error, please contact apollo service window",
    "SH_LastModifier": "Last modified by",
    "SH_LastModifyDate": "Last modified time",
    "SH_LastMonthDifference": "difference by last month",
    "SH_LastName": "Last name",
    "SH_LastPage": "Last page",
    "SH_LastUpdated": "Last updated : {0}",
    "SH_LastWorkDayOfLeaveWithoutPay": "The last day of working",
    "SH_LastWorkDayOfLeaveWithoutPay02": "last working date for leave without pay",
    "SH_LastWorkDayOfResignation": "last date of resignation",
    "SH_LastYearDifference": "difference in last year ",
    "SH_Late": "Late",
    "SH_LatestWorkingCompany": "Latest company name",
    "SH_LatestWorkingJobTitle": "Latest job title",
    "SH_Laywer": "laywer",
    "SH_Leading": "Leading",
    "SH_LeaveApplicationCode": "Leave No. ",
    "SH_LeaveCancellation": "Withdrew the leave form",
    "SH_LeaveDeduction": "Leave deduction",
    "SH_LeaveDeductionMaternity": "Leave deduction - Maternity leave",
    "SH_LeaveEarly": "Leave early",
    "SH_LeaveForm": "Leave eForm",
    "SH_LeavePolicy": "Leave policy details",
    "SH_LeaveType": "Type of leave",
    "SH_LeaveWithoutPay": "Leave without Pay",
    "SH_LegalAnnualLeave": "legal annual leave",
    "SH_LevelError": "Level invalid",
    "SH_LicenseEffectiveDate": "License effective date",
    "SH_LicenseInformation": "License or Certificate Information",
    "SH_LicenseInformationlist": "License or Certificate Information",
    "SH_LicenseName": "Certificate Name",
    "SH_LIExemptionIdentity": "labor insurance exemption identity",
    "SH_LIExemptionNote": "labor insurance exemption remarks",
    "SH_LightSignal": "Color codes",
    "SH_LIInsuredIdentity": "labor insurance identity",
    "SH_LinkInvalid": "This link is invalid",
    "SH_LinkUpAdministratorLimit": "Please make sure you have at least one active LinkUp administrator",
    "SH_List": "List",
    "SH_ListComparisonTable": "List comparison list",
    "SH_ListItem": "List item",
    "SH_LISubsidyRate": "labor insurance - subsidy rate",
    "SH_Load": "Loading...",
    "SH_LOAEffectiveDate": "leave without pay effective date",
    "SH_Location": "Location : ",
    "SH_Location02": "Location",
    "SH_LocationDesc": "Location description",
    "SH_LocationDesc02": "Location description :",
    "SH_Login": "Login",
    "SH_LoginPassword": "Log in password",
    "SH_Logout": "Log out",
    "SH_LongSickLeave": "Long sick leave",
    "SH_LossInsuranceQualification": "Loss of insurance qualification",
    "SH_LowerDelete": "delete",
    "SH_LowerTo": "to",
    "SH_LPEmployeeWithholdingRate": "Labor Pension - employee withholding rate",
    "SH_LPEmployerWithholdingRate": "Labor Pension - employer withholding rate",
    "SH_LTL": "LTL",
    "SH_MacauID": "Macau ID card",
    "SH_MAD": "MAD",
    "SH_MailInform": "寄發信件通知",
    "SH_MailUnBoundUserS05": "因Apollo 存有敏感個人資料，建議您盡快使用AD帳號登入系統。",
    "SH_MainlandChinese": "Mainland Chinese",
    "SH_MainlandForeignNonresiden": "Mainland foreign non-residen",
    "SH_MainlandForeignResiden": "Mainland foreign residen",
    "SH_MaintenanceType01": "Maintaining type",
    "SH_MaintenanceType02": "Maintaining type :",
    "SH_MajorName": "Major",
    "SH_MajorType": "Department of major",
    "SH_Male": "Male",
    "SH_ManageAccount": "Account Management",
    "SH_Management": "management",
    "SH_Manager": "Manager",
    "SH_ManagerName": "Manager name",
    "SH_ManagerSpace": "Manager Space",
    "SH_ManagingAnnouncement": "Announcement management",
    "SH_ManagingFunction": "Managing functions",
    "SH_ManualEntry": "Manual entry",
    "SH_ManuallyCreate": "Manually create",
    "SH_Mar": "MAR",
    "SH_MatchCondition": "suitable condition",
    "SH_MaterialStatus": "Marital status",
    "SH_MatermityInsurance": "Maternity insurance",
    "SH_MaternalGrandparents": "Grandparent",
    "SH_MaternalGreatGrandParents": "Grand Grandparent",
    "SH_MaternityLeave": "Maternity leave",
    "SH_MaxAttemptsReachedPlsContactHR": "Due to entering incorrectly after {0} attempts, please enter again or contact HR administrator for further assistance.",
    "SH_MaximumAmount2400": "The maximum amount is 2400",
    "SH_MaximumDisplay500": "最多顯示前500筆",
    "SH_MaxSubsidyAmount": "Maximum subsidy",
    "SH_May": "MAY",
    "SH_MealAllowance": "Meal Allowance",
    "SH_MedicalInsurance": "Medical insurance ",
    "SH_MenstrualLeave": "Menstrual leave",
    "SH_Message": "Message",
    "SH_MessageWall": "Message board",
    "SH_MilitaryServiceEndTime": "Military service end time",
    "SH_MilitaryServicePeriod": "Military service period",
    "SH_MilitaryServiceStartTime": "Military service begin time",
    "SH_MilitaryServiceStatus": "Military status",
    "SH_MilitaryServiceType": "Military service type",
    "SH_Minute": "min(s)",
    "SH_Minutes": "Minutes",
    "SH_MissingSixMonths": "Missing for six months",
    "SH_ModificationRecord": "Modification record",
    "SH_Modifier": "Updated person",
    "SH_Modify": "Edit",
    "SH_ModifyDate": "Updated time",
    "SH_ModifyHistory": "modify history",
    "SH_ModifyReason": "Reason for editing",
    "SH_Module": "模組",
    "SH_ModulesSetting": "Modules Setting",
    "SH_Mon": "Mon",
    "SH_Mon02": "Monday",
    "SH_MoneyCannotEmpty": "please input amount",
    "SH_MoneyIsEmpty": "amount is empty",
    "SH_MoneyMustBeNumbers": "amount should be in numberic",
    "SH_Month": "Month",
    "SH_Month01": "Month",
    "SH_MonthlyBasicWageZone": "area of the monthly basic salary",
    "SH_MonthlyBasicWageZoneBlank": "area of the monthly basic salary is blank",
    "SH_MonthlyBasicWageZoneError": "area of the monthly basic salary is incorrect",
    "SH_MonthlyHours": "monthly hours",
    "SH_MonthlyLeaveEncashment": "Monthly leave encashment",
    "SH_MonthlyLeaveUnit": "Monthly leave",
    "SH_MonthlyPaymentTotal5Percen": "5% withholding rate",
    "SH_MonthlySalary": "Monthly salary",
    "SH_Months": "Months",
    "SH_MoreThanReasonInputLimit": "Limit to {0} letters",
    "SH_MXN": "MXN",
    "SH_MyData": "My Data",
    "SH_MyModules": "My Modules",
    "SH_MYR": "MYR",
    "SH_MyTeam": "My Team",
    "SH_Name": "Name",
    "SH_Name02": "Name :",
    "SH_Name03": "Name",
    "SH_NameBlank": "Certificate Name is blank",
    "SH_NameKeyword": "Enter keyword of the name",
    "SH_NameKeyword02": "Enter keyword of the name",
    "SH_NameKeyword03": "Keywords of name",
    "SH_NationalHoliday": "National holiday",
    "SH_Nationality": "Nationality",
    "SH_Nationality02": "Identity",
    "SH_NativeCountryPeople": "Native",
    "SH_NaturalIdentityTypeRule": "證號別0、3、5、7、9、A時，此欄位才需填寫/必填",
    "SH_NeedFillInSalarySubjectCostCenterWorksheet": "若為否，需填 \"薪資科目成本中心\" 工作表",
    "SH_NewApply": "New apply",
    "SH_NewHire": "New hire",
    "SH_NewInstitution": "New pension",
    "SH_NewYearAllowance": "New Year Allowance",
    "SH_Next": "Next",
    "SH_Next02": "Next",
    "SH_NextMonth": "Next month",
    "SH_NextPage": "Next page",
    "SH_NextToGenerate": "Next step, create the data",
    "SH_NHI": "National health insurance",
    "SH_NHIChangeRecord": "NHI change history",
    "SH_NHIDependentsDataError": "Dependents information of NHI incorrect",
    "SH_NHIExemptionIdentity": "NHI exemption identity",
    "SH_NHIExemptionNote": "NHI exemption remarks",
    "SH_NHIInsuranceSalary": "premium salary of NHI",
    "SH_NHIInsuredIdentity": "NHI - Insured identity",
    "SH_NHIMaxSubsidyAmount": "NHI - Maximum subsidy amount",
    "SH_NHIReduceIdentity": "NHI exemption identity",
    "SH_NHIRenewal": "Continuation of national health insurance",
    "SH_NHISubsidyRate": "NHI - subsidy rate",
    "SH_No": "No",
    "SH_NoActionNeeded": "No action needed",
    "SH_NoApprovalRecord": "No approval history",
    "SH_NoAuthorizationToAccess": "No authorization to access, please click confirm to return to the home page",
    "SH_NoCheckIn": "Forgot to clock in",
    "SH_NoCheckOut": "Forgot to check out",
    "SH_Nocompanyemail": "company email is blank, please contact the HR administrator.",
    "SH_NoDeduction": "Not deducted",
    "SH_NoEducationalInformation": "No education information",
    "SH_NoEligiblePeopleFound": "There are no eligible people to be found",
    "SH_NoEmergencyContact": "No emergency contact",
    "SH_NoEmployeeId": "This employee no. does not exist.",
    "SH_NoEnrollmentNoNeedFillIn01": "若不加保，則不需填寫勞保相關欄位",
    "SH_NoEnrollmentNoNeedFillIn02": "若不加保，則不需填寫勞退相關欄位",
    "SH_NoEnrollmentNoNeedFillIn03": "若不加保，則不需填寫健保相關欄位",
    "SH_NoFindData": "Cannot find any information",
    "SH_NoFindUser": "Cannot find this user",
    "SH_NOK": "NOK",
    "SH_NonCompetitionAgreement": "Non-competition agreement",
    "SH_NonDisclosureAgreement": "Confidentiality agreement",
    "SH_None": "None",
    "SH_NoNotification": "Not notified",
    "SH_NonresidentIdentityTypeRule": "證號別5 ~9時，此欄位才需填寫/非必填",
    "SH_NoPermissionToImport": "No permission while importing this employee",
    "SH_NoRequiredData": "You must fill in all the required fields",
    "SH_NoSetting": "Not set up yet ",
    "SH_NotAccept": "Deny",
    "SH_NotAllowedDisable": "Not allowed to disable",
    "SH_NotAllowedRemoval": "Removal is not allowed after approval",
    "SH_NotApply": "Cannot apply",
    "SH_NotAuthorized": "Not authorized",
    "SH_NotChooseUploadfile": "No upload file selected",
    "SH_NotContinueSeniority": "No Year of Service recognition",
    "SH_NotDateFormat": "Do not match the format of date",
    "SH_NotDeduct": "No deduction",
    "SH_NoteInputLimit": "remarks limit to {0} characters",
    "SH_NoteInputLimit02": "limited to {0} characters",
    "SH_NoteInputLimit03": "remarks exceed {0} characters ",
    "SH_NoteSelectLimit": "Note: only allow select up to {0} records at the same time.",
    "SH_NotFindApprovalEform": "Cannot find approval eForm",
    "SH_NotFindAuthorization": "Cannot find the authorization, please contact HR administrator",
    "SH_NotFindData02": "no data found",
    "SH_NotFindEmployee": "This person does not exist",
    "SH_NotFindExcelFile": "excel file not found",
    "SH_NotFindFormData": "Cannot find this eForm",
    "SH_NotFoundLogoPicture": "Logo does not exsit",
    "SH_NotFoundSettlementData": "no settlement information found",
    "SH_NotFoundSettlementEmployee": "no settlement employee information found",
    "SH_NoThisEmpDataAuthority": "the person is no permission",
    "SH_Notification": "push message",
    "SH_NotProcessed": "not processed",
    "SH_NotReceived": "not received",
    "SH_NotReduceSalary": "No deduction",
    "SH_NotRequired": "Optional",
    "SH_NotSaveData": "Changes you made have not been saved, are you sure you want to leave this page?",
    "SH_NotSelectMoreThan": "Cannot select more than {0} options",
    "SH_NotSetting": "No settings",
    "SH_NotSupportFormat": "Format is not supported",
    "SH_NotTimeFormat": "Do no match the format of time",
    "SH_Nov": "NOV",
    "SH_NoVideoInterviewQuestionsSet": "The video interview question has not been set and the candidate will enter the phase of resume review.",
    "SH_NoWarning": "No reminder",
    "SH_NoWorkHistory": "No work experience",
    "SH_NTD": "NTD",
    "SH_NumberOfAccurateData": "Number of accurate data",
    "SH_NumberOfInaccurateData": "Number of inaccurate data",
    "SH_NumbersRange": "Number between 0~{0}",
    "SH_Numerator": "Numerator",
    "SH_NZD": "NZD",
    "SH_OccpationalSicknessLeave": "Occupational injury leave",
    "SH_Oct": "OCT",
    "SH_OddNotification": "notification",
    "SH_OfficalLeave": "Official leave",
    "SH_OfficialBusiness": "Official trip",
    "SH_OfficialMobile": "Company mobile",
    "SH_OldInstitution": "Old pension system",
    "SH_OldPassword": "Please enter your current password",
    "SH_OnBoard": "Already on board",
    "SH_OnboardDate": "Onboard date",
    "SH_Onboarding": "Onboarding",
    "SH_OnBoardInterval": "On board period",
    "SH_OnboardRequiredDocument": "Onboard required documents",
    "SH_OnBusinessTrip": "Business trip",
    "SH_OneDecimalPlace": "One decimal place",
    "SH_OneLevelUnitCode": "upper level unit code",
    "SH_OneLevelUnitName": "upper level unit name",
    "SH_OneLevelUpUnit": "upper level",
    "SH_Ongoing": "On progress",
    "SH_OnLeave": "On leave",
    "SH_OnTheDay": "Daily",
    "SH_Open": "Open",
    "SH_OpenFile": "Open file",
    "SH_OpenFileError01": "The file is empty",
    "SH_OperationBehavior": "Operation behavior",
    "SH_OperationName": "process name",
    "SH_OperationTime": "Operation time",
    "SH_OptionArea": "Additional information",
    "SH_Or": "or",
    "SH_Organization": "Organization",
    "SH_OrganizationDetails": "Organization details",
    "SH_Origin": "Source",
    "SH_OriginalApprover": "Original approver",
    "SH_Other": "other",
    "SH_Other01": "Other",
    "SH_OtherAnnualBonus": "Other Annual bonus",
    "SH_OtherAttachments": "Other attachments",
    "SH_OtherComments": "Other footnotes",
    "SH_OtherIDType": "Other ID Type",
    "SH_OtherLeaveEncashment": "Other leave encashment",
    "SH_OtherNotes": "Other notes",
    "SH_OtherReasonDesc": "Description of the reason",
    "SH_OtherSetting": "Other settings",
    "SH_OtherTaxpayerTaxIDLength": "其他所得人統編不得大於10",
    "SH_OverallAttendanceAdministrator": "Overall attendance administrator",
    "SH_Overtime": "Overtime",
    "SH_OvertimeApplicationCode": "Overtime No.",
    "SH_OvertimeCancellation": "Overtime withdrew form",
    "SH_OvertimeForm": "Overtime eForm",
    "SH_OvertimePay": "Overtime pay",
    "SH_OvertimeType": "Overtime type",
    "SH_OwnCountry": "Home country",
    "SH_Page01": "No. {0} page, total {1} page(s)",
    "SH_PaidSickLeave": "Paid sick leave",
    "SH_ParameterSetting": "Parameter settings",
    "SH_Parent": "Parent",
    "SH_PartialWorkingHours": "Partial working hours",
    "SH_PartTimeIncomeTax": "Income tax for the wages from a part-time job",
    "SH_PassportNumber": "Passport",
    "SH_Password": "Password",
    "SH_PasswordHasBeenReset": "The password has been reset. Please ask the employee to login in with default password and change it later.The default password is Apollo+ MMDD (Your birthday).",
    "SH_PasswordInputIncorrect": "Password Invalid",
    "SH_PasswordLimit": "Your password must be at least 8 characters and contain numbers and both upper and lower case letters",
    "SH_PasswordProtection": "Protected password",
    "SH_PastWorkExperienceInfo": "Past work experience information",
    "SH_PastYear": "past year",
    "SH_PatermityLeave": "Paternity leave",
    "SH_PayableBasicDate": "Payroll basis date",
    "SH_PayByHour": "Hourly",
    "SH_PayByMonth": "Monthly",
    "SH_PaymentAllocationDate": "Payroll period attribution date",
    "SH_PaymentCardinalNumbers": "Payment base number ",
    "SH_PaymentRecord": "Payment records",
    "SH_PaymentType": "Payment type",
    "SH_Payroll": "Payroll",
    "SH_PayrollChangeRecordsExist01": "Payroll change records later than the effective date",
    "SH_PayrollChangeRecordsExist02": "Payroll change records on the effective date",
    "SH_PayrollChangeRecordsExist03": "Payroll change records earlier than the effective date",
    "SH_PayrollDataCreationHasFailed02": "Failed to create the payroll NHI dependent insurance information due to the existing dependent has not withdrawn yet",
    "SH_PayrollDataCreationHasFailed03": "Failed to create the payroll dependents information due to duplicate ID number",
    "SH_PayrollDependentsIsExist01": "these dependents have not withdrawal the insurance health yet, cannot select this dependents : {0}",
    "SH_PayrollDependentsIsExist02": "these relative has already exsit in payroll：{0}",
    "SH_PayrollDependentsIsExist03": "健保眷屬存在於Payroll尚未退保",
    "SH_PayrollDetails": "Salary details",
    "SH_PayrollEffectiveDateError01": "Payroll has a transaction history after this effective date",
    "SH_PayrollEffectiveDateError02": "Payroll has a transaction history on the same effective date",
    "SH_PayrollEffectiveDateError03": "Payroll has a transaction history on the same effective date, and the transaction behavoir not in termniate / suspend / transfer out : {0}",
    "SH_PayrollEffectiveDateError04": "Payroll has a transaction history earlier than the effective date, and does not withdrawal yet : {0}",
    "SH_PayrollNotification": "Payroll Notification",
    "SH_PayrollYype": "Payroll type",
    "SH_PEN": "PEN",
    "SH_PendingForApproval": "Pending for approval",
    "SH_PensionInsurance": "Endowment insurance",
    "SH_PeopleNumber": "Employee #",
    "SH_Percentage": "Percentage",
    "SH_PercentLimit": "The sum of percentage cannot be more than 100%",
    "SH_PercentMustBeInteger": "ration must be in integer between 1 to 100",
    "SH_Period": "Period",
    "SH_Periodicity": "Periodic",
    "SH_Permanent": "Valid forever",
    "SH_PermanentAddress": "Residential address",
    "SH_Person": "{0} Person(s)",
    "SH_Personal": "Personal",
    "SH_PersonalContributionRate": "employee withholding rate",
    "SH_PersonalData": "Personal Info",
    "SH_PersonalLeave": "Personal leave",
    "SH_PersonalRecord": "Personal record",
    "SH_PersonalSalaryContent": "personal payroll content",
    "SH_PersonalSalaryContentError": "personal payroll content invalid",
    "SH_PersonalSpace": "Personal Space",
    "SH_PersonalWorkTimetable": "Personal Work Timetable",
    "SH_PersonData": "Employee information",
    "SH_Personnel": "Employee",
    "SH_PersonnelAuthorityQuery": "人員權限查詢",
    "SH_PersonRole": "Role of employee",
    "SH_PerThreeMonths": "Every three months",
    "SH_Phone": "Mobile Phone",
    "SH_Phone02": "Home phone",
    "SH_Phone03": "Phone number",
    "SH_PHP": "PHP",
    "SH_PicDesc": "Icon descriptions :",
    "SH_PicFileLimit": "File types only allow JPG, JPEG, GIF, PNG, PDF",
    "SH_PKR": "PKR",
    "SH_PleaseEnterWithdrawalReason": "Please enter the withdrawal reason",
    "SH_PleaseEnterYour": "Please enter your {0}",
    "SH_PleaseFillInTheResume": "The completeness and correctness of the resume will affect the qualification examination. Please fill in and submit the job title. Are you confirm to save?",
    "SH_PleaseSelect": "Select",
    "SH_PleaseSelectCategory": "please Select type",
    "SH_PLN": "PLN",
    "SH_PlsCreateChange": "please create the changes form first ",
    "SH_PlsEnterAddress": "Please enter the address",
    "SH_PlsEnterCompanyCodeAndEmployeeNumberToGetResetPasswordLink.": "請輸入您的公司代碼及工號，我們將會寄送重置密碼連結寄至您的信箱",
    "SH_PlsEnterDateOfBirth": "Please enter your date of birth",
    "SH_PlsEnterDescription": "please input description",
    "SH_PlsEnterDomainLocationToLogin": "Please enter a domain name you would like to use and we will login to your Apollo via {type} account.",
    "SH_PlsEnterEmployerContributionRate": "類別為\"新制\"時，請輸入雇主提繳率0~100",
    "SH_PlsEnterEndTime": "Please enter end time",
    "SH_PlsEnterHighestReductionAmount": "欄位\"殘疾身分\"為\"是\"時，請輸入最高減徵金額",
    "SH_PlsEnterHighestSubsidyAmount": "減免身分為\"是\"時，請輸入補助金額(正整數)",
    "SH_PlsEnterIdentityCardNumber": "Please enter identification number",
    "SH_PlsEnterIPAddress": "Please enter an accurate IP address",
    "SH_PlsEnterKeyword": "Please enter keywords",
    "SH_PlsEnterName": "Please enter name",
    "SH_PlsEnterNewPassword": "Please enter your new password",
    "SH_PlsEnterNHIInsuredUnitCode": "補充保費身分 為1、2、6、7時，請輸入健保投保單位代碼",
    "SH_PlsEnterPassword": "Please enter your password",
    "SH_PlsEnterPasswordAgain": "Please enter your password again",
    "SH_PlsEnterPeopleNumber": "Please enter the number of people",
    "SH_PlsEnterPersonalContributionRate": "類別為\"新制\"時，請輸入個人提繳率0~100",
    "SH_PlsEnterReductionRate": "欄位\"殘疾身分\"為\"是\"時，請輸入減徵費率",
    "SH_PlsEnterReliefResult": "減免身分 為\"是\"時，請輸入減免結果",
    "SH_PlsEnterResult": "While the withholding type is 4 or 5，please enter the result.",
    "SH_PlsEnterStartTime": "Please enter start time",
    "SH_PlsEnterSubsidyRate": "減免身分為\"是\"時，請輸入補助費率1~100",
    "SH_PlsEnterTitle": "Please enter the title",
    "SH_PlsEnterWithdrawalReasonRequired": "Please enter the withdrawal reason (required) :",
    "SH_PlsEnterYourCompanyCode": "Please enter company code",
    "SH_PlsEnterYourEmployeeNumber": "Please enter your employee number",
    "SH_PlsEnterYourIdNumber": "Please enter your ID number",
    "SH_PlsFillDesc": "Please fill in the description",
    "SH_PlsFillInDomainToAzureLogin": "Please enter a domain name you would like to use and we will login to your Apollo via Microsoft Azure",
    "SH_PlsFillReasonDesc": "Please describe the reason",
    "SH_PlsInputCode": "please enter code",
    "SH_PlsLoginAgain": "You have logged out the system, please log in again",
    "SH_PlsRanking": "Please provide the resume ranking",
    "SH_PlsRating": "Please leave a rating",
    "SH_PlsRememberAddSalarySubject": "請記得至「薪資資料」新增薪資津貼科目 ",
    "SH_PlsSelectAFile": "please select at least one record",
    "SH_PlsSelectAgreeOrDisAgree": "Please select approve or disapprove",
    "SH_PlsSelectCalendar": "Please select the calendar",
    "SH_PlsSelectCategory": "Please select category",
    "SH_PlsSelectCloudLocation": "select cloud server",
    "SH_PlsSelectDate": "Please select a date",
    "SH_PlsSelectDept": "Please select department",
    "SH_PlsSelectEmp": "Please select an employee",
    "SH_PlsSelectFile": "Please select the file",
    "SH_PlsSelectLocation": "Please select a location",
    "SH_PlsSelectMonth": "Please select a month",
    "SH_PlsSelectOneOption": "Please select at least one option",
    "SH_PlsSelectOption": "Please select an option",
    "SH_PlsSelectReason": "Please select a reason",
    "SH_PlsSelectReceiver": "Please select the receiver",
    "SH_PlsSelectRelationship": "Please select relationship",
    "SH_PlsSelectStatus": "Please select a status",
    "SH_PlsSelectTime": "Please select time",
    "SH_PlsSelectType": "Please select a type",
    "SH_PlsSelectYear01": "Please select a year",
    "SH_PlsSelectYear02": "Please select a year",
    "SH_PlsUploadExcelFile": "Please upload an excel file",
    "SH_PlsUploadFile": "Please upload a file",
    "SH_PlsUploadImageFile": "Please upload an image file",
    "SH_PlsUseAdAccountLogin": "Please use miscrosoft AD account to login",
    "SH_PlsUseMicrosoftAccountLogin": "Sign in with Microsoft",
    "SH_PlzChooseAgent": "Please select the Agent",
    "SH_PlzSetApproveRule": "No approval policy yet, please contact HR administrator",
    "SH_PlzWaitBatchApprovedSuccess": "Batch approval is in progress and please wait as this may take several minutes...",
    "SH_PoliticalStatus01": "Party member",
    "SH_PoliticalStatus02": "member",
    "SH_PoliticalStatus03": "The group",
    "SH_PositionDetails": "Position details",
    "SH_PositiveInteger": "Positive integer",
    "SH_Postdoc": "Postdoctoral",
    "SH_PreEmployment": "Pre-employment",
    "SH_PreMaternityLeave": "Pregnancy Checkup Leave",
    "SH_PreMonth": "Every",
    "SH_PrePage": "Previous",
    "SH_PresentAddress": "Contact address",
    "SH_Preview": "Preview",
    "SH_Previous": "Previous",
    "SH_PreviousMonth": "Last month",
    "SH_PreviousStep": "back",
    "SH_PreYearSomething": "Every {0}",
    "SH_Print": "Print",
    "SH_PrintCondition": "print condition",
    "SH_PrintDate": "Print date",
    "SH_PrintPayslip": "Print payslip",
    "SH_PrivacyAgree": "I have read and accept Privacy Policy",
    "SH_PrivacyPolicy": "Privacy Policy",
    "SH_PrivacyPolicyChange": "Changes of Apollo privacy policy",
    "SH_PrivacyPolicyChangeDetail": "Apollo privacy policy may change from time to time or for the purpose to compliance with laws.",
    "SH_PrivacyPolicyContactUs": "If you have any questions regarding Apollo privacy policy, information in below or related to personal data protections, please go to 【Contact us】 to leave your questions and we will get back to you as soon as possible.",
    "SH_PrivacyPolicyContent": "Apollo privacy policy content",
    "SH_PrivacyPolicyHowUse": "How Apollo use your personal information",
    "SH_PrivacyPolicyHowUseDetail": "We collect, process and use users personal information for business operation, provide services, marketing business, consumers and customers management and related supporting services. We collect information to maintain and improve our services, developing new functions, satisfy users needs and for internal company reports. Furthermore, we keep all records of your communication and messages to help solve any issues from users. We will not provide user personal information to a third party or for other purposes unless with your consent in advance, and will compliance with appropriate law regulations. Finally, we will also ask for users consent before using information for other purposes other than those state in this ?Privacy Policy?.",
    "SH_PrivacyPolicyInclude": "Below is Apollo privacy policy to make you understand we support and protect personal privacy, and we follow laws and regulations when we collect, process and use your personal information, including:",
    "SH_PrivacyPolicySecurity": "Security of Apollo cloud service",
    "SH_PrivacyPolicySecurityDetail": "Apollo uses reasonable and legitimate protections such as SSL encryption technology helps to prevent from malicious damages such as access, alteration, copy or disclosure our information. In addition, we use Microsoft Azure cloud services as a high standard security protection including automatic scanning for virus, security loophole detection and certified as high level global cloud security etc. MAYO will also take into actions for security check periodically to avoid malware hack our data system and secure the safety of users information.",
    "SH_PrivacyPolicyWhyCollect": "What data Apollo collect and why we collect it",
    "SH_PrivacyPolicyWhyCollectDetail": "We collect data to make our services better for our users. We will collect users event information after they registered their Apollo accounts for using Apollo cloud services, attending promotion activities and lucky draw games. We may record information from user computers and browsers, such as IP address, access browsing histories and the date and time when you log into the system in order to identify some general settings (such as webpage or system language). We also use cookie technology to save user habits and preferences when using our service. This provides the service that users may need. If you choose to refuse all cookies, some services may not function properly on the website.",
    "SH_PrivateMail": "Personal email address",
    "SH_ProbationComplete": "Probation pass",
    "SH_Processing": "In progress",
    "SH_ProcessingDoNotClosePage": "Work in progress. Please do not close this window until complete",
    "SH_ProduceData": "Generate data ",
    "SH_ProfessionalExperienceYears": "Recognized professional experience",
    "SH_ProfessionalSeniority": "Professional Year of Service",
    "SH_ProfitCenter": "Profit center",
    "SH_ProfitCenterCode": "Profit center code",
    "SH_ProfitCenterName": "Profit center name",
    "SH_Project": "Project",
    "SH_ProjectCostCenter": "Project Cost Center",
    "SH_ProjectOrganization": "Project organization",
    "SH_ProjectSupervisor": "Project manager",
    "SH_Promotion": "Promotion",
    "SH_Property": "nature",
    "SH_Proportion": "Pro-rata",
    "SH_ProportionAmount": "Pro-rata/Amount",
    "SH_ProportionOfRecognitionSeniority": "Recognized Year of Service percentage",
    "SH_ProrataPayment": "Pro-rata payment",
    "SH_ProvideCommentToBlowQuestion": "Please provide your comment to below question",
    "SH_ProvidentFund": "Provident fund",
    "SH_ProvideScoreAndComment": "Please provide your score and comment to below question",
    "SH_PushNotification": "Push notification",
    "SH_QualificationRequirement": "Qualification requirement",
    "SH_QueryOrPrint": "Check/Print",
    "SH_Ranking": "Ratings",
    "SH_Rate": "Rate",
    "SH_Rating": "Rating",
    "SH_Read": "Read",
    "SH_ReadAndAccept": "I have read and accept ",
    "SH_Reason": "Reason",
    "SH_ReasonForCancellation": "Cancellation reasons",
    "SH_ReasonRequired": "Rationale is required",
    "SH_Received": "received",
    "SH_Receiver": "Receiver",
    "SH_RecevedRecords": "Received records",
    "SH_RecruitingRequestForm": "Recruiting request form",
    "SH_Recruitment": "Recruitment",
    "SH_ReduceItem": "Deduction",
    "SH_ReduceNote": "exemption remarks",
    "SH_ReduceRate": "exemption rate",
    "SH_ReductionNote": "Reduction remarks",
    "SH_ReductionRate": "Reduction rate",
    "SH_ReferenceData": "Reference data",
    "SH_RegistrationCountry": "Registered of country / area",
    "SH_RegistrationCounty": "Registered of province / city",
    "SH_RegistrationDataHasModified": "{0} registration date has been modified",
    "SH_RegularLeave": "One fixed day off ",
    "SH_RehiringEmployee": "Rehiring Employees",
    "SH_RehiringEmployeeContinueSeniority": "Recognize previous Year of Service for rehire employee",
    "SH_Reinstatement": "reinstatement",
    "SH_ReinstatementDay": "Reinstatement day",
    "SH_Reject": "Decline",
    "SH_Rejected": "Declined",
    "SH_Rejected02": "Declined",
    "SH_RelatedDocument": "Relevant information",
    "SH_Relationship": "Relationship",
    "SH_RelativeName": "Relative Name",
    "SH_RelativeTitle": "Relative title",
    "SH_Release": "Publish",
    "SH_Released": "Released",
    "SH_Releaser": "Release person",
    "SH_ReleaseTime": "Release time",
    "SH_Releasing": "Active",
    "SH_ReliefIdentity": "exemption identity",
    "SH_ReloadPageAndReoperateProcess": "The webpage cache has an error. Please reload the page (F5) and operate the related process again",
    "SH_Remaining": "Remaining",
    "SH_Remark": "Note",
    "SH_RemarkError": "remarks incorrect",
    "SH_Remove": "Remove",
    "SH_Remove02": "Remove",
    "SH_RemoveAll": "Clear all",
    "SH_RemoveReason": "Return note",
    "SH_RentOfDwellingHouse": "Rent of dwelling-house",
    "SH_RentOfDwellingHouseBlank": "rent is blank",
    "SH_RentOfDwellingHouseError": "rent is invalid",
    "SH_Repeated": "repeated",
    "SH_ReportCenter": "Report Center",
    "SH_ReportingUnit": "withholding agencies",
    "SH_ReportUnitAddress": "address of the withholding agencies",
    "SH_ReportUnitName": "Name of the withholding agencies",
    "SH_ReportUnitTaxNumber": "house tax registration number of the withholding agencies",
    "SH_RequestDatetime": "Application date",
    "SH_RequestSubmitTime": "Time for requesting approval",
    "SH_RequestSubmitTime02": "Time for requesting approval : ",
    "SH_RequireCheckInForBreak": "Require check in/out for break",
    "SH_Required": "Required field",
    "SH_ResetPassword": "Reset password",
    "SH_ResidencePermitNumber": "Taiwan Residence Permit",
    "SH_ResidentIdentity": "resident permit",
    "SH_ResignationBilling": "Resignation Settlement",
    "SH_ResponsiblePerson": "Responsible person",
    "SH_RestDay": "flexible rest day",
    "SH_Result": "Result",
    "SH_RetainOldInstitution": "Keep years of service in old system",
    "SH_Return": "Return",
    "SH_Return02": "Return",
    "SH_ReturnForRevision": "Return for revision",
    "SH_Reupload": "re-upload",
    "SH_ReUploadData": "re-upload",
    "SH_RevisedNotRepublished": "Revised but has not republished",
    "SH_RMB": "RMB",
    "SH_RMB02": "RMB",
    "SH_RON": "RON",
    "SH_RoundDown": "Round down",
    "SH_Rounding": "Round",
    "SH_RoundUp": "Round up",
    "SH_RSD": "RSD",
    "SH_RUB": "RUB",
    "SH_RuleName": "Rule Name",
    "SH_RuleNameModify": "Modify rule name",
    "SH_RuleOf": "{0} policy",
    "SH_Salary": "Salary",
    "SH_Salary5%WithholdingRate": "salary : 5% withholding rate",
    "SH_SalaryAccount": "Payroll account",
    "SH_SalaryAccountNumber": "Payroll account",
    "SH_SalaryAllowance": "Salary/allowance",
    "SH_SalaryBankBranchCode": "bank branch code",
    "SH_SalaryBankCodeError": "bank code invalid",
    "SH_SalaryBasicData": "payroll basic information",
    "SH_SalaryChangeRecordCreateFailed": "Payroll 薪資異動紀錄建立失敗。調整薪資範圍，生效日必須為周期第一天",
    "SH_SalaryDetails": "Payroll item",
    "SH_SalaryGroup": "Payroll group",
    "SH_SalaryIncomeWithheldTax": "withholding by standard income tax rate",
    "SH_SalaryRange": "Payment range",
    "SH_SalarySubject": "payroll subject",
    "SH_SalarySubjectCostCenter": "Payroll subject cost center",
    "SH_SalarySubjectCostCenterError": "Payroll subject cost center invalid",
    "SH_SalaryTotalAmount": "payroll amount",
    "SH_SalaryYearAndMonth": "Payroll year/month",
    "SH_SAR": "SAR",
    "SH_Sat": "Sat",
    "SH_Sat02": "Saturday",
    "SH_Save": "Save",
    "SH_SaveSuccess": "Successfully saved",
    "SH_SchoolLocation": "Location of the school",
    "SH_SchoolName": "School",
    "SH_Score": "Scores",
    "SH_Search": "Search",
    "SH_SearchEmployees": "搜尋員工",
    "SH_Searching": "Searching…",
    "SH_SearchKeyword": "Search keyword",
    "SH_SearchLimitedDays": "Search is limited to {0} days",
    "SH_SearchNotification": "Search",
    "SH_SeasonHours": "138 hours in 3 months",
    "SH_SecretaryArea": "Secretary Area",
    "SH_SEK": "SEK",
    "SH_Select": "Select",
    "SH_Select02": "select",
    "SH_SelectAll": "Select all",
    "SH_SelectDateOfBirth": "Select date of birth",
    "SH_SelectDept": "Select department",
    "SH_SelectedChangeActionInconsistent": "The change action has an inconsistent approval process, please revise first before the submission",
    "SH_SelectEmployee": "Select employee",
    "SH_SelectEndDate01": "End date and time",
    "SH_SelectFile": "Select a file",
    "SH_SelectStartDate01": "Start date and time",
    "SH_SelectUnit": "Select unit",
    "SH_SelectUpperCategory": "Select parent level category",
    "SH_Self": "self",
    "SH_SendChanges": "送出修改",
    "SH_Sender": "Sender",
    "SH_SendMail": "Send Email",
    "SH_SendMailWrong": "Send Email Failed",
    "SH_SeniorHighSchool": "Vocational college",
    "SH_SeniorityYearsRange": "0~99.9 years for Service Year Range",
    "SH_SentRecords": "Sent records",
    "SH_Sep": "SEP",
    "SH_SerialNumber": "Number",
    "SH_SeriesNumber": "Series number",
    "SH_ServiceInstitute": "Service institute",
    "SH_SetAuthorizationSuccessfully": "Authorization set successfully",
    "SH_Setting": "Setting",
    "SH_SettingAddedApprovor": "additional approver settings",
    "SH_SettingItem": "Item settings",
    "SH_SettingPayrollPassword": "Set password for payslip",
    "SH_SettlementCategoryError": "settlement data invalid",
    "SH_SettlementDataGenerated": "settlement data created",
    "SH_SettlementDataTypeError": "settlement data type invalid",
    "SH_SettlementHours": "settlement hours",
    "SH_SetToHide": "Hide",
    "SH_SGD": "SGD",
    "SH_Shift": "Work shift",
    "SH_ShiftSupport": "shift support",
    "SH_ShortSickLeave": "Short sick leave",
    "SH_ShowData01": "Display {0} - {1} item(s), total {2} item(s)",
    "SH_Sibling": "Sibling",
    "SH_SickLeave": "Sick Leave",
    "SH_SigningSupervisor": "Signing supervisor",
    "SH_SimplifiedChinese": "Simplified Chinese",
    "SH_Site": "Factory type",
    "SH_SizeLimit50KB": "大小限制為50KB以內",
    "SH_Skills": "Skills",
    "SH_SocialCost": "Social cost",
    "SH_SocialHousingCode": "Code of Social security & Provident fund",
    "SH_SocialSecurity": "Social security",
    "SH_SocialSecurityData": "Social security information",
    "SH_SocialSecurityProvidentFundData": "Social security & Provident fund data",
    "SH_SocialSecurityProvidentFundDataError": "Social security & Provident fund data incorrect",
    "SH_SomebodyHasApproved": "This has been approved",
    "SH_SomebodyPendingApproval": "{0} is pending for approval",
    "SH_SpecialBrowseableList": "Exceptional access list",
    "SH_SpecialExpenseDeductions": "Special expense deductions",
    "SH_SpecialExpenseDeductionsTotal": "Special expense deductions total",
    "SH_SpecialLeaveForm": "Special Leave eForm",
    "SH_SportsAndLeisureStudies": "Sports & leisure",
    "SH_Spouse": "Spouse",
    "SH_SpouseName": "Spouse Name",
    "SH_Stage": "Stage",
    "SH_Standard": "Benchmark",
    "SH_StandardWorkHour": "Standard working hours",
    "SH_StartCounting": "start calculating",
    "SH_StartDate": "Start date",
    "SH_StartingDate": "starting date",
    "SH_StartMonth": "The starting month is",
    "SH_StartOperation": "Start process",
    "SH_StartTime": "Start time",
    "SH_StartUsing": "Click to get started",
    "SH_StationSetting": "站台設定",
    "SH_Status": "Status",
    "SH_Status02": "Status :",
    "SH_StatusCode": "Status code",
    "SH_StatusName": "State name",
    "SH_STAYFUN": "STAYFUN",
    "SH_StillActive": "Still active",
    "SH_StopContribution": "Pension termination",
    "SH_StopInsurance": "Suspend",
    "SH_StopPayment": "Payment end",
    "SH_StudyEndTime": "Study end time",
    "SH_StudyStartTime": "Study start time",
    "SH_StudyType": "Study type",
    "SH_Submit": "Submit",
    "SH_SubmitFailed": "Failed to submit",
    "SH_SubmitRequestForm": "Submit request",
    "SH_SubmitToApprove": "Submit for review",
    "SH_SubordinateEmployee": "Subordinate employees",
    "SH_SubRange": "Subrange",
    "SH_SubsidyNote": "subsity remarks",
    "SH_SubsidyRate": "Subsidy rates",
    "SH_Substitute": "Agent",
    "SH_Subtotal": "Subtotal",
    "SH_SucceedCount": "Successful results：{0}",
    "SH_Success": "Succeed",
    "SH_SuccessfullySubmitted": "Successfully submitted",
    "SH_SuccessfulTransfer": "Transfer successful",
    "SH_SuggestModifyDate": "Recommanded modification date",
    "SH_Sun": "Sun",
    "SH_Sun02": "Sunday",
    "SH_SupervisorFunction": "Supervisor function",
    "SH_SupplementaryFund": "Supplementary Provident Fund",
    "SH_SupplementaryPremiumIdentity": "Supplementary premium identity",
    "SH_SupplementaryPremiumIdentity01": "employee",
    "SH_SupplementaryPremiumIdentity02": "part-time employee",
    "SH_SupplementaryPremiumIdentity03": "Part-time job of underprivileged groups ",
    "SH_SupplementaryPremiumIdentity04": "The fifth type Insured - Low-income households",
    "SH_SupplementaryPremiumIdentity05": "insured identity not qualify",
    "SH_SupplementaryPremiumIdentity06": "The fifth type Insured",
    "SH_SupplementaryPremiumIdentity07": "Professional and technical staff self-employed",
    "SH_SupplementaryPremiumIdentity08": "Self-employed in occupational union",
    "SH_SupplementaryPremiumPartTimeUnit": "unit of part-time income Supplementary premium",
    "SH_Support": "Support",
    "SH_SupportingDocuments": "Supporting documents",
    "SH_SupportRelativesData": "Dependents information",
    "SH_SupportStaff": "Support staff",
    "SH_SupportUnit": "support unit",
    "SH_SwiftCode": "Bank branch code",
    "SH_SwitchCloudLocation": "switch cloud server",
    "SH_Symbol": "symbol",
    "SH_SystemAbnormal": "System abnormal",
    "SH_SystemError": "System is unable to operate, please connect with Apollo service contact (Code {0})",
    "SH_SystemErrorPlsContactTube": "System is unable to operate, please connect with Apollo service contact",
    "SH_SystemOpenFileError": "Upload error, maybe you had change the file password, please change or delete the password and upload again.",
    "SH_TasksLaborContract": "Labor contract - project base",
    "SH_TaxIdNumberIsExist": "tax ID number already exsit",
    "SH_TaxIdNumberLength": "tax ID number must be in 8 numberic characters",
    "SH_TaxingAuthorityCode": "The taxing authority code",
    "SH_TaxpayersIndentity": "Taxpayer's Indentity",
    "SH_TaxpayersIndentityError": "扣繳身分有誤",
    "SH_TaxpayersIndentityRule01": "如為0大陸居民時，只能輸入項目為0專項附加扣除",
    "SH_TaxpayersIndentityRule02": "如為2外籍非居民時，只能輸入項目為外籍津補貼",
    "SH_TaxpayerTaxIDTypeError": "所得人統編類型錯誤",
    "SH_TaxWithholder": "Tax withholder",
    "SH_TaxWithholdingUnitNumber": "The tax identification number for tax withholding agencies",
    "SH_Temp": "Draft",
    "SH_TemplateOption": "Template",
    "SH_Termination": "Resignation",
    "SH_TerminationDate": "Resignation date",
    "SH_TerminationEffectiveDate": "Resignation effective date",
    "SH_TerminationReason": "Resignation reason",
    "SH_TestResult": "verify results",
    "SH_THB": "THB",
    "SH_TheBegining": "From",
    "SH_TheEnd": "end",
    "SH_TheMonth": "Of the month",
    "SH_ThisAccountInvalid": "Account invalid",
    "SH_ThisEmployeeIsExistInStayArea": "Employee already exsit",
    "SH_ThisMonth": "This month",
    "SH_ThreeDecimalPlaces": "Three decimal places",
    "SH_Thrown": "Transfer is completed",
    "SH_Thu": "Thu",
    "SH_Thu02": "Thursday",
    "SH_Time": "Time",
    "SH_TimeCourse": "Period",
    "SH_TimeEnd": "End Time",
    "SH_Timeout": "Passed deadline",
    "SH_TimeSetting": "time setting",
    "SH_TimeStart": "Start Time",
    "SH_Title": "Title",
    "SH_Title02": "title",
    "SH_TitleError": "title invalid",
    "SH_Today": "today",
    "SH_ToDo": "Ad Hoc Work",
    "SH_Topic": "Questions",
    "SH_Total": "Total : ",
    "SH_Total02": "Total",
    "SH_TotalAmount": "Total amount",
    "SH_TotalAmount01": "Amount",
    "SH_TotalAnnualSalary": "Total Annual salary",
    "SH_TotalHourAndMinutes": "{0} hr {1} min",
    "SH_TotalHourAndMinutesForDeductBreakTime": "Break time deduction {0} hr {1} min, apply for {2} hr {3} min in total",
    "SH_TotalHourForDeductBreakTime": "Deduct break time for {0} hr(s), apply for {1} hr(s) in total",
    "SH_TotalHours": "Total hours",
    "SH_TotalNumberOfData": "Total number of data",
    "SH_TotalNumbers": "{0} characters",
    "SH_TotalOvertimePeriod": "Total overtime period",
    "SH_TotalProfessionalSeniority": "Total professional Year of Service",
    "SH_TotalWorkingSeniority": "Total work Year of Service",
    "SH_TotalWorkingYears": "Total service year: {0} years",
    "SH_TownshipArea": "Township area",
    "SH_TraditionalChineseVersion": "Chinese (Traditional) version",
    "SH_TrailCalculateReference": "trail calculate",
    "SH_TrainingAgreement": "Training agreement",
    "SH_Transfer": "Transfer",
    "SH_TrialCalculate": "trail calculate",
    "SH_TRY": "TRY",
    "SH_Tue": "Tue",
    "SH_Tue02": "Tuesday",
    "SH_TurnedOff": "Inactive",
    "SH_TurnedOn": "activated",
    "SH_TurnoverRate": "Analysis of turnover rate",
    "SH_TW": "TW",
    "SH_TwoDecimalPlaces": "Two decimal places",
    "SH_TwoMonthsAgo": "Last two month",
    "SH_Type01": "Type",
    "SH_Type02": "Type : ",
    "SH_TypeBlank": "Type is blank",
    "SH_TypeError": "Type is incorrect",
    "SH_UAH": "UAH",
    "SH_UKD": "GBP",
    "SH_UnableProceedWhenCalculating": "Unable to proceed due to payroll in progress",
    "SH_Unconfirmed": "Unconfirmed",
    "SH_UndeliveredJobNotice": "as a remind that you have not applied yet, you can go to the homepage and check for the job vacancies.",
    "SH_Undone": "Incomplete",
    "SH_UnemploymentInsurance": "Unemployed insurance",
    "SH_Unexecuted": "Action has not processed yet",
    "SH_Unexpired": "Unexpired",
    "SH_UnfixedLaborContract": "Labor contract - no fixed term",
    "SH_UniformSerialNumber": "tax ID Number",
    "SH_UniformSerialNumberError": "GUI number format valid",
    "SH_Unit": "Unit",
    "SH_Unit02": "Unit",
    "SH_Unit03": "Unit: ",
    "SH_Unit04": "unit",
    "SH_UnitAbbreviation": "Unit abbreviation",
    "SH_UnitLevel": "Unit level",
    "SH_UnitLevelTitle": "Unit level name",
    "SH_UnitManager": "Unit manager",
    "SH_UnitManagerId": "Unit manager ID",
    "SH_UnitManagerName": "Unit manager name",
    "SH_UnitName": "Unit name",
    "SH_UnitServiceYear": "Unit service year",
    "SH_UnitType": "Unit type",
    "SH_UnitYOS": "Year of service (unit)",
    "SH_UnitYOSStartDate": "year of service (unit) begin date",
    "SH_Unknown": "Unknown",
    "SH_Unopened": "inactive",
    "SH_UnpaidSickLeave": "Unpaid sick leave",
    "SH_Unread": "Unread",
    "SH_Unsettlement": "Pending",
    "SH_UntimedWorkHour": "Irregular working hours",
    "SH_UnusedLeaveEncashment": "Salary for unused leave",
    "SH_UnusedLeaveHours": "Remaining leave hours",
    "SH_Update": "Update",
    "SH_Updater": "Last updated by",
    "SH_UploadAttachment": "upload attachments",
    "SH_UploadCompanyLogo": "Upload company logo",
    "SH_UploadFailCount": "upload failed counts",
    "SH_UploadFailed": "upload failed",
    "SH_UploadFailReason": "upload failed reason",
    "SH_UploadFile": "File upload",
    "SH_UploadFileFormat": "File types only allow Microsoft Office document, TXT, PDF, compressed file, JPG, JPEG, GIF, PNG",
    "SH_UploadImageFormat": "File types only allow JPG, JPEG, GIF, PNG",
    "SH_UploadResult": "upload result",
    "SH_UploadSuccess": "upload completed",
    "SH_UploadSuccessCount": "upload completed counts",
    "SH_UploadTotalCount": "upload total records",
    "SH_UrgeApprove": "Approval reminder",
    "SH_UrgeApprover": "Approval reminder send by",
    "SH_UrgeApproveSuccess": "Successfully sent out the approval reminder",
    "SH_USAID": "Social Security Number",
    "SH_USD": "USD",
    "SH_USD02": "USD",
    "SH_Used": "Used",
    "SH_UseTemplate": "Applying a template",
    "SH_ValidPeriod": "Valid period",
    "SH_VEF": "VEF",
    "SH_VerificationFailed": "Verification failed",
    "SH_Version": "Version",
    "SH_VideoInterview": "Video interview",
    "SH_View": "View",
    "SH_VND": "VND",
    "SH_WaitEffective": "Pending validation",
    "SH_WaitingForRating": "To be rated",
    "SH_Waitting": "Waiting",
    "SH_WaittingApproval": "Pending eForm for Approval",
    "SH_WaittingConfirm": "Waiting for confirmation",
    "SH_WaittingConfirm02": "Waiting for confirmation : {0} in total",
    "SH_WaittingRelease": "Pending",
    "SH_WaivedReason": "Reason for exemption from military service",
    "SH_WarningFunction": "Reminder function",
    "SH_WarningTime": "Reminder time",
    "SH_Wed": "Wed",
    "SH_Wed02": "Wednesday",
    "SH_WeddingLeave": "Marriage leave",
    "SH_Week": "Week",
    "SH_Weekday": "Weekday",
    "SH_Weekly": "weekly",
    "SH_Welcome": "Hi, {0}",
    "SH_WhetherDeductBenefits": "Deduct welfare funds of not",
    "SH_WhetherSureToCheckIn": "Clock in/out ?",
    "SH_WhetherSureToDelete": "Whether sure to delete?",
    "SH_WhetherSureToExit": "Unsaved data will be lost, Do you want to leave without finishing?",
    "SH_WhetherSureToLeave": "leave this page?",
    "SH_WhetherSureToOverwrite": "overwrite old data",
    "SH_WhetherSureToSendUrgingSignatureMail": "Whether to send a reminder mail?",
    "SH_WhetherSureToUpdate": "Modify or not",
    "SH_WholeNumber": "Whole numbers",
    "SH_WireTransfer": "transfer",
    "SH_Withdraw": "Withdrawn",
    "SH_Withdraw02": "Withdraw",
    "SH_Withdraw03": "Withdraw",
    "SH_WithdrawalApproval": "Withdraw approval",
    "SH_WithdrawalApproval02": "Withdrawal pending for approval",
    "SH_WithdrawalReason01": "Withdrawal reason",
    "SH_WithdrawalReason02": "Withdrawal reason :",
    "SH_WithdrawalReasonRequired": "The withdrawal reason field is required",
    "SH_WithdrawForm": "Withdraw eForm",
    "SH_WithdrawnDay": "insurance withdrawal date",
    "SH_WithholdingItems": "withholding items",
    "SH_WithholdingItemsError": "扣繳項目有誤",
    "SH_WithholdingMethodBlank": "withholding mode is blank",
    "SH_WithholdingMethodError": "withholding mode invalid",
    "SH_WithholdingRateAmount": "withholding ratio / amount",
    "SH_WithholdingRateAmountBlank": "withholding rate / amount is blank",
    "SH_WithholdingRateAmountError": "withholding rate / amount invalid",
    "SH_WithholdingType": "withholding mode",
    "SH_WithholdingTypeIsRequired": "certificate number is 0 or 3，withholding type is required",
    "SH_WorddingLimit": "Maximum {0} characters allowed, {1} characters entered",
    "SH_WorkContents": "Job description",
    "SH_WorkHistory": "Job History",
    "SH_WorkHistorylist": "Job History",
    "SH_WorkHour": "Working hours",
    "SH_WorkingDay": "Working day",
    "SH_WorkingExperienceYears": "Years of work experience",
    "SH_WorkingYears": "year of service",
    "SH_WorkLocation": "Job location",
    "SH_WorkLocationGroup": "Work location group",
    "SH_WorkShift": "Shift schedule",
    "SH_WorkShiftBlank": "Work shift is blank",
    "SH_WorkShiftError": "Work shift is incorrect",
    "SH_WorkTime": "Work time",
    "SH_WorkTimetableConclusion": "Work timetable closing",
    "SH_WorkTimetableForApproval": "Work Timetable for Approval",
    "SH_Year": "Year",
    "SH_Year02": "Year :",
    "SH_Year03": "Year",
    "SH_YearMonth": "{0}/{1}",
    "SH_YearMonth02": "Year/Month",
    "SH_YearOfService": "Year of service",
    "SH_YearOfServiceRecongnition": "Year of Service recognition",
    "SH_Years": "year(s)",
    "SH_YearsOfService": "Years of service",
    "SH_Yes": "Yes",
    "SH_YOSInformation": "Service year Information",
    "SH_YourIPIsNotAuthorizedForUse": "您的IP不在授權使用範圍",
    "SH_YourIPNotInListAllowedByTheCompany": "Your IP address is not in the allowed list. Please use the legal IP address and log in again.",
    "SH_ZAR": "ZAR",
    "zh-tw": "Traditional Chinese",
    "SH_Proportion02": "proportion",
    "SH_DeductionDate": "deduction date",
    "SH_EndDate02": "end date",
    "SH_OneTime": "One Time",
    "SH_PerYear": "Per year",
    "SH_ApplicationsNumber": "申請次數",
    "SH_ApplicationsTime": "申請時間",
    "FD_PlsSelectLeastOneEmergency": "Please select at least one emergency contact",
    "SH_ImportData": "import",
    "SH_CostCenterStatus": "Cost center status",
    "SH_PlsEnterCorrectDateOrUseDatePicker": "Please enter correct date or use datepicker to choose",
    "SH_PlsEnterAvailableDateOrUseDatePicker": "Please enter available date or use datepicker to choose",
    "SH_PlsEnterCorrectMonthOrUseMonthYearPicker": "Please enter correct month or use MonthYearPicker to choose",
    "SH_PlsEnterAvailableMonthOrUseMonthYearPicker": "please enter available month or use MonthYearPicker to choose",
    "SH_PhysicallyChallengedReductionAmount": "Physically challenged reduction amount ",
    "SH_LoanInterestAndRentCanOnlyChooseOne": "Housing loan interest, housing rent can only choose one",
    "SH_SelectPeriod": "Select period",
    "SH_EventName": "Event name",
    "SH_Disaster": "Disaster",
    "SH_Election": "Election",
    "SH_EmployeeLocation": "Employee location",
    "SH_WorkLocation2": "Work location",
    "SH_ContactLocation": "Contact location",
    "SH_ResidentialLocation": "Residential location",
    "SH_InputSelectedEmployee": "Input selected employee",
    "SH_ImportEmployee": "Import employee",
    "SH_CanApply": "Apply",
    "SH_OrgPersonalDataCorrectNoExceptions": "Organization and personal data are correct. No data transfer exceptions",
    "PY_LeaveWithoutPay02": "Leave Without Pay",
    "SH_ExceptionData": "Exception data",
    "SH_SameYearCannotChangeWithholdingItem": "同一年度不得變更扣繳項目",
    "SH_AnticipatedEffectiveDate": "Anticipated effective date",
    "SH_DataTransferExceptionsReport": "Data transfer exceptions report",
    "SH_CannotOperateWhenBatch": "Cannot operate when batch job",
    "SH_ChangeBatchTemplate": "Change Batch Template",
    "SH_WorkLocationGroupCode": "Work location group code",
    "SH_ReasonForWorkLoactionGroupTransaction": "Reason for work loaction group change",
    "SH_EmployeeInactiveOnEffectiveDate": "The employee is inactive on effective date",
    "SH_ChangeExsitOnEffectiveDate": "Change record already exsits on effective date",
    "SH_ChangeOneOption": "Please change at least one option",
    "SH_WorkLocationGroupCodeInvalid": "Work loaction group invalid",
    "SH_CertificateNumberNotMatchIdentityCardType": "證號別與所得人統編類型不符",
    "SH_SubsidyItem": "Subsidy item",
    "SH_ProfoundOrSevere": "Profound or Severe",
    "SH_Moderate": "Moderate",
    "SH_Mild": "Mild",
    "SH_LaborInsuranceOldJobInsurance": "Labor insurance old job insurance",
    "SH_NHISubsidyItem01": "Mild",
    "SH_NHISubsidyItem02": "Moderate",
    "SH_NHISubsidyItem03": "Profound or Severe",
    "SH_NHISubsidyItem04": "Tainan City, over 65 years old, mild physical and mental disorders",
    "SH_NHISubsidyItem05": "Tainan City, over 65 years old, moderately physically and mentally handicapped",
    "SH_NHISubsidyItem06": "Moderate, mild physical and mental disorders in Kaohsiung City",
    "SH_NHISubsidyItem07": "Taipei City, over 65 years old",
    "SH_NHISubsidyItem08": "New Taipei City, over 65 years old",
    "SH_NHISubsidyItem09": "Keelun, over 65 years old",
    "SH_NHISubsidyItem10": "Taoyuan City, over 65 years old",
    "SH_NHISubsidyItem11": "Elderly people over 65 years old in Taichung City",
    "SH_NHISubsidyItem12": "Middle-aged and low-income seniors aged 65 to 69 in specific counties and cities",
    "SH_NHISubsidyItem13": "Middle-aged and low-income seniors over 70 years old",
    "SH_NHISubsidyItem14": "【高市65歲】－高雄市65歲以上老人(近一年申報宗合所得稅率小於20%者)",
    "SH_NHISubsidyItem15": "【高市65歲】－高雄市65歲以上老人(近一年申報綜合所得稅稅率大於20%者)",
    "SH_NHISubsidyItem16": "Elderly people over 65 years old in the outlying islands",
    "SH_NHISubsidyItem17": "Aboriginal people over 55 years old in Taipei",
    "SH_NHISubsidyItem18": "Involuntary Unemployed in Taipei from 20 to 55 years old",
    "SH_NHISubsidyItem19": "Aboriginal people over 55 years old in Keelung",
    "SH_NHISubsidyItem20": "Indigenous people over 55 years old in Taichung City",
    "SH_NHISubsidyItem21": "Receiving unemployed payers and their dependents",
    "SH_NHISubsidyItem22": "Those receiving vocational training living allowances and their dependents",
    "SH_NHISubsidyItem23": "Tobacco health welfare donation subsidy (not exceeding one of the consumption expenditure)",
    "SH_NHISubsidyItem24": "Tobacco health welfare donation subsidy (not exceeding five times the consumption expenditure)",
    "SH_NHISubsidyItem25": "Low-income children under the age of 18 children and teenagers",
    "SH_NHISubsidyItem26": "Vulnerable family aged children and teenagers",
    "SH_NHISubsidyItem27": "Retirees insurance combined with public security for less than 30 years (not paid for pensioners)",
    "SH_NHISubsidyItem28": "Retirees insurance and public insurance for 30 years (not receiving pensioners)",
    "SH_NHISubsidyItem29": "Tainan City, Sinopec pollution, residents of Annan District",
    "SH_NHISubsidyItem30": "Low-income households",
    "SH_NHISubsidyItem31": "Penghu Cancer Population population",
    "SH_PlsSetEffectiveDateGreater": "Please set the effective date to be greater than {0}",
    "SH_PersonalizedSetting": "Personalized setting",
    "SH_IncomeTaxTypes09": "51 Income from Lease",
    "SH_IncomeTaxTypes10": "53 Income from Royalties",
    "SH_IncomeTaxTypes11": "95 Income from government subsidy",
    "SH_IncomeTaxTypes12": "97 Income from Recipient",
    "SH_VietnamIdentity": "Vietnam Identity Card",
    "SH_IndiaIdentity": "India Identity Card",
    "SH_OperationExecutionTime": "Operation execution time",
    "SH_DenominatorCannotEqualZero": "分母不可以等於0",
    "SH_DenominatorCannotSmallerThanNumerator": "分母不可以小於分子",
    "SH_MostAdded": "most {0} times",
    "SH_FileSizeOver": "file size over ",
    "SH_SearchResult": "search result",
    "SH_DepartmentEmployeeList": "people list of organization",
    "SH_UnderOrganization": "affiliated organization",
    "SH_ClosedReason": "closed reason",
    "FD_PersonIsRehired": "This person is a rehired employee. The system will bring the original data.",
    "SH_Retirement": "Retirement",
    "SH_OutOfBusiness": "Stopping",
    "SH_WithdrawalGroup": "Withdrawal",
    "SH_LossNHIPolicy": "喪失全民健康保險投保資格者",
    "SH_ChangeUnit": "Transfer Group insurance applicant",
    "SH_CopyPhoneNumber": "copy phone number",
    "SH_DialOutNumber": "dial",
    "SH_OnlyEnrollmentCanChangeInsuranceUnit": "前一筆為退保時，投保單位才可變更",
    "SH_FirstDataMustBeEnrollment": "無保險資料，第一筆必須為加保",
    "SH_FieldsCannotChangedWhenSurrendering": "異動行為為退保時，其餘欄位不可變動",
    "SH_RateError": "費率有誤",
    "SH_PlsEnterSubsidyRate02": "請輸入補助費率1~100",
    "SH_Display500ItemsMostly": "display 500 items mostly",
    "FD_WhetherIsEmergencyContact": "whether he/she is emergency contact",
    "FD_AtLeastOneEmergencyContact": "at least one emergency contact",
    "SH_WithholdingItemsRule01": "扣繳項目為1/外籍津補貼時不可填寫專項附加扣除金額",
    "FD_NotEnterEmployeesAffiliatedOrgznization": "not enter the data of employee's affiliated organization",
    "SH_CumulativeDeductedMoney": "累計已扣款金額",
    "SH_ClaimRatioType01": "依債權金額比例計算",
    "SH_ClaimRatioType02": "依函文比例計算",
    "SH_AmountDeductedOutsideTheSystem": "於本系統外已扣的金額",
    "SH_TotalAmountOfClaim": "債權總金額",
    "SH_ClaimRatio": "債權比例",
    "SH_LetteClaimRatioTypeMustbeSame": "函文債權比例類型需相同",
    "SH_PersonelInformationList": "Personnel information list",
    "SH_TravelSmartWorking": "Official/Business Trip eForm/Smart Working",
    "SH_US": "US",
    "SH_UK": "UK",
    "SH_VN": "VN",
    "SH_IN": "IN",
    "SH_SG": "SG",
    "SH_DE": "DE",
    "SH_InsuredInformationAlreadyExsit": "Insured information already exsit",
    "SH_ChangeActionOnlyBeOneKind": "Change action only be one kind",
    "SH_ImportFormatInvalid": "import format is invalid, please correct. ",
    "SH_TravelDataSmart": "Official/business trip/smart working information",
    "SH_TravelDataSmartOnly": "Smart working information",
    "FD_CnPolicy01": "隱私權政策",
    "FD_CnPolicy02": "第一部分：前言提示",
    "FD_CnPolicy03": "Apollo服務提供者，即碼優人力資源咨詢（上海）有限公司（或簡稱“我們）制定本隱私權政策（下稱“本政策 /本隱私權政策）， 我們已與貴公司達成服務協議，提供本apollo人力資源平臺服務(下稱“本平臺”) ，我們透過貴公司合法取得之個人信息並深知個人信息對貴公司/個人信息提供者(以下合稱“您”)的重要性，我們將按法律法規要求，采取相應安全保護措施，盡力保護您的個人信息安全可控。鑒於此，向您說明：",
    "FD_CnPolicy04": "為了便於您了解您在使用我們的服務過程中，我們需要收集的信息類型與用途，我們將結合具體服務向您逐一說明。",
    "FD_CnPolicy05": "為了向您提供服務所需，我們會按照合法、正當、必要的原則收集您的信息。",
    "FD_CnPolicy06": "若為了向您提供服務而需要將您的信息共享至第三方，我們將評估該第三方收集信息的合法性、正當性、必要性。我們將要求第三方對您的信息采取保護措施並且嚴格遵守相關法律法規與監管要求。另外，我們會按照法律法規及國家標準，以確認協議、具體場景下的文案確認、彈窗提示等形式征得您的同意或確認第三方已經征得您的同意。",
    "FD_CnPolicy07": "若為了向您提供服務而需要從第三方獲取您的信息，我們將要求該第三方說明信息來源，並要求第三方保障其提供信息的合法性；若我們因開展業務需進行的個人信息處理活動超出您原本向第三方提供個人信息時的授權範圍，我們將征得您的明確同意。",
    "FD_CnPolicy08": "您可以根據以下內容，進一步了解本政策的具體約定：",
    "FD_CnPolicy09": "第二部分：隱私權政策",
    "FD_CnPolicy10": "本隱私權政策部分將幫助您了解以下內容：",
    "FD_CnPolicy11": "一、\t我們如何收集和使用您的信息；",
    "FD_CnPolicy12": "二、我們如何使用Cookie和同類技術；",
    "FD_CnPolicy13": "三、我們如何共享、轉讓、公開披露您的信息",
    "FD_CnPolicy14": "四、我們如何保護您的信息；",
    "FD_CnPolicy15": "五、您如何管理您的信息；",
    "FD_CnPolicy16": "六、我們如何處理未成年人的信息；",
    "FD_CnPolicy17": "七、您的信息如何在全球範圍轉移；",
    "FD_CnPolicy18": "八、本隱私權政策如何更新；",
    "FD_CnPolicy19": "九、如何聯系我們。",
    "FD_CnPolicy20": "為進一步完善您在應征求職過程中的體驗、向您提供更好的人力資源管理服務，在您使用我們的產品及/或服務時，我們需要創建個人賬號，並同時收集和使用的您的個人信息包括如下：",
    "FD_CnPolicy21": "為實現向您提供我們產品或服務的基本功能，您須授權我們收集、使用的必要信息。如您拒絕提供相應信息，您將無法正常使用我們的產品及服務；",
    "FD_CnPolicy22": "為實現向您提供我們產品及/或服務的附加功能，您可選擇授權我們收集、使用的信息。如您拒絕提供，您將無法正常使用相關附加功能或無法達到我們產品及服務的附加功能。",
    "FD_CnPolicy23": "為使用我們的產品及服務，您需提供以下信息：服務範圍之姓名、電子郵箱地址、生物特征(在您使用本平台Lasso AI視頻面試管理功能時所必需收集的個人敏感信息，您可以不提供敏感信息，惟您將無法完整使用Lasso AI視頻面試管理功能)、創建的用戶名和密碼等其他基於使用功能所必需之信息。",
    "FD_CnPolicy24": "您提供的上述信息，將在您使用本服務期間持續授權我們使用。在您註銷賬號時，我們將停止使用並刪除上述信息。",
    "FD_CnPolicy25": "上述信息將存儲於中華人民共和國境內。如需跨境傳輸，我們將單獨征得您的授權同意。",
    "FD_CnPolicy26": "(一)Cookie",
    "FD_CnPolicy27": "為確保網站正常運轉、為了您獲得更輕松的訪問體驗、向您推薦您可能感興趣的內容，我們會在您的計算機或移動設備上存儲Cookie、Flash Cookie，或瀏覽器（或關聯應用程序）提供的其他通常包含標識符、站點名稱以及一些號碼和字符的本地存儲（統稱“Cookie”）。借助於 Cookie，網站能夠存儲您的偏好等數據。",
    "FD_CnPolicy28": "(二)Cookie同類技術",
    "FD_CnPolicy29": "除 Cookie外，我們還會在網站上使用網絡信標和像素標簽等其他同類技術。例如，我們向您發送的電子郵件可能含有鏈接至我們網站內容的點擊URL。如果您點擊該鏈接，我們則會跟蹤此次點擊，幫助我們了解您的產品或服務偏好並改善客戶服務。網絡信標通常是一種嵌入到網站或電子郵件中的透明圖像。借助於電子郵件中的像素標簽，我們能夠獲知電子郵件是否被打開。如果您不希望自己的活動以這種方式被追蹤，則可以隨時從我們的訂閱名單中退訂。",
    "FD_CnPolicy30": "(一)共享",
    "FD_CnPolicy31": "我們不會與任何公司、組織和個人分享您的個人信息，但以下情況除外：",
    "FD_CnPolicy32": "在獲取明確同意的情況下共享：獲得您的明確同意後，我們會與其他方共享您的個人信息。",
    "FD_CnPolicy33": "我們可能會根據法律法規規定，或按政府主管部門的強制性要求， 對外共享您的個人信息",
    "FD_CnPolicy34": "與我們的關聯企業(公司)共享：您的個人信息可能會與我們的關聯企業(公司)共享。我們只會共享必要的個人信息，且受本隱私政策中所聲明目的的約束。關聯企業(公司)如要改變個人信息的處理目的，將再次征求您的授權同意。我們的關聯公司包括：MAYO HUMAN CAPITAL INTERNATIONAL PTE. LTD及鼎恒數位科技股份有限公司。",
    "FD_CnPolicy35": "與授權合作夥伴共享：僅為實現本政策中聲明的目的，我們的部分服務將由授權合作夥伴提供。我們可能會與合作夥伴共享您的部分個人信息，以提供更好的客戶服務和用戶體驗。",
    "FD_CnPolicy36": "我們僅會出於合法、正當、必要、特定、明確的目的共享您的個人信息，並且只會共享提供服務所必要的個人信息。我們的關聯企業(公司)及合作夥伴無權將共享的個人信息用於任何其他用途。",
    "FD_CnPolicy37": "對我們與之共享個人信息的公司、組織和個人，我們會與其簽署嚴格的保密協議，要求他們按照我們的說明、本隱私政策以及其他任何相關的保密和安全措施來處理個人信息。",
    "FD_CnPolicy38": "(二)轉讓",
    "FD_CnPolicy39": "我們不會將您的個人信息轉讓給任何公司、組織和個人，但以下情況除外：",
    "FD_CnPolicy40": "在獲取明確同意的情況下轉讓：獲得您的明確同意後，我們會向其 他方轉讓您的個人信息；",
    "FD_CnPolicy41": "在涉及合並、收購或破產清算時，如涉及到個人信息轉讓，我們會要求新的持有您個人信息的公司、組織繼續受此隱私政策的約束，否則我們將要求該公司、組織重新向您征求授權同意。",
    "FD_CnPolicy42": "(三)公開披露",
    "FD_CnPolicy43": "我們僅會在以下情況下，公開披露您的個人信息：",
    "FD_CnPolicy44": "獲得您明確同意後；",
    "FD_CnPolicy45": "基於法律的披露：在法律、法律程序、訴訟或政府主管部門強制性 要求的情況下，我們可能會公開披露您的個人信息。",
    "FD_CnPolicy46": "(一)我們已采取符合業界標準、合理可行的安全防護措施保護您的信息，防止個人信息遭到未經授權訪問、公開披露、使用、修改、損壞或丟失。例如，在您的瀏覽器與服務器之間交換數據時受SSL協議加密保護；我們同時對網站提供HTTPS協議安全瀏覽方式；我們會使用加密技術提高個人信息的安全性；我們會使用受信賴的保護機制防止個人信息遭到惡意攻擊；我們會部署訪問控制機制，盡力確保只有授權人員才可訪問個人信息；以及我們會舉辦安全和隱私保護培訓課程，加強員工對於保護個人信息重要性的理解。",
    "FD_CnPolicy47": "(二)我們會采取合理可行的措施，盡力避免收集無關的個人信息。我們只會在達成本政策目的所需的期限內保留您的個人信息，除非法律有強制的存留要求；",
    "FD_CnPolicy48": "(三)我們非常重視數據安全，通過相應的安全保護措施保護您的個人數據不被泄漏，但同時也請您註意，在數據網絡上不存在絕對完善的安全措施。如果我們的管理防護設施遭到破壞，導致信息被非授權訪問、公開披露、篡改、或毀壞，導致您的合法權益受損，我們將承擔相應的法律責任。",
    "FD_CnPolicy49": "(四)在不幸發生個人信息安全事件後，我們將按照法律法規的要求，及時向您告知：安全事件的基本情況和可能的影響、我們已采取或將要采取 的處置措施、您可自主防範和降低風險的建議、對您的補救措施等。我們將及時將事件相關情況以郵件、信函、電話、推送通知等方式告知您，難以逐一告知個人信息主體時，我們會采取合理、有效的方式發布公告。",
    "FD_CnPolicy50": "按照中國相關的法律、法規、標準，以及其他國家、地區的通行做法，我們保障您可以通過以下方式訪問及管理您的信息:",
    "FD_CnPolicy51": "(一)查詢、更正和補充您的信息：",
    "FD_CnPolicy52": "您有權查詢、更正或補充您的信息。您可以向客服尋求幫助，協助您查詢、更正或補充您的信息。",
    "FD_CnPolicy53": "(二)刪除您的個人信息：",
    "FD_CnPolicy54": "在以下情形中，您可以向我們提出刪除個人信息的請求：",
    "FD_CnPolicy55": "如果我們處理個人信息的行為違反法律法規；",
    "FD_CnPolicy56": "如果我們收集、使用您的個人信息，卻未征得您的同意；",
    "FD_CnPolicy57": "如果我們處理個人信息的行為違反了與您的約定；",
    "FD_CnPolicy58": "如果您不再使用我們的產品或服務，或您註銷了賬號；",
    "FD_CnPolicy59": "如果我們不再為您提供產品或服務。",
    "FD_CnPolicy60": "若我們決定響應您的刪除請求，我們還將同時通知從我們獲得您個 人信息的實體，要求其及時刪除，除非法律法規另有規定，或這些實體獲得您的獨立授權。",
    "FD_CnPolicy61": "當您從我們的服務中刪除信息後，我們可能不會立即在備份系統中刪除 相應的信息，但會在備份更新時刪除這些信息。",
    "FD_CnPolicy62": "(三)改變您授權同意的範圍：",
    "FD_CnPolicy63": "每個業務功能需要一些基本的個人信息才能得以完成（見本政策“第 一部分”）。對於額外收集的個人信息的收集和使用，您可以隨時給予或收回您的授權同意。",
    "FD_CnPolicy64": "當您收回同意後，我們將不再處理相應的個人信息。但您收回同意的 決定，不會影響此前基於您的授權而開展的個人信息處理。",
    "FD_CnPolicy65": "(四)個人信息主體註銷賬戶：",
    "FD_CnPolicy66": "您可以隨時註銷此前註冊的賬戶，您可以通過我們的客服協助您完成註銷賬戶的程序。",
    "FD_CnPolicy67": "在註銷賬戶之後，我們將停止為您提供產品或服務，並依據您的要求， 刪除您的個人信息，法律法規另有規定的除外。",
    "FD_CnPolicy68": "(五)約束信息系統自動決策：",
    "FD_CnPolicy69": "在某些業務功能中，我們可能僅依據信息系統、算法等在內的非人工自動決策機制做出決定。如果這些決定顯著影響您的合法權益，您有權要 求我們做出解釋，我們也將提供適當的救濟方式。",
    "FD_CnPolicy70": "(六)響應您的上述請求：",
    "FD_CnPolicy71": "為保障安全，您可能需要提供書面請求，或以其他方式證明您的身份。我們可能會先要求您驗證自己的身份，再處理您的請求。",
    "FD_CnPolicy72": "我們將在15天內做出答覆。如您對答覆內容不滿意，可以通過客服發起投訴。",
    "FD_CnPolicy73": "對於您合理的請求，我們原則上不收取費用，但對於多次重覆、超出合 理限度的請求，我們將視情況收取一定成本費用。對於那些無端重覆、需要過多技術手段（例如，需要開發新系統或從根本上改變當前慣例）、給他人合法權益帶來風險或者非常不切實際的請求，我們可能會予以拒絕。",
    "FD_CnPolicy74": "在以下情形中，按照法律法規要求，我們將無法響應您的請求：",
    "FD_CnPolicy75": "與國家安全、國防安全直接相關的；",
    "FD_CnPolicy76": "與公共安全、公共衛生、重大公共利益直接相關的；",
    "FD_CnPolicy77": "與犯罪偵查、起訴、審判和判決執行等直接相關的；",
    "FD_CnPolicy78": "有充分證據表明您存在主觀惡意或濫用權利的；",
    "FD_CnPolicy79": "響應您的請求將導致您或其他個人、組織的合法權益受到嚴重損害的。",
    "FD_CnPolicy80": "涉及商業秘密的。",
    "FD_CnPolicy81": "我們的產品、網站和服務主要面向成年人。如沒有父母或監護人的同意，未成年人不得創建自己的用戶賬戶。",
    "FD_CnPolicy82": "對於經父母同意收集未成年人個人信息的情況，我們只會在受到法律允許、父母或監護人明確同意或者保護兒童所必要的情況下使用或公開披露此信息。",
    "FD_CnPolicy83": "盡管當地法律和習俗對未成年人的定義不同，如我們發現自己在未事先獲得可證實的父母同意的情況下收集了未成年人的個人信息，則會設法盡快刪除相關數據。",
    "FD_CnPolicy84": "原則上，我們在中華人民共和國境內收集和產生的個人信息，將存儲於中華人民共和國境內。",
    "FD_CnPolicy85": "由於我們通過遍布全球的資源和服務器提供產品或服務，這意味著，在獲得您的授權同意後，您的個人信息可能會被轉移到您使用產品或服務所在國家/地區的境外管轄區，或者受到來自這些管轄區的訪問。",
    "FD_CnPolicy86": "此類管轄區可能設有不同的數據保護法，甚至未設立相關法律。在此類情況下，我們會確保您的個人信息得到在中華人民共和國境內足夠同等的保護。例如，我們會請求您對跨境轉移個人信息的同意，或者在跨境數據轉移之前實施數據去標識化等安全舉措。",
    "FD_CnPolicy87": "我們的隱私政策可能變更。",
    "FD_CnPolicy88": "未經您明確同意，我們不會削減您按照本隱私政策所應享有的權利。我們會在本頁面上發布對本政策所做的任何變更。",
    "FD_CnPolicy89": "對於重大變更，我們會提供更為顯著的通知（包括對於某些服務，我們會通過電子郵件發送通知，說明隱私政策的具體變更內容）。",
    "FD_CnPolicy90": "本政策所指的重大变更包括但不限于： ",
    "FD_CnPolicy91": "我們的服務模式發生重大變化。如處理個人信息的目的、處理的個人信息類型、個人信息的使用方式等；",
    "FD_CnPolicy92": "我們在所有權結構、組織架構等方面發生重大變化。如業務調整、破產並購等引起的所有者變更等；",
    "FD_CnPolicy93": "個人信息共享、轉讓或公開披露的主要對象發生變化；",
    "FD_CnPolicy94": "您參與個人信息處理方面的權利及其行使方式發生重大變化；",
    "FD_CnPolicy95": "我們負責處理個人信息安全的責任部門、聯絡方式及投訴渠道發生變化時；",
    "FD_CnPolicy96": "個人信息安全影響評估報告表明存在高風險時。",
    "FD_CnPolicy97": "我們還會將本政策的舊版本存檔，供您查閱。",
    "FD_CnPolicy98": "如果您對本隱私政策有任何疑問、意見或建議，可以通過以下方式與我們聯系，我們將在10天內回覆您的請求：",
    "FD_CnPolicy99": "如對本政策內容有任何疑問、意見或建議，您可通過客服與我們聯系；",
    "FD_CnPolicy100": "我們還設立了個人信息保護專職部門，個人信息提供者可透過貴公司通過 021-51798516 與我們聯系，我們的辦公地址：上海市黃浦區西藏中路168號25樓33室；",
    "FD_CnPolicy101": "如果您對我們的回覆不滿意，特別是您認為我們的個人信息處理行為損害了您的合法權益，您還可以通過向被告所在地有管轄權的法院提起訴訟來尋求解決方案。",
    "SH_CannotFindArea": "no such area ",
    "SH_CannotFindInsuredType": "no such insured classification",
    "SH_SalaryAreaInconsistentWithTemplate": "薪資地區群組與匯入範本不一致",
    "SH_ExpatriateDirectManager": "expatriate direct manager",
    "SH_SecondaryCostCenterIncorrect": "The secondary cost center ratio is incorrect",
    "SH_SecondaryCostCenterCodeBlank": "The secondary cost center code is blank",
    "SH_SecondaryCostCenterRatioBlank": "The secondary cost center ratio is blank",
    "SH_SecondaryCostCenterCodeIncorrect": "The secondary cost center code is incorrect",
    "SH_LaborInsuranceSubsidyItem": "labor insurance - Subsidy item",
    "SH_NHISubsidyItem": "NHI - Subsidy item",
    "SH_PerformanceRecord": "Performance record",
    "SH_PerformancePlan": "Performance Plan",
    "SH_EvaluationDate": "Evaluation Date",
    "SH_EvaluationClassification": "Evaluation Classification",
    "SH_EvaluationUnit": "Evaluation Unit",
    "SH_EvaluationJobTitle": "Evaluation Job Title",
    "SH_EvaluationJobGrade": "Evaluation Job Grade",
    "SH_EvaluationRewards": "Evaluation Rewards",
    "SH_PerformanceAssessment": "Performance Assessment",
    "SH_PerformanceScore": "Performance Score",
    "SH_LaborInsuranceSubsidyItemBlank": "labor insurance - Subsidy item is blank",
    "SH_LaborInsuranceSubsidyItemError": "labor insurance - Subsidy item is required",
    "SH_NHISubsidyItemBlank": "NHI - Subsidy item is blank",
    "SH_NHISubsidyItemError": "NHI - Subsidy item is required",
    "SH_SubsidyItemBlank": "Subsidy item is blank",
    "SH_SubsidyItemError": "Subsidy item is required",
    "FD_EvaluationDateCannotEarlyThanOnBoardDate": "The evaluation date  cannot be earlier than the onboard date.",
    "SH_CorrespondsError": "{0} corresponds to {1} error",
    "SH_CannotLessThenTwoDecimal": "{0} cannot be less than two decimal places",
    "SH_CannotExceedTenNumbericCharacters": "{0} cannot exceed 10 numberic characters",
    "SH_PleaseSelectEndDate": "Please selete end date",
    "SH_BatchDelete": "Batch delete({0})",
    "SH_BatchTermination": "Batch delimit",
    "SH_Once": "one time",
    "SH_Period02": "recurring",
    "SH_End": "Delimit",
    "SH_ImportAdd": "Batch import -add",
    "SH_ImportTermination": "Batch import -delimit",
    "SH_ConvertUnusedLeaveSalaryRate": "Convert the unused leave into salary rate",
    "SH_DetecteUserMobilePhoneTimeSettingError": "Detecte user's mobile phone time setting  is error.",
    "FD_EvaluationUnitCode": "Evaluation Unit code",
    "FD_EvaluationJobTitleCode": "Evaluation Job Title code",
    "FD_EvaluationJobGradeCode": "Evaluation Job Grade code",
    "FD_PositionData": "Position Data",
    "FD_SigningForm": "Signing Form",
    "FD_EmployeeStillExpatriate01": "Gently reminder,the employee is still during expatriate. Are you sure to send it?",
    "FD_EmployeeStillExpatriate02": "Gently reminder,the employee is still during expatriate.Please make adjustments before making changes. ",
    "FD_NotAttendDuringExpatriatePeriod": "*If you did not attend during expatriate period, please process leave without pay calculation on attendance module.",
    "FD_NotProcessPayrollDuringExpatriatePeriod": "*If you did not process payroll during expatriate period, please stop payment on payroll module.",
    "FD_AddEmployeeChange": "Add employee change",
    "FD_AddExpatriateChange": "Add Expatriate change",
    "FD_ChangeType": "Change type",
    "FD_EmployeeChange": "Employee change",
    "FD_ExpatriateChange": "Expatriate change",
    "FD_ExpatriateStart": "expatriate start",
    "FD_ExpatriateEnd": "expatriate end",
    "FD_ExpatriatePeriodAdjustment": "expatriate end date adjustment",
    "SH_MonthlyEmployeeOnlyAllowImportMonthlySalary": "Employee type in Monthly only allow to import monthly salary",
    "FD_CannotAddExpatriateChangeWhenTermination": "This employee status is resignation, you cannot add Expatriate change.",
    "FD_CannotAddExpatriateChangeWhenNotStart": "Because of the employee doesnot start to expatriate, you cannot add expatriate change",
    "FD_EffectiveDateNotLaterLatestChange": "The date is not later than the latest change record",
    "FD_ExpatriateDepartmentIsOrWillBeExpired": "The expatriate unit has been expired or will be expired",
    "FD_ExpatriateManagerPositionNumber": "expatriate manager job title",
    "SH_NHIDependentsSubsidyRateBlank": "NHI dependents- subsidy rate is blank",
    "SH_NHIDependentsSubsidyRateInvalid": "NHI dependents- subsidy rate invalid",
    "SH_NHIDependentsMaxSubsidyAmountBlank": "NHI dependents- Maximum subsidy amount is blank",
    "SH_NHIDependentsMaxSubsidyAmountInvalid": "NHI dependents- Maximum subsidy amount invalid",
    "SH_AmountInputLimit": "{0} cannot exceed 9 numberic characters and cannot be less than two decimal places.",
    "FD_PerformanceBatchImport": "Performance Batch Import",
    "SH_GDPRRegulations01": "*In response to GDPR.If you are a EU resident,you cannot have FACEPASS service.",
    "SH_GDPRRegulations02": "*In response to GDPR. If the candidate is a EU resident,the can't have facepass service.Please don't s send Lasso interview invitation.",
    "SH_GDPRRegulations03": "*In response to GDPR.If you patch import the data which incloude EU resident of 15~16 years,please have agreement from legal representative by law.",
    "SH_GDPRRegulations04": "*In response to GDPR.If you are a EU resident of 15~16 years,please have agreement from legal representative by law.",
    "SH_GDPRRegulations05": "*In response to GDPR.If you input the data which is about EU resident of 15~16 years,please have agreement from legal representative.",
    "SH_GDPRRegulations07": "*In response to GDPR.If you add the dependents info for income tax is EU resident, you should not collect the data which is under 13 years children.If the EU resident data is 13~16 years,please have agreement from legal representative.",
    "SH_GDPRRegulations08": "*In response to GDPR.If you add the family member is EU resident, you should not collect the data which is under 13 years children.If the EU resident data is 13~16 years,please have agreement from legal representative.",
    "SH_GDPRRegulations09": "*In response to GDPR.If you add the NHI dependents is EU resident, you should not collect the data which is under 13 years children.If the EU resident data is 13~16 years,please have agreement from legal representative.",
    "SH_GDPRRegulations10": "*In response to GDPR.If you add the emergency contact is EU resident, you should not collect the data which is under 13 years children.If the EU resident data is 13~16 years,please have agreement from legal representative.",
    "SH_OneDecimalPlace02": "one decimal place",
    "SH_CannotEarlierThan": "{0} cannot be earlier than {1}",
    "SH_ExpatriateUnitManager": "expatriate unit manager",
    "SH_ExpatriateAnticipatedEndDate": "expatriate anticipated end date",
    "SH_EmployeeChangeForm": "employee change form",
    "SH_ExpatriateChangeForm": "expatriate change form",
    "SH_EmployeeInformation": "employee information",
    "SH_AttachmentErrorPlsReupload": "The attachment is error, please reupload.",
    "SH_PayrollChangeRecord": "payroll change record ",
    "SH_SubjectName": "subject name",
    "SH_BaseSalaryTotal": "base salary accumulation",
    "SH_FixedAllowanceTotal": "fixed allowance accumulation",
    "SH_SalaryTotal": "payroll accumulation",
    "SH_BonusPaymentRecord": "bonus payment record",
    "SH_PaymentPeriod": "payment period",
    "SH_BonusTotal": "bonus accumulation",
    "SH_ContactInfo": "contact information",
    "SH_PersonalPayroll": "personal payroll ",
    "SH_ExpatriatePayroll": "expatriate payroll",
    "FD_EmployeePerformanceReport": "Employee performance report",
    "FD_PerformanceBatchTemplate": "Performance batch import template",
    "FD_PerformanceBatchExceptionDescription": "Performance batch import exception description",
    "SH_DataSuccessfullyImported": "The data has successfully imported",
    "SH_OnlyIsAbleToBeDelete": "僅列出可刪除之資料",
    "SH_ConvertUnusedLeaveSalaryRateIsIncorrectFormat": "Convert the unused leave into salary rate is in an incorrect format",
    "SH_OverallOvertime": "Overall overtime",
    "SH_EmployeeNotExpatriate": "The employee is not stationed, no such information",
    "SH_ConfirmActivate": "Are you sure you want to activate?",
    "SH_ConfirmDeactivate": "Are you sure you want to deactivate?",
    "SH_ApolloPolicy01": "You are welcome to use the Apollo comprehensive human resource solution (hereinafter to be referred to as “the service”).  The service is provided by MAYO Human Capital Inc. (hereinafter to be referred to as “the company”).  To further improve customers (your employer) experience and provide you with a better service on human resource management, the company will collect the necessary data through the personal data provided by your employer during the process of the service.  Through data analysis and value-added, the company can provide you with more advance and intimate services.  To ensure the security of your data, privacy, and personal rights provided by your employer and help you to understand your rights on how our company collects, processes, and uses the personal data, please read this privacy policy in detail.",
    "SH_ApolloPolicy02": "1.Application Scope",
    "SH_ApolloPolicy03": "1.1. During the usage of the service, the collection, processing, and use of the personal data involved are protected by this privacy policy.  Base on the protection of personal rights and interests, our service will collect, process, and use personal data in an honest and good-faith manner, shall not exceed the necessary scope of specific purposes, and shall have legitimate and reasonable connections with the purposes of collection.",
    "SH_ApolloPolicy04": "1.2. This privacy policy only applies to the service and the personal data entrusted or involved in the collection, processing, and use within the service.  Any personal data that are not entrusted or involved in the management of the service do not apply to the provision of this privacy policy.",
    "SH_ApolloPolicy05": "1.3. If you refuse to provide the necessary information for the service, it may result in the inability to enjoy the full service or unable to use the service.",
    "SH_ApolloPolicy06": "2. Notification of collection, processing, and use of personal data",
    "SH_ApolloPolicy07": "To ensure the security of personal data and privacy, the company will inform the following under the provisions of article 9 from the Personal Data Protection Act:",
    "SH_ApolloPolicy08": "2.1. Collecting subject: MAYO Digital Technology Co., Ltd (MAYO HUMAN CAPITAL INC.)",
    "SH_ApolloPolicy09": "2.2. Purpose of collection: The purpose of the company’s data collection includes contracts, similar contracts, STAYFUN employee welfare integration platform (hereinafter to be referred to as “STAYFUN platform”) or other e-commerce services (if any) for statistical and research analysis.  In addition, other operations combined with business registration or organization are also included in the collecting purpose to provide a better quality of the service.",
    "SH_ApolloPolicy10": "2.3.    Types of personal data gathered: In accordance with \"The specific purpose and the classification of personal information of the Personal Data Protection Act\" issued by Ministry of Justice, categories of personal data to be collected: categories of personal data your company will provide include following categories are:\"001 Life and Health Insurance, 002 Human Resource Management (including recruitment, separation, employee profile, current position, education, working experience, examination distribution, long term learning, training and development, performance management, Verify the qualification, compensation, \n\nattendance record, benefit, Deprived of civil rights, special check \n\n(background), other personnel management.), C001 Type for identifying individuals (For example: Name, title, address, office address, previous address, home telephone number, cell phone number, messenger account, internet web signing up account, postal and resident address, photograph, fingerprint, the E-mail address, digital signature, serial number of certificate card, certificate serial number, record providing internet identity authentication or applying inquiry service record as well as any other data which can identify the individual.), C002 Type for identifying finance (For example: The number and name for financial institutions’ account, the number of credit card or VISA card, the number of insurance policy and any other number or account of individual, etc.), C003 Type for identifying in government data (For example: The number of personal ID card, certificate identity number, tax registration numbers, the number of Insurance certificate, the disabled manual number, the number of retirement certificate and the number of passport, etc.), C011 Individual description (For example: Age, gender, date of birth, native place, nationality and voice, etc.), C012 Physical description \n\n(For example: Height, weight and blood type, etc.), C021, C023, C031, C033, C034, C038, C051 Schools record (For example: Universities, colleges or other schools, etc.), C052 Qualification or technique (For example: Qualification for diploma, professional technique, special license (such as pilot's license), governmental vocational training process, national examination, grades and other training record, etc.), C054 Occupation expertise (For example: Expert, scholar and adviser, etc.), C061 Current status of employment (For example: Employer, title in the work place, the description about work, ranking, the date of employment, working hour, the working place, character of industry, the condition and period being offered, the previous liabilities and experiences related to the existing employer, etc.), C062 Employment experiences (For example: The date of employment, the way of being employed, employed through intermediary/referral and the period of employment, etc.), C063 Leave office (For example: The date of leaving office and it's reason, notice and condition for leaving office, etc.), C064 Working experiences. (For example: Previous employer and work, the period of redundancy and in military service, etc.), C065 Working or missions record (For example: Clock in and out, personal leave, sick leave, furlough, maternity leave or any related record, the working record, the reason of unemployment, performance appraisal records, records of reward and punishment, information of deprivation of civil right, etc.), C068 Salaries and withholding amount (For example: Salary, wages, commission, bonus, expenses, allowance, welfare, borrowed cash, tax-paying, the deduction of pension, admission fee for the labor union, the minimum wage, the way which the wage are paid in, and date of being given a raise, etc.), C072 Educational and training record. (For example: Compulsory education and training concerning work, education and training already participated, qualifications or technique which already have ,etc.), C081 Earning, income, property and investment. (For example: Total wages, total incomes, earned wage, earned income, capital and asset, savings, the start date and due date, the earning of investment, the income of investment and the expenses of property, etc.) to be used within the scope of each specific purpose. If your employer uses the RMS module, you must collect the category starting with C111 Record of health (For example: Medical reports, record of treatment and diagnosis, result of examination, handicapped type, degree, validity period, identity number and contactor, etc.).  ",
    "SH_ApolloPolicy11": "2.4. Period, region, object, and method of using personal data:",
    "SH_ApolloPolicy12": "2.4.1. Period: The duration within the contract authorization between your employer and the company.",
    "SH_ApolloPolicy13": "2.4.2. Region: Personal data will be used in Taiwan and the region where our server and backup server, and cloud server are located.",
    "SH_ApolloPolicy14": "2.4.3. Counterparty and methods of use: Your data will only be processed and used electronic document, or paper documents form by the company and its subsidiaries within the necessary scope.  Your data will not be provided to any unrelated third party.",
    "SH_ApolloPolicy15": "3. The scope of the service to collect, process, and use on personal data",
    "SH_ApolloPolicy16": "3.1. In the process of using the service, the server may record data from the terminal device and browser.  Includes information such as device location, IP address, domain name, operation log, and various types of data that you actively enter or upload.",
    "SH_ApolloPolicy17": "3.2. The company uses an HTTP cookie to record your habits and preference while using the service and is used to initialize other services correlated.  If you cancel the cookie function within your browser, you will not be able to use the full functionality of the service.",
    "SH_ApolloPolicy18": "3.3. When you contact the company, the company will record your call records, content, contact information, and related problem-solving solutions and results in order to contact you or help you solve the problem.",
    "SH_ApolloPolicy19": "3.4. The company can conduct data analysis and research on the obtained user data to provide better services.",
    "SH_ApolloPolicy20": "3.5. The service is based on the local personal data protection act and will not collect, store, process, and use particular personal data such as medical records, medical treatment, genetics, sex life, health checks, criminal records, politics, religion, belief, biological characteristic, and minor information, etc.  Unless the company been authorized with the written consent from your employer, the service will not collect data violating the law.",
    "SH_ApolloPolicy21": "3.6. Third-Party Applications ",
    "SH_ApolloPolicy22": "3.6.1 Collection and Use of Your Personal Data ",
    "SH_ApolloPolicy23": "The service will only collect and utilize the contents of third-party applications as follows: ",
    "SH_ApolloPolicy24": "3.6.1.1 Google Calendar: We will update your calendar data through Google Calendar. ",
    "SH_ApolloPolicy25": "3.6.1.2 Google OAuth 2.0: We will allow you to use your Google account for authentication to access the service through the Google OAuth 2.0 API after your consent. We will obtain your personal data such as your calendar only with your consent. We do not store your password, and we do not collect or use your personal data beyond the scope of the service's functionality.",
    "SH_ApolloPolicy26": "The service's use and transfer to any other app of information received from Google APIs will adhere to {0}, including the Limited Use requirements. ",
    "SH_ApolloPolicy27": "3.6.2 Processing of Your Personal Data ",
    "SH_ApolloPolicy28": "The company will implement appropriate security measures to protect your personal data stored in the service from theft, alteration, damage, loss, or disclosure, which originates from your Google account. ",
    "SH_ApolloPolicy29": "3.7. The service will not disclose or share your user information with third parties without authorization from your employers, except under the following circumstances:",
    "SH_ApolloPolicy30": "3.7.1. Related to national security and national defense;",
    "SH_ApolloPolicy31": "3.7.2. Related to public safety, public health, and major public interests;",
    "SH_ApolloPolicy32": "3.7.3. Related to the criminal investigation, prosecution, trial, and enforcement of the jud.gment, etc.;",
    "SH_ApolloPolicy33": "3.7.4. Out of the protection of the significant legal rights and interests of the personal data or other individuals’ lives and property, but it is difficult to obtain your employer’s consent;",
    "SH_ApolloPolicy34": "3.7.5. The personal data collected is disclosed to the public by yourself;",
    "SH_ApolloPolicy35": "3.7.6. The personal data collected from legal public information, such as legal news reports, government public information, and other channels;",
    "SH_ApolloPolicy36": "3.7.7. Obtain the explicit consent from your employer;",
    "SH_ApolloPolicy37": "3.7.8. During the event of merger, acquisition, bankruptcy, liquidation of the company, or other situation related, if the service involves the transfer of personal data, the company will require the new company or organization which holds your personal data continue to follow this privacy policy.  Otherwise, the company will require the new company or organization to seek authorization from your employer.",
    "SH_ApolloPolicy38": "3.8. For companies or organizations which share their personal data with the company, the company will sign strict data protection agreements with them, requiring them to process data under this privacy policy and any other related confidentially and security measures.",
    "SH_ApolloPolicy39": "3.9. When your employer cancels the Apollo service, you can choose to delete or transfer all your personal data.  After you delete the data from the company’s services, the company may not immediately delete the corresponding data from the backup system but will ensure to delete the data when the backup is updated.",
    "SH_ApolloPolicy40": "3.10. If the company stops operating Apollo services, the company will immediately stop collecting your personal information and notify your employer of the cessation of operations in the form of an announcement. Based on subsequent service needs, the company will obtain your or your employer's consent. The data will be retained for 3 months, after which the personal data held will be deleted or de-identified.",
    "SH_ApolloPolicy41": "4. Data Security",
    "SH_ApolloPolicy42": "4.1. The company has taken reasonable and practicable safety protection measures that meet industry standards to protect the security of the personal data you provide and prevent personal data from unauthorized access, public disclosure, use, modification, damage, or loss.  For instance, when exchanging data between your browser and server, it is encrypted and protected by SSL (Secure Socket Layer) protocol; encapsulated and secured in the App of the terminal device; the company also provides HTTPS (Hyper Text Transfer Protocol over Secure Socket Layer) safe browsing protocol to Apollo; the company will use encryption and masking technology to improve the security of personal data; the company will deploy an access control mechanism to ensure that only authorized personnel can access personal data; the company will organize security and privacy protection training courses to strengthen employees’ awareness of the importance of protecting personal data.",
    "SH_ApolloPolicy43": "4.2. The company attaches importance to data security.  Through corresponding security measures, the company secures your personal data from leaking out.  However, please note that there are no perfect security measures on the data network.  If unfortunately, a security incident that involves personal data has appeared, within 72 hours of accordance with the requirements of law and regulation the company will notify your employer by email, letter, telephone, push notification, and official website announcement, etc.",
    "SH_ApolloPolicy44": "5. Cross-border Transmission",
    "SH_ApolloPolicy45": "With the consent authorized by your employer, the company will comply with the local Personal Data Protection Act and transfer the collected personal data from overseas (such as the European Economic Area) to the company’s cloud platform service providers and partners.",
    "SH_ApolloPolicy46": "6. Quick Link to STAYFUN",
    "SH_ApolloPolicy47": "The service also provides a quick link to facilitate your access to the STAYFUN platform.  However, the privacy policy of the service does not apply to the STAYFUN platform.  You should review the privacy policy of the STAYFUN platform to ensure your rights.",
    "SH_ApolloPolicy48": "7. Cross-border Rights",
    "SH_ApolloPolicy49": "7.1. If you are an EU citizen or your personal data belongs to the EU, you have the right to request access, amendment, forgetting, erasure, restriction of processing, and objection on processing or transfer your data (portability) from your employer in accordance with the “General Data Protection Regulation” (GDPR).  To learn more about these rights, please visit the GDPR page of the European Commission.",
    "SH_ApolloPolicy50": "7.2. If your personal data does not belong and managed by the EU, the rights will be exercised in accordance with the local laws.",
    "SH_ApolloPolicy51": "7.3. If you have any concerns about your privacy, please contact the human resources department of your employer.",
    "SH_ApolloPolicy52": "8. How the privacy policy of the service will be update",
    "SH_ApolloPolicy53": "8.1. In order to adapt to changes in this service, improve with the opinions of customers, or for any other purpose, the company has the right to modify this privacy policy at any time.  The company will publish information about all the changes in the privacy policy on a dedicated page (privacy policy).  For major changes, the company will provide further notice (by e-mail or through other methods).",
    "SH_ApolloPolicy54": "8.2. If you have any questions or concerns about the privacy policy or data processing method of the service, please contact us.",
    "SH_ApolloPolicy55": "9. Your rights ",
    "SH_ApolloPolicy56": "In accordance with the provisions of the Personal Data Protection Law, you may exercise the following rights in relation to the relevant personal data: ",
    "SH_ApolloPolicy57": "9.1 The company may inquire, request to read or request to make copies, and the company may charge necessary costs according to law. ",
    "SH_ApolloPolicy58": "9.2 You may request supplements or corrections from the company, but you and the client (your employer) should provide equivalent information for explanation. ",
    "SH_ApolloPolicy59": "9.3 The client (your employer) may request the company to stop the collection, processing or use and request deletion. ",
    "SH_ApolloPolicy60": "Last update date: 2024/04/11",
    "PT_AllowArrangeWorkingDaySetting": "允許排工作日設定",
    "SH_EffectiveEndCannotBeEarlierThanStart": "Effective end date should not precede the effective start date. ",
    "SH_RatioMustMoreThan0": "The ratio must more than 0",
    "SH_BatchEmployeeChangeTemplate": "Batched employee change template",
    "SH_BatchEmployeeChangeError": "Batched employee change",
    "SH_BatchExpatriateChangeTemplate": "Batched expatriate change template",
    "SH_BatchExpatriateChangeError": "Batched expatriate change",
    "SH_BatchEmployeeChange": "Batched employee change",
    "SH_BatchExpatriateChange": "Batched expatriate change",
    "SH_NotAttendDuringExpatriatePeriod": "If you did not attend during expatriate period, please process leave without pay calculation on attendance module.",
    "SH_NotProcessPayrollDuringExpatriatePeriod": "If you did not process payroll during expatriate period, please stop payment on payroll module.",
    "SH_ExpatriateEndDateEqualLastWorkingDay": "If the change behavior :expatriate end date equal the last working day of expatriate duration , add new change eform?",
    "SH_AnticipateResignationEffectiveDate": "Anticipate resignation effective date ",
    "SH_ChangeAnticipatedEndDate": "Change anticipated end date",
    "SH_AfterModifyLeaveWithoutPayPeriod": "After modify leave without pay period",
    "PY_AddToPerferingList": "add to list",
    "PY_PerferingList": "perfering list",
    "SH_PlsChangeYourPasswordOrNotifyAccountManager": "If it is not you, please change your password or notify the company account manager as soon as possible.",
    "FD_PrimaryCostCenterCannotEqualSecondary": "Primary Cost Center and Secondary cost center can not be the same.",
    "FD_SecondaryCostCenterCannotRepeat": "Secondary cost center cannot choose the same item.",
    "FD_SecondaryCostCenterCannotBeZeroRatio": "Secondary cost center cannot be zero ratio.",
    "SH_CostCenterCodeCannotRepeat": "No multiple settings for same cost center code.",
    "SH_CostCenterRatioCannotBeZero": "Cost center ratio should not be zero.",
    "SH_AffiliationOrexpatriate": "所屬/派駐",
    "SH_PlsFillEmployeeData": "Please fill in the employee data ",
    "SH_EmployeeData02": "employee data",
    "SH_ChangeHistory03": "change history",
    "SH_CodeAlreadyExists": "The code already exists",
    "SH_PlsFillInRequireField": "please fill in require field",
    "SH_RoleCode": "Role code",
    "SH_RoleName": "Role name",
    "SH_RoleType": "Role type",
    "SH_RoleDescription": "Role description",
    "SH_Function02": "Function",
    "SH_SearchKeyword02": "Search keyword",
    "SH_RoleSetting": "Role setting",
    "SH_CreateRole": "Create a role",
    "SH_FunctionPermissionSetting": "Fuction permission setting",
    "SH_ProgressSearch": "Progress search",
    "SH_FillRoleInfo": "Type role inform",
    "SH_AddNewEmployee": "Add new people",
    "SH_DataPermission": "Data permission",
    "SH_ReviewCreate": "Review/Create",
    "SH_DisplaySelectedPeople": "Display selected people({0})",
    "SH_PlsSelectCorp": "Please select legal entity",
    "SH_PlsSelectUnit": "Please select unit",
    "SH_PlsFillEmployeeNumberOrName": "Please input ID or name",
    "SH_PlsSearchEmployeeNumberNameKeyword": "Please search related ID/name or other keyword ",
    "SH_ColumnDisplayFunction": "The function of column display",
    "SH_ModuleFunction": "Module function",
    "SH_FunctionOperation": "Function operation",
    "SH_FunctionDescription": "Function description",
    "SH_PlsCheckRolePermissionRange": "Please tick the role permission of range",
    "SH_RolePermissionRange": "The role permission of range",
    "SH_FunctionDataPermission": "The function permission",
    "SH_AllData": "All data",
    "SH_AffiliationCorp": "Affiliated legal entity",
    "SH_AffiliationManagerUnit": "Affiliated manager's unit",
    "SH_ManagerAndSubordinateUnits": "Manager and his subordinate units",
    "SH_EmployeeHimself": "Employee himself",
    "SH_Selected": "Selected({0})",
    "SH_PlsSelectFunction": "Please choose function",
    "SH_PermissionFunction": "Permssion function",
    "SH_DataPermissionRange": "Data permission range",
    "SH_AreYouSureDeleteInform01": "Are you sure to delete{0}inform",
    "SH_CopyRoleInformation": "Copy role information",
    "SH_AreYouSureDeleteInform02": "Are you sure to delete inform",
    "SH_Role": "Role:",
    "SH_IncludeDataPermission": "Include data permission",
    "SH_PlatformSettings": "Platform settings",
    "SH_AddPermission": "Add permission",
    "SH_AddRoleForEmployee": "Add role for the people",
    "SH_ChangesNotSaved": "Changes you made have not been saved, do you want to save it?",
    "SH_WhetherSureToDeleteTheFollowNumData": "Are you sure you want to delete the following {0} piece of data?",
    "SH_ReasonForDelete": "Reason for delete",
    "SH_PlsFillReasonForDelete": "Please fill in the reason for delete",
    "SH_WhetherSureToDeleteTheFollowData": "Are you sure you want to delete the following data?",
    "SH_PlsFillFormData": "Please fill in the following information",
    "SH_ReselectFile": "Reselect the file",
    "SH_PleaseEnterThe": "Please enter the {0}",
    "SH_PleaseSelectThe": "Please select the {0}",
    "SH_PieceOfData": "({0}) piece of data",
    "SH_FileName": "filename",
    "SH_DomainName": "About the domain name, please ask for the IT employee of your company.",
    "SH_ResetPassword02": "Please enter your Company ID and Employee ID, we will send the link for changing your password to your email. If you have any problem, please ask for the HR department.",
    "SH_PasswordChangedPlsLoginAgain": "Your password has been changed, please login again.",
    "SH_EnterYourPassword": "Enter your password.",
    "SH_ForgotHRSystemPassword": "Forgot HR system's password.",
    "FD_ReturningNoServiceRecognition": "Returning-no service recognition",
    "FD_ReturningServiceRecognition": "Returning-service recognition",
    "SH_DownloadAPP": "Download APP",
    "SH_DeleteAll2": "delete all",
    "SH_PayRetroavtiveForLeaveWithdrawal": "Pay retroactive for leave withdrawal",
    "SH_RatioOfPayRetroactive": "The ratio of pay retroactive",
    "SH_FormApplied": "Applied",
    "SH_FormApproved": "Approved",
    "SH_FormApproved2": "Approved",
    "SH_FormEffective": "Effective",
    "SH_FormRejected": "Rejected",
    "SH_AttendanceBonus": "Attendance bonus",
    "SH_BonusSubject": "Bonus subject",
    "SH_BonusRule": "Bonus rule",
    "SH_EmployedDay": "Employed days",
    "SH_CycleDay": "Cycle days",
    "PT_ActualAmount": "Net pay",
    "SH_BonusRuleSetting": "Bonus rule setting",
    "SH_AddRule": "Add rule",
    "SH_AddAccount": "Add subject",
    "SH_ConditionSetting": "Condition setting",
    "SH_SubjectCode": "Subject code",
    "SH_SubjectName2": "Subject name",
    "SH_RuleCode": "Rule code",
    "SH_RuleName2": "Rule name",
    "SH_WhetherSureApplySettingFollowingData": "Are you sure you want to apply the setting for the following {0} piece of data?",
    "SH_AppliedRule": "Applied rule",
    "SH_BonusPayment": "Bonus payment",
    "SH_PlsFillRalatedData": "Please fill in the following information",
    "SH_ClickDownloadAPP": "Click & download APP",
    "SH_ApolloVideoInterview": "Apollo video interview",
    "SH_ExpatriateTo2": "(expatriate to {0}/{1})",
    "SH_ExcuteSuccess": "Success",
    "SH_EmployeeLeaveRuturn": "The employee is leave and return on the same date",
    "SH_IosNotSuppotFacePass": "iOS not support FACEPASS",
    "SH_ChangesNotSaved02": "The data which you edit is not stored, if you store it and check the next one.",
    "SH_DiscardStorage": "Discard storage",
    "SH_ExpatriatedCountryArea": "Expatriated(county/area)",
    "SH_ExpatriatedProvinceCity": "Expatriated(province/city)",
    "SH_ExpatriatedWorklocation": "Expatriated work location",
    "FD_SimpleActiveEmployeeList": "Simple active employee list",
    "FD_TheEmployeeListOnProbationExceptLeave": "The employee list on probation except leave",
    "FD_FutureOfEffectUnitChangeReport": "Future of effect unit change report",
    "SH_ThroughFormCenterToCheckAttachment": "Through form \"Query\" to check attachment",
    "SH_Remind": "Remind",
    "SH_NotEffective": "Not effective",
    "SH_ManagementLayer": "Management layer",
    "SH_SpecialIdenty": "Special identity",
    "SH_UsFlsaType": "US FLSA Type",
    "SH_PeoplePermissionQuery": "People permission query",
    "SH_RoleRoleInformation": "Role information",
    "SH_FunctionFunctionPermission": "Function permission",
    "SH_RoleInformationPermission": "Role information permission",
    "SH_AddInformationPermission": "Add function information permission",
    "SH_FunctionFunctionName": "Function name",
    "SH_ChooseChooseRole": "Choose Role",
    "FD_InvoluntarySeparation": "Involuntary separation",
    "SH_ShutDown": "Shut down",
    "SH_Relocation": "Relocation",
    "SH_Closed04": "Closed",
    "SH_Dissolution": "Dissolution",
    "SH_Bankruptcy": "Bankruptcy",
    "SH_LaborStandardsAct": "Labor Standards Act {0}",
    "SH_Act": "{0}",
    "FD_FixedTermContractFromTo": "Fixed term contract( From{0}/{1}/{2} to {3}/{4}/{5})",
    "SH_OtherReason": "Other reason",
    "SH_LaborStandardsAct13": "Labor Standards Act 13 proviso",
    "SH_RoleCanNotDelete": "The role can't delete brcause of system established",
    "SH_DisplaySelectedLegalEntity": "Display selected legal entity({0})",
    "SH_DisplaySelectedUnit": "Display selected unit({0})",
    "SH_Items2": "Item{0}",
    "SH_Department2": "DEPARTMENT",
    "SH_EmployeeNo2": "EMPLOYEE NO.",
    "SH_RequestedFor2": "REQUESTED FOR.",
    "SH_TheAboveInformation": "THE ABOVE INFORMATION IS TRUE TO OUR KNOWLEDGE",
    "SH_IssueDate02": "ISSUE DATE",
    "SH_IdNo": "ID NO.",
    "SH_PassportNo": "PASSPORT NO.",
    "SH_Title3": "TITLE",
    "SH_HireDate2": "HIRE DATE",
    "SH_TerminationDate2": "TERMINATION DATE",
    "SH_RequestedFor": "REQUESTED FOR",
    "SH_Company2": "COMPANY",
    "SH_AuthorizedPerson": "REPRESENTATIVE",
    "FD_CostCenterCodeNoChinese": "Cost center code cannot include chinese",
    "SH_CompanyNumber2": "CONTACT NUMBER",
    "SH_CompanyAddress": "COMPANY ADDRESS",
    "FD_InvoluntarySeparationReason": "Involuntary separation reason",
    "FD_DownloadFormat": "Download format",
    "FD_FixedTermContractDuration": "Fixed term contract - contract duration",
    "SH_BatchDelete2": "Batch delete",
    "FD_TerminationCertificate2": "TERMINATION CERTIFICATE",
    "FD_InvoluntarySeparationCertificate": "INVOLUNTARY SEPARATION CERTIFICATE",
    "FD_EmployedCertificate2": "EMPLOYED CERTIFICATE",
    "SH_Agender": "Agender",
    "SH_ProfileImageSetting": "Profile Image Setting",
    "SH_Gender2": "Gender",
    "SH_DefaultPicture": "Default Picture",
    "SH_WhetherSureToWithdrawTheFollowData": "Are you sure you want to withdraw the following data?",
    "FD_CostCenterLimitEnglishNumber": "Cost center limit to letter or numberic characters",
    "SH_ExcellentLanguage": "Excellent",
    "SH_FairLanguage": "Fair",
    "SH_LittleLanguage": "Little",
    "SH_NoneLanguage": "None",
    "SH_Listening": "Listening",
    "SH_Speaking": "Speaking",
    "SH_Reading": "Reading",
    "SH_Writing": "Writing",
    "SH_InputErrorMessageSkill": "{0}Incorrect format, the same skill skill classification, please use '|' to separate the options e.g.,skill1|skill2|skill2",
    "SH_SkillClassification": "Skill Classification",
    "SH_Skill": "Skill",
    "SH_LanguageSkill02": "LANGUAGE AND SKILL INFORMATION",
    "FD_PersonalLanguageSkillList": "Personal language and skill list",
    "FD_ExpatriateChangeList": "Expatriate change list",
    "PY_EmployeeIdNotExistOrNotAuthorized02": "employee number is not exist or does not have the permission to perform this  adjustment",
    "FD_LanguageSkillBatchImport": "Language and skill Batch Import",
    "SH_StartDate2": "Start Date",
    "SH_FormNo": "Form No",
    "FD_LanguageSkillBatchImportTemplate": "Language and Skill Batch Import Template",
    "FD_LanguageSkillBatchImportErrorDescription": "Language and Skill Batch Import exception description",
    "FD_LanguageData": "Language Data",
    "FD_SkillData": "Skill Data",
    "SH_AdministratorCreated": "Administrator created",
    "SH_StartDateTimeIncorrect": "Start date or time is incorrect",
    "SH_EndDateTimeIncorrect": "End date or time is incorrect",
    "SH_Invaliding": "Voiding",
    "SH_ParameterName": "Parameter name",
    "SH_Value": "value",
    "SH_EmployeeBatchSetting": "Employee batch setting",
    "SH_DeductionSetting": "Deduction setting",
    "SH_AllowedTimes": "Allowed times",
    "SH_DeductionMethod": "Deduction method",
    "SH_Selected2": "Selected {0} item(s)",
    "SH_AddSuccess": "Successfully added",
    "SH_BatchApplied": "Batch applied",
    "SH_CodeRepeat": "Code duplicated",
    "SH_MyPayslip": "My Payslip",
    "SH_SalarySubjectCode": "Payroll subject code",
    "SH_ParameterType": "Parameter type",
    "SH_Text": "Text",
    "SH_Boolean": "Y/N",
    "SH_Numeric": "Numeric type",
    "SH_Array": "Array",
    "SH_Restore": "Restore default",
    "SH_EmpEditIsWrong": "Employee ID edit is wrong",
    "SH_EmpLeaveWithoutPay": "Employee is leave without pay ",
    "SH_EmpHasBeenResigned": "Employee has been resigned",
    "SH_DataEmpDataPermision": "You don't have the data permission of the Employee ID",
    "SH_CannotAbeoveEffectiveDate": "It can't above the effective date.",
    "SH_EditEffectiveDateIsWrong": "The edit of effective date is wrong",
    "SH_EffectiveDateIsWrong": "The effective date is blank",
    "SH_IntervalIsOverlap": "Interval is overlap",
    "SH_Agent": "Agent",
    "SH_AgentStatus": "Agent ststus",
    "SH_InputIsError": "{0}input is error",
    "FD_Staying2": "{0}status is leave without pay",
    "FD_HasBeenResigned": "{0}has been resigned",
    "SH_NoticeAgent": "Notice：The employee is still the other employees’ agent",
    "SH_NoAgentList": "Lack of the agencies setting list of people+, please ask for HR department.",
    "SH_NoMatchAgentList": "The apply duration does not consist with the agencies list of people+, please ask for HR department.",
    "SH_CodeDulicated": "Code duplicated",
    "FD_AgentBatchImport": "Agent Batch Import",
    "FD_AgentBatchImportErrorDescription": "Agent batch import exception description",
    "FD_AgentBatchImportTemplate": "Agent Batch Import Template",
    "FD_AgentList": "Agent list",
    "SH_NotApplySendAgain": "The selected item is no longer available for application, please send it again.",
    "SH_EmployeeDifferentAgent": "Employee no. should different from agent employee no.",
    "SH_LaborPensionType": "Labor pension type",
    "SH_LaborPensionTypeBlank": "Labor pension type is blank",
    "SH_LaborPensionTypeIncorrect": "Labor pension type is incorrect",
    "SH_ContributionIdentityType": "contribution identity type",
    "SH_ContributionIdentityTypeBlank": "Contribution identity type is blank",
    "SH_ContributionIdentityTypeIncorrect": "Contribution identity type is incorrect",
    "SH_LaborPensionContributionIdentity01": "Workers designated under the Labor Standards Act.",
    "SH_LaborPensionContributionIdentity02": "Commissioned workers",
    "SH_LaborPensionContributionIdentity03": "Employers",
    "SH_OtherWithholdingSetting": "Other withholding setting",
    "SH_FirstEmploymentInTheYear": "First employment in the year",
    "SH_FullTimeSchoolIntern": "Intern (full time school)",
    "SH_HostAttendanceInfoNotSetUp": "Host attendance attribution information has not been set up",
    "SH_OtherWithholdingSettingIncorrect": "Other withholding setting is incorrect",
    "SH_FieldShouldBeNull": "{0}should be NULL",
    "SH_ForeignersNewPensionSystemRule01": "Foreigners who have obtained permanent residency can choose the new pension system",
    "SH_ForeignersNewPensionSystemRule02": "Limited to those who obtain permanent residency",
    "AH_MailUnBoundUserForAD": "Please click the following link to log in MAYO service via {0} <br /><a href='{1}'> {2} </a>",
    "SH_InternalSeniorityBeginDate": "year of service (internal) begin date",
    "SH_AdjustSuggestionEmployerWithholdingRate": "Adjust suggestion to employer withholding rate",
    "SH_EmployerWithholdingRate": "employer withholding rate-{0}",
    "SH_Current": "current",
    "SH_Suggestion": "suggestion",
    "SH_Markallasread": "Mark all messages as read",
    "SH_FileFormat": "File format",
    "SH_FileFormarDownloadNote": "When fetching a lot of data, CSV format could be downloaded faster.",
    "SH_AssignmentStatus01": "unChecked",
    "SH_AssignmentStatus02": "Process",
    "SH_AssignmentStatus03": "Overdue",
    "SH_AssignmentStatus04": "End",
    "SH_AssignmentStatus05": "Close",
    "SH_AssignmentStatus06": "Back",
    "SH_ReadAll": "Read All",
    "SH_AssignmentStatus07": "AutoClose",
    "SH_AfterActivatedDate": "After the activated date of",
    "SH_Month02": "{0} month(s)",
    "SH_NextYear01": "Next (year)",
    "SH_EndOfJanuary": "The end of January",
    "SH_EndOfFebruary": "The end of February",
    "SH_EndOfMarch": "The end of March",
    "SH_EndOfApril": "The end of April",
    "SH_EndOfMay": "The end of May",
    "SH_EndOfJune": "The end of June",
    "SH_EndOfJuly": "The end of July",
    "SH_EndOfAugust": "The end of August",
    "SH_EndOfSeptemper": "The end of Septemper",
    "SH_EndOfOctober": "The end of October",
    "SH_EndOfNovember": "The end of November",
    "SH_EndOfDecember": "The end of December",
    "SH_Cycles01": "{0} cycle(s)",
    "SH_PaymentRules": "Payment rule",
    "SH_MustAfterStartDate": "Must later the start time",
    "SH_NotAdjustmentDependentsInsurance": "There is no adjustment on dependents insured information.",
    "SH_NotAdjustment02": "There is no adjustment on {0}.",
    "SH_WithholdingRateChangeOnlyOncePerMonth": "Employee Withholding Rate Change can only be applied once per month.",
    "SH_WithholdingRateAdjustOnlyTwicePerYear": "Employee Withholding Rate Adjustment can only be applied twice per year.",
    "SH_Adjustment02": "Adjustment",
    "SH_PaymentBaseNumber": "Payment base number-{0}",
    "SH_GroupInsurance": "Group Insurance",
    "SH_InsuranceStartDate": "Insurance start date",
    "SH_InsuranceEndDate": "Insurance end date",
    "SH_PersonalPaid": "Personal paid",
    "SH_CalculateCategory": "Calculation category",
    "SH_Details": "Details",
    "FD_TrainingType": "Training type",
    "FD_TrainingName": "Training name",
    "FD_TrainingStartDate": "Training date and time (start)",
    "FD_TrainingEndDate": "Training date and time (end)",
    "FD_TrainingHours": "Training hours",
    "FD_TrainingInstitution": "Training institution",
    "FD_CourseLecturer": "Course lecturer",
    "FD_Grades": "Grades",
    "FD_TrainingResult": "Training result",
    "FD_TrainingCost": "Training cost",
    "FD_BirthdayMonth": "Birthday month",
    "FD_EmployeeBirthdayList": "Employee birthday list",
    "FD_TrainingRecord": "Training record",
    "FD_TrainingReport": "Training report",
    "FD_Training": "Training",
    "FD_TrainingBatchTemplate": "Training batch template",
    "SH_NotExistOrError": "{0} does not exist/error",
    "FD_AddTrainingRecord": "Add training record",
    "FD_EditTrainingRecord": "Edit training record",
    "SH_NHISubsidyItem32": "Aboriginal people over 55 years old in Taoyuan",
    "SH_NHISubsidyItem33": "Aboriginal people",
    "SH_NHISubsidyItem34": "Aboriginal people in Lanyu Township",
    "SH_NHISubsidyItem35": "Receiving unemployed payers' dependents",
    "SH_NHISubsidyItem36": "Those receiving vocational training living allowances' dependents",
    "SH_NHISubsidyItem37": "Children (under the age of 6 ) in Penghu County",
    "SH_NHISubsidyItem38": "Overseas chinese",
    "SH_NHISubsidyItem39": "Young overseas chinese",
    "SH_NHISubsidyItem40": "Low-income foreign spouse",
    "SH_NHISubsidyItem41": "Low-income foreign spouse-1",
    "FD_TrainingBatchImport": "Training batch import",
    "SH_ExceptionDescription": "exception description",
    "FD_TrainingBatchImportErrorDescription": "Training batch import exception description",
    "SH_FormFieldSetting": "Form field setting",
    "SH_FieldLimit": "Field limit",
    "SH_TimeStartEnd": "Tme(Start to End)",
    "SH_MoreThanFileLimit": "Exceeds the {0} file limit",
    "SH_eFormProcessSetting": "eForm flow settings",
    "FD_IncorrectItemPleaseReferExample": "Incorrect item, please refer to the example on the bottom right side",
    "SH_PartTimeHolidayOvertimePay": "Part-time employee holiday attendance (overtime pay)",
    "SH_TransferFileVersionChangedSettingsWillCleared": "If the transfer file version is changed, the settings will be cleared, please check if you want to change it.",
    "FD_TrainingDateTime": "Training date and time",
    "SH_InProcess": "In Process",
    "SH_ApproveCompleted": "Completed",
    "SH_Reject03": "Reject",
    "SH_Withdrawn": "Withdrawn",
    "SH_NotApplied": "Not applied",
    "SH_SystemTimeOut": "The system execution timed out, please connect with Apollo service contact",
    "SH_BankAccountOpening": "Bank Account Opening",
    "SH_BankAccountBinding": "Bank Account Binding",
    "SH_OnlineBanking": "Online Banking",
    "SH_NewItem": "New Item",
    "SH_ReviewOrder": "Review Order",
    "SH_Period03": "period",
    "SH_RetreatApplication": "Retreat application",
    "SH_SaveForm": "Save form",
    "SH_AddLanguageSkills": "Add Language Skills",
    "SH_AddDepartmentCategory": "Add department category",
    "SH_PartialKeywordSearch": "Partial keyword search",
    "SH_CaseClosed": "Case Closed",
    "SH_ReturnComments": "Return comments",
    "SH_EnterReturnComments": "Enter return comments",
    "SH_WithdrawForm1": "Withdraw form",
    "SH_SubmitForApproval": "Submit for approval",
    "SH_Personnotfound": "Person not found",
    "SH_Changedate1": "Change Date",
    "SH_Adjustmentdescription": "Adjustment Description",
    "SH_Reasonforadjustment": "Reason for Adjustment",
    "SH_Enterwithdrawalformcomment": "Please enter withdrawal form comment",
    "SH_Selectatleastoneitem": "Select at least one item",
    "SH_Setsearchcriteria": "Please set search criteria",
    "SH_Dataselectedsametime": "At most {0} data can be selected at the same time",
    "SH_selected3": "{0} data has been selected",
    "SH_Updatedate": "Update Date",
    "SH_Identity": "Identity",
    "SH_Selectsearchcriteria": "Please select search criteria",
    "SH_separatewithspacing": "In case of multiple keywords, please separate with spacing",
    "SH_Subordinateunits": "Subordinate units",
    "SH_Pendingapproval": "Pending Approval list",
    "SH_Loading": "Loading",
    "SH_ContactEmail": "Contact Email",
    "SH_Lastupdateby": "Last update by",
    "SH_Createform2": "Create Form",
    "SH_Addapprovers2": "Please click the button in the upper right corner to add approvers",
    "SH_Addapprovers": "Add approvers",
    "SH_Lastupdatedate": "Last update Date",
    "RM_Queryprogress": "Query Progress",
    "SH_Importedsuccessfully": "Imported successfully",
    "SH_Dataverification": "Data verification",
    "SH_Failureperformprocessagain": "Failure list as following, please perform the process again or contact the Apollo service desk",
    "SH_FileFormatLimit03": "File format restrictions of JPG, GIF, PNG files",
    "SH_FileFormatLimit04": "File formats are limited to JPG, GIF, PNG files, and <br /> size is limited to 300KB.",
    "SH_FileFormatLimit05": "File format restrictions: <br />.doc/.docx/.xls/.xlsx/.ppt/.pptx, <br />.pdf/.rtf/.txt/.zip/.7z/.rar), <br />.jpg/.jpeg/.png/.bmp",
    "SH_FileFormatLimit06": "File formats are limited to <br />.doc/.docx/.xls/.xlsx/.ppt/.pptx, <br />.pdf/.rtf/.txt/.zip/.7z/.rar, <br />.jpg/.jpeg/.png/.bmp, <br /> size limit is 10MB",
    "SH_Passportpinyinsurname": "Passport Pinyin-Surname",
    "SH_Passportpinyinfirstname": "Passport Pinyin-First Name",
    "SH_SchoolName1": "School name",
    "SH_Schoolslocation": "The school's location",
    "SH_Contentduplicated": "Content duplicated",
    "SH_Addlanguages": "add languages",
    "SH_Times": "time(s)",
    "SH_Replyresult": "Reply to the result",
    "SH_VieweForm": "View the eForm",
    "SH_Applyingapprovalpolicy": "Applying the approval policy",
    "SH_Approvalrulenotsetting": "approval rule is not setting",
    "SH_Companywide": "Company-wide",
    "SH_Setapproval": "Approval setting",
    "SH_Approvalrule2": "approval rule",
    "SH_Approvalrule": "approval policy",
    "SH_Setapprovalrule": "approval rule setting",
    "SH_Addapprovalrule3": "Please set the approval condition prior adding new approval rules",
    "SH_Addapprovalrule2": "Please click Add item at the top right to add a new approval rule",
    "SH_Editapprovalrule": "Edit approval rule",
    "SH_Addapprovalrule": "Add approval rule",
    "SH_approvalrulename": "Approval rule name",
    "SH_Manageapprovalpolicy": "Manage approval policy",
    "SH_Editapprovalpolicy": "Edit approval policy",
    "SH_Addapprovalpolicy": "Add approval policy",
    "SH_Approvalpolicyname": "Approval policy name",
    "SH_Priority": "Priority",
    "SH_Ruleconditions": "Rule Conditions",
    "SH_leastonelevelsupervisorsign": "Sign up to at least one level of supervisor",
    "SH_Highestlevelsignto ": "Highest level to sign to ",
    "SH_Levelatleastsignto": "Level at least to sign to",
    "SH_Upperlevelsignoff": "upper level sign-off required",
    "SH_Level": "level",
    "SH_Suretocancel": "Are you sure to cancel?",
    "SH_Addinformation": "Please click the button in the upper right corner to add information",
    "SH_Displaynotdisplay": "Display or not display",
    "SH_Requirednotrequired": "Required or not required",
    "SH_Displayname": "display name",
    "SH_Fieldname": "Name of Field",
    "SH_Signaturesetting": "Signature setting",
    "SH_Templatesetting": "Template setting",
    "SH_Shared": "Shared",
    "SH_Option": "Option",
    "SH_filelimitsizelimit": "The file limit is {0} and the size limit is {1}",
    "SH_Managertitle": "Manager title",
    "SH_Signaturefile": "Signature file",
    "SH_MobilePhone": "Mobile Phone",
    "SH_Email2": "Email",
    "SH_Note": "Note",
    "SH_SelectJobGrade": "Select job grade",
    "SH_TemporaryDepositSuccessful": "Temporary deposit successful",
    "SH_IDInformation": "ID information",
    "SH_ChangeEmployeeInfo": "Are you sure you want to change your employee information?",
    "SH_Corrected": "Corrected",
    "SH_PleaseEnter": "Please enter",
    "SH_ApplicationForm": "Application eForm",
    "SH_DifferentPassword": "Password confirmation does not match password",
    "SH_Uploader": "Uploader",
    "SH_UploadTime": "Upload time",
    "SH_NoMatchingInfo": "No matching information",
    "SH_DeliveryExceptions": "Delivery Exceptions",
    "SH_SignatureFailure": "Signature failure",
    "SH_DataAcquisitionAbnormality": "Data acquisition abnormality",
    "SH_EmployeeStatus": "Employee Status",
    "SH_EditVariationForm": "Edit the variation form",
    "SH_DataLoadingInProgress": "Data loading in progress",
    "SH_PleaseFillInComments": "Please fill in your comments",
    "SH_SigningForm": "Signing Form",
    "SH_SuccessfulVisaVerification": "Successful visa verification",
    "SH_PleaseSelectRole": "Please select role",
    "SH_PleaseSelectLevel": "Please select level",
    "SH_AssignAfterOrganizationSignOff": "Designated signatures after organization sign-off",
    "SH_AssignBeforeOrganizationSignOff": "Designated signatures prior to organization sign-off",
    "SH_DesignatedPersonOrRole": "Designated person or role (up to 3 people)",
    "SH_DesignatedPersonIsTheApplicant": "If the designated person is the applicant, does he/she need to sign",
    "SH_DesignatedPerson": "Designated Person",
    "SH_DesignatedRole": "Designated Role",
    "SH_OrganizationalApproval": "Organizational Approval",
    "SH_UpwardCheckLevel": "Upward check level",
    "SH_Above": "Above",
    "SH_Day": "Day",
    "SH_NoLimit": "No limit",
    "SH_CannotUploadBlankFiles": "Cannot upload blank files",
    "FD_SelectPersonalPhoto": "Please select a personal photo",
    "SH_OnTheJobDate": "On-the-job date",
    "SH_ChangeTime": "Change time",
    "FD_EnquiryNote": "Note: If the date of change is used as the query criterion, the unit and the supervisor will download the information effective as of the date of the query area; if the date of employment is used as the query criterion, the unit and the supervisor will download the information effective as of the date of employment",
    "SH_DataModule": "Data Module",
    "FD_Newcomers": "Newcomers",
    "FD_Leavers": "Leavers",
    "FD_RetainedStaff": "Retained Staff",
    "FD_DownloadRecords": "Download Records",
    "FD_ReasonChangeSetting": "Reason for Change Setting",
    "FD_EditAccountInformation": "Edit account information",
    "FD_AccountActivatedDefaultPassword": "The account has been activated. The employee account and the default password (Apollo+Birthday+Month+Day) have been sent to the employee mailbox, please follow the steps in the mailbox to authenticate and change the password to start using the system functions.",
    "FD_ConfirmReset": "Confirm Reset",
    "FD_ImportWillBeEnabledAtTheSameTime": "Please note: The system will send an \"Apollo XE Account Activation Letter\" to the employee's company mailbox you have imported.",
    "SH_QueryCondition": "Query condition",
    "FD_AuditNotice": "Audit Notice",
    "FD_AuditEmployeeInformation": "Audit Employee Information",
    "FD_AuditStatus": "Audit Status",
    "FD_ExistingInformation": "Existing Information",
    "FD_ApplicationInformation": "Application Information",
    "FD_RevisionApproved": "Revision approved",
    "FD_ChangeContactInfo": "Change contact data information",
    "FD_ChangeBasicInfo": "Change Basic Information",
    "FD_ChangeEducationInfo": "Change education information",
    "FD_AddEducationData": "Create education information",
    "FD_ChangeCertificationInfo": "Change certificate information",
    "FD_AddCertificationData": "Create certificate information",
    "FD_TypeDataCorrection": "Type of Data Correction",
    "FD_EmergencyContactNumber": "Emergency Contact Number (Mobile)",
    "FD_ReasonReturnApplication": "Reason for return of application",
    "FD_TypeIdentity": "Type of identity",
    "FD_PersonDoesNotExist": "This person does not exist",
    "FD_UnitDoesNotExist": "This unit does not exist",
    "FD_NotOnBoard1": "Not on board",
    "FD_UseOldEmployeeNumber": "Use the old employee number",
    "FD_OriginalEmployeeNumber": "Original employee number",
    "SH_SerialNumber1": "serial number",
    "FD_EmployeeNumberCannotBeUsed": "This employee number cannot be used",
    "FD_DataTransfer": "Data Transfer",
    "FD_AccountCreation": "Account creation",
    "FD_ImportTalentPool": "Import from Talent Pool",
    "FD_AddDocumentType": "Add document type",
    "FD_EmailIsApolloLoginAccount": "The email address entered in this field is the Apollo login.",
    "FD_ReEmploymentContinuingSeniority": "This is a re-employed employee, please confirm if you are renewing your previous employment.",
    "FD_AddWorkExperience": "Create work experiences",
    "FD_NoInformationYet": "No information yet",
    "FD_SelfRecommendation": "Self-recommendation",
    "FD_InterviewAttachment": "Interview attachment",
    "FD_Education": "Education background",
    "FD_Recommender": "Recommender",
    "FD_ResumeAttachment": "Resume attachment",
    "SH_Limit": "Limit",
    "SH_LimitSettingFailed": "Limit setting failed",
    "FD_AccountActivation": "Account Activation",
    "PT_RequestSpecialLeaveForm": "Request Special Leave",
    "PT_ImportLeaveTypeFlow": "Import Leave Type",
    "SH_AnnualOperation": "Annual Operation",
    "SH_BelowComingSoon": "Coming Soon",
    "SH_CreatePersonnelFile": "Foundation - Create personnel file",
    "SH_AccountSetup": "Account set up",
    "SH_FeatureRecommendations": "Feature Recommendations",
    "SH_GetStarted": "Get Started",
    "SH_NewHireOnBoard": "New Hire Onboarding",
    "SH_SetAttendanceRules": "Attendance - Set attendance rules",
    "SH_SetSalaryInsurance": "Payroll - Set up personnel salary, insurance, and income tax information",
    "SH_UseGuide": "Use Case Guide",
    "SH_UseGuideL2Intro": "Before you start, please have the following data ready:",
    "SH_UseGuideL2Intro01": "Employee data of the new hire(s)",
    "SH_UseGuideL2Intro02": "Employee numbering rule(s)",
    "SH_UseGuideL2Intro03": "Applicable work shift, attendance rules, and punch card setting for the new hire(s)",
    "SH_UseGuideL2Intro04": "Applicable leave policy for the new hire(s)",
    "SH_UseGuideL2Intro05": "Payment terms: payroll type and range",
    "SH_UseGuideL2Intro06": "Labor insurance and NHI insurance information(dependent included)",
    "SH_UseGuideL2Intro07": "Income tax information(dependent included)",
    "SH_UseGuideL2Intro08": "The effective email address of the new hire(s)",
    "SH_UseGuideL3Intro01": "Set onboard date and start creating personnel file",
    "SH_UseGuideL3Intro02": "Fill in employee information and their role related information",
    "SH_UseGuideL3Intro03": "Choose the new hire(s) to start onboarding process",
    "SH_UseGuideL3Intro04": "Create a new change form to set work shift(班別異動), attendance rules(出勤規則異動), and punch card setting",
    "SH_UseGuideL3Intro05": "Leave hours are set according to attendance rules",
    "SH_UseGuideL3Intro06": "Add salary details, insurance information, and income tax information",
    "SH_UseGuideL3Intro07": "Create dependents enrollment information",
    "SH_UseGuideL3Intro08": "Add dependent information to include into income tax calculation",
    "SH_UseGuideL3Intro09": "Set email addresses as login accounts",
    "SH_HireOnBoardList": "Hire Onboard List Management",
    "FD_StatusMessage": "Status message",
    "FD_StatusMessageInputPrompt": "Your coworkers will see this in Search People",
    "SH_UseGuideOnConfirm": "Activate use case guide?",
    "SH_UseGuideOffConfirm": "Deactivate use case guide?",
    "SH_UseGuideOnAlready": "Use case guide activated. Deactivate it?",
    "SH_CreateTerminationForm": "Create Resignation Form",
    "SH_WithdrawalOperation": "Insurance & Pension Withdrawal Operation",
    "SH_CreateEmployeeChangeForm": "Create People+ Change Form",
    "SH_CreateLeaveWithoutPayForm": "Create Leave Without Pay Form",
    "SH_UseGuideL2Intro09": "Employee resignation date(=Last working day)",
    "SH_UseGuideL2Intro10": "Items to be withdrawn(Labor insurance, labor pension, occupational injury insurance, NHI)",
    "SH_UseGuideL2Intro12": "Employee last working date",
    "SH_CreateInsuranceForm": "Add New Insurance Data",
    "SH_CreateResignationSettlement": "Add Resignation Settlement & Generate Data",
    "SH_Transport": "Transport Data",
    "SH_CreateLeaveWithoutPaySettlement": "Add Leave Without Pay Settlement & Generate Data",
    "SH_UseGuideL3Intro10": "Set the effective date as the next day of employee's resignation date, and set the change action as resignation.(Starting from effective date, the employee won't be able to log into the system.)",
    "SH_UseGuideL3Intro11": "Set the effective date as the next day of employee's resignation date, and set the status to withdrawal insurance.",
    "SH_UseGuideL3Intro12": "Set the annual leave cash out back multiple years to avoid missed calculation",
    "SH_UseGuideL3Intro13": "Click on setting icon and check the data in each tab",
    "SH_UseGuideL3Intro14": "Please check 5 tabs separately(Attendance warning, Work timetable closing, Leave deduction, Overtime pay, Compensatory leave encashment, Salary for unused leave)<br />  ✱Encashment options:<br /> - 結算拋轉(Transport Settlement)：Hours will be transported to Payroll to calculate. <br /> - 結算不拋轉(Not Transport Settlement) ：Hours won't be transported to Payroll to calculate, and will be locked to prevent future use. <br /> - 保留可用時數(Keep Remain Hours)：Hours won't be transported to Payroll to calculate, but hours remain available(Change the expiration date if passed)",
    "SH_UseGuideL3Intro15": "Click \"Transporting one data type\" and it will be transported to Payroll module",
    "SH_UseGuideL3Intro16": "Set the effective date as the next day of employee's last working date, and set the change action as leave without pay.(Starting from effective date, the employee won't be able to log into the system.)",
    "SH_UseGuideL3Intro17": "Set the effective date as the next day of employee's last working date, and set the status to withdrawal insurance.",
    "SH_CreateReinstatementForm": "Create Reinstatement Form",
    "SH_EmpInsuranceOperation": "Employee Insurance Enrollment",
    "SH_EmpInsuranceData": "Employee Insurance Data",
    "SH_UseGuideL2Intro13": "Reinstatement date",
    "SH_UseGuideL2Intro14": "Items to be enrolled(Labor insurance, labor pension, occupational injury insurance, NHI)",
    "SH_UseGuideL3Intro18": "Set change action to reinstatement",
    "SH_UseGuideL3Intro19": "Select the employee, and enter insurance information section. Add insurance and/or pension information that are needed to enroll.",
    "SH_EditAccountInfo": "Edit Account Info",
    "SH_QuickTimeFilterThisYear": "This year",
    "SH_QuickTimeFilterLastYear": "Last year",
    "SH_QuickTimeFilterLast6Month": "Last 6 month",
    "SH_QuickTimeFilterLast3Month": "Last 3 month",
    "SH_TimeFilterCustom": "Custom period",
    "SH_UnitAndManager": "Unit and Manager",
    "SH_CompanyInformation": "Company Information",
    "FD_OutputIsBasedOnParameterSetting": "※ Reminder: If the data is set to not shown in change form, your download ouput will be empty. Please check {0}",
    "FD_ManagerAuthorizationType": "Manager Authorization Type",
    "FD_ManagerDelegationStatus": "Manager Delegation Status",
    "SH_SetEmployeeEmail": "Set Employee Account Email",
    "SH_UseGuideL3Intro20": "After searching the employee's data with employee no. or name, find the \"edit account\" function under \"manage(管理功能)\". Then, input email as login account. <br />✱Please note: If the employee's data is not found, check and adjust the account verified(是否已驗證) filter.",
    "SH_ReceiveAccountActivationEmail": "Receive Account Activation Mail",
    "SH_UseGuideL3Intro21": "Follow the instructions in the acitvation mail, use the given password to login, reset the password, and verify the ID on App or web in order.",
    "SH_EditAccountEmail": "Edit Account Email",
    "SH_UseGuideL3Intro22": "If the email is set incorrectly, follow the steps of \"Set Employee Account Email\" to set the correct email as the account.",
    "SH_BatchSetAccountEmail": "Batch Change Account Email",
    "SH_UseGuideL3Intro23": "After downloading the template, fill in the emails of each employee in Excel, and upload.",
    "SH_ResetAccountPassword": "Reset Account Password",
    "SH_UseGuideL3Intro24": "After searching the employee's data with employee no. or name, find the \"reset password\" function under \"manage(管理功能)\". <br />✱Please note: If the employee's data is not found, check and adjust the account verified(是否已驗證) filter.",
    "SH_LoginAndResetPassword": "Login and Reset Password",
    "SH_UseGuideL3Intro25": "Use the reset password sent to email",
    "FD_RequestAddEmployeeData": "Request to add employee data",
    "PT_SetAttendanceRules": "Set attendance rules",
    "SH_UseGuideL1Intro01": "Check Work Shift Setting",
    "SH_UseGuideL1Intro02": "Work Shift and Attendance Rule Applied to Employee",
    "SH_UseGuideL1Intro03": "Plan Work Schedule on the Interface",
    "SH_UseGuideL1Intro04": "Plan Work Schedule by Importing",
    "SH_UseGuideL1Intro05": "Employee Request Overtime",
    "SH_UseGuideL1Intro06": "Manager Review Overtime eForm",
    "SH_UseGuideL1Intro07": "Employee Confirmation(This step is required if your company does not allow after-hour overtime request)",
    "SH_UseGuideL2Intro15": "Work Shifts to Be Applied",
    "SH_UseGuideL2Intro16": "Calendar Applied to the Work Shifts",
    "SH_UseGuideL2Intro17": "Attendance Rules to Be Applied",
    "SH_UseGuideL2Intro18": "Employees to Be Scheduled",
    "SH_UseGuideL2Intro19": "Work Shifts Can Be Applied by Employees",
    "SH_UseGuideL2Intro20": "Attendance Rules Can Be Applied by Employees",
    "SH_UseGuideL2Intro21": "Employee No. of the Employees to Be Scheduled",
    "SH_UseGuideL2Intro22": "Date Matched Events(e.g. Working day/One fixed day off/Monthly leave-One fixed day off)",
    "SH_UseGuideL2Intro23": "Shift Schedules to Be Applied",
    "SH_UseGuideL3Intro26": "Check the Calendar to Be Appplied to Work Shift",
    "SH_UseGuideL3Intro27": "Check which calendar is applied to the work shift to be applied on the work shift list.",
    "SH_UseGuideL3Intro28": "Check the Calendar",
    "SH_UseGuideL3Intro29": "Leave events are suggested to not be added for work schedule arrangement.",
    "SH_UseGuideL3Intro30": "Turn on \"Scheduling function\" in Attendance Rule",
    "SH_UseGuideL3Intro31": "In the attendance rule, click on \"Working schedule rules\" tab and turn on the switch of \"Scheduling function\".",
    "SH_UseGuideL3Intro32": "Check \"Monthly holiday setting\" of \"Working schedule rules\"",
    "SH_UseGuideL3Intro33": "Check and fill in the number of days can be used by employees. Valid period is suggested to be set to 1 months after opening.",
    "SH_UseGuideL3Intro34": "Check Work Shift and Attendance Rule Applied to Employees",
    "SH_UseGuideL3Intro35": "If you need to adjust work shift or attendance rule, add a change form.",
    "SH_UseGuideL3Intro36": "Search Unit and Year/month to Get the Work Timetable",
    "SH_UseGuideL3Intro37": "Click \"Edit\" and Start Planning Work Timetable",
    "SH_UseGuideL3Intro38": "Click \"Leave type\" and \"Shift schedule\" in the gray box first, then fill in the timebox below to schedule shift for employees by the order of: national holiday, leave/rest day, shifts.\nWhen needing to change planned schedule, right click on the timebox you wish to change the click \"Delete\" or \"Set your own work time\" to adjust.",
    "SH_UseGuideL3Intro39": "Check Total Scheduled Work Time and Leave",
    "SH_UseGuideL3Intro40": "Click \"Overall information\" to check individual's accumulate hours and leave per month.",
    "SH_UseGuideL3Intro41": "Once the Work Schedule is Fully Arranged, Click \"Check and release\".",
    "SH_UseGuideL3Intro42": "The system will check if the scheduled work timetable fit working hour rules. You can decide to adjust according to the system alert or skip entirely and release the work timetable.",
    "SH_UseGuideL3Intro43": "Download Importing Template",
    "SH_UseGuideL3Intro44": "Click \"Import working schedule\" in Attendance module, then click \"Download the template\" on the up right.",
    "SH_UseGuideL3Intro45": "Fill in the Excel Template",
    "SH_UseGuideL3Intro46": "Fill in the required(*) information in the Excel file. \"Status code(狀態代碼)\" and \"Shift code(班次代碼)\" can be found on the sheets in the template file for you to check and fill in accordingly.",
    "SH_UseGuideL3Intro47": "Back in Apollo XE, choose the file in import working schedule",
    "SH_UseGuideL3Intro48": "After Creating the Data, Check and Upload",
    "SH_UseGuideL3Intro49": "If errors occur, adjust the file data according to the system messages and reupload.",
    "SH_UseGuideL3Intro51": "Fill in time, compensatory method, and rationale. Beware of the overtime policy of your company.",
    "SH_UseGuideL3Intro52": "Click the Row of the Overtime eForm to View Detail",
    "SH_UseGuideL3Intro53": "Check Employee's check-in/out Records",
    "SH_UseGuideL3Intro54": "Choose Your Approval Result",
    "SH_UseGuideL3Intro55": "Click the Row of the Overtime eForm Whose Status is \"Waiting for confirmation\"",
    "SH_UseGuideL3Intro56": "Adjust Actual Overtime Date/time",
    "SH_UseGuideL3Intro57": "Click \"Confirm\" After Filling All the Required Data",
    "SH_UseGuideL1Intro08": "Request Special Leave and Request Leave",
    "SH_UseGuideL1Intro09": "Manager Review Leave Form",
    "SH_UseGuideL1Intro10": "Administrator Review Special Leave Request",
    "SH_UseGuideL1Intro11": "Create and Turn On Leave Type",
    "SH_UseGuideL1Intro12": "Check Leave Rule in Attendance Rule Setting",
    "SH_UseGuideL1Intro13": "Import Employee's Available Leave Hours(Entitlement)",
    "SH_UseGuideL2Intro24": "Adding or Adjusting Leave Type",
    "SH_UseGuideL2Intro25": "Attendance Rules Applied by Employee ",
    "SH_UseGuideL2Intro26": "Employees Entitled to Leave",
    "SH_UseGuideL2Intro27": "Leave Type, Entitlement, and Valid Period",
    "SH_UseGuideL3Intro58": "Chosen Leave Type",
    "SH_UseGuideL3Intro59": "Request Special Leave",
    "SH_UseGuideL3Intro60": "When not finding the leave type needed, click on \"Special Leave eForm\" button to request for the leave type you need. You can proceed the leave request process once acquired the leave hours. <br />✱Please note: Although leave request process can be proceeded, the special leave hours still require administrator's approval.",
    "SH_UseGuideL3Intro61": "Click the Row of the Form to Enter Review Window",
    "SH_UseGuideL3Intro62": "Give Your Approval Opinion",
    "SH_UseGuideL3Intro63": "Choose approve or disapprove, and click \"Confirm\".",
    "SH_UseGuideL3Intro64": "Choose approve or disapprove, and click \"Confirm\". <br />✱Please note: If the leave form is already approved, you will need to go to <a href='/ta/manager/formrecordmanage/leaverecords'>Managing eForms Records</a> to revise the leave form information.",
    "SH_UseGuideL3Intro65": "Add Company Customized Leave",
    "SH_UseGuideL3Intro66": "Click \"Add\" on the up right and fill in leave code and leave name.",
    "SH_UseGuideL3Intro67": "Check the Leave Type",
    "SH_UseGuideL3Intro68": "After adding the leave type, go back to the list to check the setting is turned on.",
    "SH_UseGuideL3Intro69": "Set Attendance Rule",
    "SH_UseGuideL3Intro70": "Choose the attendance rule to be revised, and enter the setting page.",
    "SH_UseGuideL3Intro71": "Check Leave Rule Setting",
    "SH_UseGuideL3Intro72": "In the \"Leave Rule\" tab, find the leave type and enter the setting page. Check \"Leave entitlement setting\", \"Leave setting\", and \"Salary setting\" sections respectively. Click \"Confirm\" to save your settings.",
    "SH_UseGuideL3Intro73": "Check the Leave Type Is Turned On in the Attendance Rule",
    "SH_UseGuideL3Intro74": "Once turned on, eligible employees can request this special leave type.",
    "SH_UseGuideL3Intro75": "Choose Year and Type of Leave to Be Imported",
    "SH_UseGuideL3Intro76": "Fill in the Template",
    "SH_UseGuideL3Intro77": "Fill in the employee no. or names to be applied to this leave type, as well as the entitlement(hours, minutes), valid period, and payroll basis date.",
    "SH_UseGuideL3Intro78": "Upload Excel File and Create the Data",
    "SH_UseGuideL3Intro79": "Adjust Entitled Leave Hours",
    "SH_UseGuideL3Intro80": "Search by \"Leave type\" and/or \"Employee ID/Name\" and click \"Setting\" to manage individual's entitlement, valid period, and payroll basis date.",
    "SH_GoogleCalendar": "Google Calendar",
    "SH_ThirdPartyIntegration": "Third-party Integration",
    "SH_GoogleCalendarEnableConfirm": "Once enabled, employees can sync the attendance information of their affliated unit and subordinate units to their Google account.  Information is synced after 24 hours at first time.",
    "SH_EnableConfirm": "Enable Confirmation",
    "SH_Disclaimer": "Disclaimer",
    "SH_GoogleCalendarBeforeEnable": "Please read carefully and agree to the following before enabling this function.",
    "SH_GoogleCalendarDisclaimerAdministrator": "1. Data Sharing Consent: By enabling third-party integration features, you confirm that you agree to share certain data from Apollo XE with collaborative external third-party platforms (including but not limited to Google).<br /> 2. Data Management Responsibility: Before enabling this feature, you should define clear data sharing standards within your organization to ensure that all data use, processing, and transmission comply with your organization's privacy policy and relevant laws and regulations.<br /> 3. Knowledge Dissemination: You are responsible for ensuring that your colleagues, employees, or other stakeholders have been informed of these data sharing standards and fully understand their rights and obligations when using the data sharing features of Apollo XE.<br /> 4. Disclaimer: Our company is not responsible for the content or privacy practices of third-party platforms and cannot control such content or privacy practices. Therefore, you should review the information protection policies of these third-party platforms on your own. Once you share your data with a third-party platform, our company and its affiliates will not be responsible for any data security issues, privacy breaches, or any other forms of improper data handling that may arise from the third-party platform. <br /> Before enabling this feature, please ensure that you have read, understood, and agreed to all the above terms. If you do not agree to these terms, please do not enable the data sharing function with third-party platforms.",
    "SH_DisableIntegration": "Disable integration",
    "SH_GoogleCalendarDisableConfirm": "Disable integration with Google Calendar?",
    "SH_TermsReadAndAgree": "I have read and agree to the terms",
    "SH_GoogleCalendarDisableAdmin": "Once disabled, the information will no longer be synced to the integrated Google accounts. Information updated to Google will not be deleted.",
    "SH_Integration": "Integration",
    "SH_GoogleCalendarIntegration": "Google Calendar Sync",
    "SH_GoogleCalendarIntegrationDescription": "将部门同仁的出勤资料更新至您的 Google 帐号，第一次开启时需等待 24 小时",
    "SH_GoogleCalendarIntegrationTooltips": "Your company hasn’t enabled this function, please contact system admins to open it.",
    "SH_IntegrationLastSynced": "Last synced at",
    "SH_GoogleCalendarEnable": "Enable syncing with Google Calendar?",
    "SH_GoogleCalendarDisable": "Disconnect syncing with Google Calendar?",
    "SH_GoogleCalendarDisableEmp": "Once disabled, the information will no longer be synced to Google Calendar. Information updated to Google will not be deleted.",
    "SH_IntegrationDisable": "Disconnect successfully",
    "SH_GoogleCalendarDisclaimerEmployee": "Dear user, when you choose to synchronize your data with third-party tools (including but not limited to Google), please carefully manage your personal and company data. Once you opt for data synchronization, you must take responsibility for protecting data security and comply with relevant data protection laws and regulations. This means you should not unauthorizedly distribute or disclose data to external individuals or unauthorized third parties outside the company.\n\nWe hereby declare that we are not responsible for any data you distribute through Google or other third-party tools. Any data leaks, misuse, or other forms of data security incidents that occur through such means should be your responsibility to bear the consequences. We strongly recommend that you read and understand the privacy policy and terms of use of the third-party tools in detail before performing data synchronization.\n\nPlease ensure that you have understood all the terms in this disclaimer before synchronizing your data and voluntarily comply with them. If you do not agree with the above terms, please do not proceed with data synchronization.",
    "SH_GoogleCalendarSetting": "Integration Setting",
    "SH_GoogleCalendarIntegrateSuccess": "Integrated successfully. You can view the attendance information on Google Caleandar in {0} minutes.",
    "SH_GoogleCalendarSyncLimit": "Please wait for at least 1 hour to sync again",
    "SH_DisableConfirm": "Disable Confirmation",
    "SH_GoogleCalendarSyncSuccess": "Synced successfully. You can view the attendance information on Google Caleandar in {0} minutes.",
    "SH_SyncData": "Sync data",
    "SH_UseGuideL1Intro14": "Review Check in/out Records and Signing Form",
    "SH_UseGuideL1Intro15": "Resignation and Leave Without Pay Settlement",
    "SH_UseGuideL1Intro16": "Settlement",
    "SH_UseGuideL1Intro17": "Match Insurance Premium",
    "SH_UseGuideL1Intro18": "Process Payroll/Bonus",
    "SH_UseGuideL1Intro19": "Employee ID Initial Code Setting",
    "SH_UseGuideL1Intro20": "Publish Yearly Calendar",
    "SH_UseGuideL1Intro21": "Grant Annual Total Leaves",
    "SH_UseGuideL1Intro22": "Set Monthly Leaves of Shift Working Employees",
    "SH_UseGuideL1Intro23": "Maintain Payroll Cycle",
    "SH_UseGuideL1Intro24": "Adjust Occupational Injury Premium Rate",
    "SH_UseGuideL2Intro28": "Settlement Period",
    "SH_UseGuideL2Intro29": "Latest Bill of Labor insurance, labor pension, occupational injury insurance, and NHI",
    "SH_UseGuideL2Intro31": "Only companies that use the year as a prefix code for employee no. are advised for this operation.",
    "SH_UseGuideL2Intro32": "Occupational Injury Premium Rate Based On Industries",
    "SH_UseGuideL3Intro81": "Confirm the Check In/Out Records Are Correct(No Warning)",
    "SH_UseGuideL3Intro82": "Confirm the Signing Forms Are Cleared",
    "SH_UseGuideL3Intro83": "Calculate By Each Data Type In Order",
    "SH_UseGuideL3Intro84": "Select payroll year/month and search to load the data. Starting from data type \"Attendance Warning(出勤異常)\" to \"Other Leave Encashment(其他假別代金)\", in the order of top to down, click on the calculating button to generate data. If the data is correct, click \"Cancel\", or close the window by clicking on X on the top right. If the data is incorrect, click \"Void\" to cancel the generated data, and go to Managing eForms Records to adjust the data, then recalculate.",
    "SH_UseGuideL3Intro85": "Unapproved Overtime Form Will Not Be Included In Settlement",
    "SH_UseGuideL3Intro86": "When the data shows employees having overtime hours but no overtime rate, there are likely forms waiting to be approved. Go back to Managing eForms Records to complete and confirm the forms are signed, then recalculate.",
    "SH_UseGuideL3Intro87": "Void Settlement Data",
    "SH_UseGuideL3Intro88": "If you need to void settled data, please operate from bottoms up, starting from the data type \"Other Leave Encashment(其他假別代金)\".",
    "SH_UseGuideL3Intro89": "Transport one data type to Payroll at a time to calculate amount by clicking on the right of each type, or click \"Transfer all\" to do so in batch.",
    "SH_UseGuideL3Intro90": "Upload Labor Insurance and NHI Insurance Bills Respectively",
    "SH_UseGuideL3Intro91": "Select year and insured unit information first, then click \"Add Item\". In the window, choose the verify item, upload the bill, and click \"Start Verify\". The system will generate the verified result.",
    "SH_UseGuideL3Intro92": "Settle The Difference Shown By The Results",
    "SH_UseGuideL3Intro93": "Select Year/Month to Start",
    "SH_UseGuideL3Intro94": "Select the year/month to calculate payroll and start operation process.",
    "SH_UseGuideL3Intro95": "Confirm",
    "SH_UseGuideL3Intro96": "Check 6 Data Types Respectively: <br /> - Employee information: Check if the employees(new hires, leave without pay, resignation, reinstatement) list and payment range are correct.\n<br /> - Salary details: Check if the payroll structure and information are correct.\n<br /> - Insurance information: Check if the labor and NHI insurance fees are correct.\n<br /> - Attendance information: Check if the data transferred from attendance is correct.\n<br /> - Matching Insurance premium discrepancies: Check if the matching Insurance premium discrepancies from last month are correct.\n<br /> - Other pay or deduction items: Enter or import the pay/deduction items applying to employees. If there are bonuses you need to merge with monthly payroll, enter here to be included.",
    "SH_UseGuideL3Intro97": "Data Calculation",
    "SH_UseGuideL3Intro98": "After clicking on \"Calculate\", wait for around 30 minutes then refresh the page to download each data detail to check. We advise you to double confirm the payroll subject and amount. Close the payroll operation only when details are confirmed.",
    "SH_UseGuideL3Intro99": "Calculation Complete",
    "SH_UseGuideL3Intro100": "Set payroll notification in order to allow employees to view payslip after a set time.",
    "SH_UseGuideL3Intro101": "Bonus Operation",
    "SH_UseGuideL3Intro102": "If the bonus is separating from the monthly pay, please select the \"Create a bonus operation\" on the up right, and execute step 2 to 4 mentioned above.",
    "SH_UseGuideL3Intro103": "Add Parameter",
    "SH_UseGuideL3Intro104": "Locating \"Employee no.\" and click the option setting(選項設定) in the setting column. Click \"Add new item(新增項目)\", then create a code for the next year. ",
    "SH_UseGuideL3Intro105": "Calendar For Normal Work Timetable",
    "SH_UseGuideL3Intro106": "The system will generate a calendar template based on a government-issued calendar annually. Click the \"Applying a template\" on the top right to directly apply. This calendar contains national holidays,  rest days(flexible leave) and special work day(make up day).",
    "SH_UseGuideL3Intro107": "Calendar For Shift Work Timetable",
    "SH_UseGuideL3Intro108": "Simply create a new empty calendar.",
    "SH_UseGuideL3Intro109": "Company Specific Event",
    "SH_UseGuideL3Intro110": "Click the \"New Event\" botton on top right to create a company-specific event. It could be a special work day, rest day, or fixed day off.",
    "SH_UseGuideL3Intro111": "Publish Calendar",
    "SH_UseGuideL3Intro112": "Once published, the corresponding events in the calendar can be applied to the employee work timetable.",
    "SH_UseGuideL3Intro113": "Create Leave Hours",
    "SH_UseGuideL3Intro114": "Click the \"Create Leave Hours\" botton on the top right. You can either create for each type of leave individually, or click the \"Create all\" botton on the window to create all types of leave in batch.",
    "SH_UseGuideL3Intro115": "Enter The Attendance Rule Setting of Employees to Be Scheduled",
    "SH_UseGuideL3Intro116": "Set Monthly Holiday Setting In The \"Working Schedule Rules\" Tab",
    "SH_UseGuideL3Intro117": "After opening the window of \"Monthly Holiday Setting\", switch the year to the next one. Fill in and check the number of leaves and days off for each month. Click \"Confirm\", then the employees who apply the attendance rule can use the leaves in the next year.",
    "SH_UseGuideL3Intro118": "Generate Payroll Cycle For Payment Range",
    "SH_UseGuideL3Intro119": "Select payment range, and set the year. Click the \"Generate Payroll Cycle\" button on the right. The payroll cycle of next year for each month will then appear below.",
    "SH_UseGuideL3Intro120": "Fill in the new occupational injury premium rate based on the industry category of the insured unit.",
    "SH_UseGuideL3Intro121": "Select date duration first, then clear all the warnings by checking each warning has corresponding eForm (leave, forgot to clock in/out) or absenteesim.",
    "SH_UseGuideL3Intro122": "Select date duration first, then make sure all the pending eForms are signed by sending a reminder, changing the approver, approving on behalf of others, or deleting the eForm.",
    "SH_ExpandTable": "Expand the table",
    "SH_CollapseTable": "Collapse the table",
    "SH_FormTracking": "Track Forms",
    "SH_FormToBeApproved": "Pending Forms for Approval",
    "SH_FormNewApply": "Apply Forms",
    "SH_FormDesign": "Design Forms",
    "SH_MayoFormApproved": "Your {0} is approved",
    "SH_MayoFormRejected": "Your {0} is rejected",
    "SH_MayoFormDeleted": "Your {0} is deleted by the administrator",
    "SH_MayoFormCancellationApproved": "Your {0} cancellation is approved",
    "SH_MayoFormApprovalAwait": "You have a {0} waiting for approval",
    "SH_DataSyncInProgress": "Data syncing in progress. Please try again in a few hours.",
    "SH_ConfirmUpload": "Confirm Upload",
    "SH_RowNo": "Row Number",
    "SH_MayoReport": "MAYO Report",
    "SH_ExpandMenu": "Expand Menu",
    "SH_HideMenu": "Hide Menu",
    "SH_NotLogin": "Anonymous",
    "SH_PageNotFound": "404 Page not found",
    "SH_ServerPageNotFound": "This page is not found on the server."
}
