import { useState } from 'react';
import { Button, CompoundDropdownMenu, CompoundDropdownMenuItem } from '@mayo/mayo-ui-beta/v2';
import { setCookie } from 'domains/shared/utils/cookies';
import { COOKIE_NAMES } from 'domains/xe/config';
import { changeLanguage } from 'domains/xe/i18n';
import Cookies from 'js-cookie';
import { Globe } from 'lucide-react';

const LanguageDropdown = () => {
    // 從 cookie 中讀取當前語系
    const getCurrentLanguage = () => {
        return Cookies.get(COOKIE_NAMES.LOCALE) || 'en';
    };

    const [language, setLanguage] = useState(getCurrentLanguage());

    const handleLanguageChange = async (newLanguage: string) => {
        setLanguage(newLanguage);
        // 使用 setCookie 設定 cookie
        setCookie(COOKIE_NAMES.LOCALE, newLanguage, '.mayohr.com');
        await changeLanguage(newLanguage);
    };

    const menuItems: CompoundDropdownMenuItem[] = [
        { id: 'label-language', type: 'label', label: 'Language Family' },
        { id: 'sep-1', type: 'separator' },
        {
            id: 'radio-language',
            type: 'radioGroup',
            label: 'languageSelection',
            value: language,
            onValueChange: handleLanguageChange,
            items: [
                { id: 'radio-en', label: 'English', value: 'en' },
                { id: 'radio-zh-TW', label: '繁體中文', value: 'zh-TW' },
                { id: 'radio-zh-CN', label: '简体中文', value: 'zh-CN' },
            ],
        },
    ];

    return (
        <CompoundDropdownMenu
            menuItems={menuItems}
            triggerLabel="open"
            dropdownMenuContentProps={{
                className: 'border-sidebar-border',
            }}
            placement="bottom-end"
            triggerProps={{ variant: 'outline', className: 'w-52' }}
            trigger={
                <Button size="icon" variant="default" className="bg-foreground hover:bg-accent">
                    <Globe height={18} width={18} />
                </Button>
            }
        />
    );
};

export default LanguageDropdown;
