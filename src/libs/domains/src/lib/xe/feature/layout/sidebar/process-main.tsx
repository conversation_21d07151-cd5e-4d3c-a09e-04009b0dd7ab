import {
    Collapsible,
    CollapsibleContent,
    CollapsibleTrigger,
    SidebarGroup,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
} from '@mayo/mayo-ui-beta/v2';
import { ChevronRight, LucideIcon } from 'lucide-react';
import { useRouter } from 'nextjs-toploader/app';

interface NavMainProps {
    groupTitle: string;
    items: {
        title: string;
        url: string;
        icon: LucideIcon;
        isActive?: boolean;
        items?: (
            | {
                  title: string;
                  url: string;
              }
            | null
            | false
        )[];
    }[];
}

const ProcessMain = ({ groupTitle, items }: NavMainProps) => {
    const router = useRouter();
    if (items.length === 0) {
        return null;
    }
    return (
        <SidebarGroup>
            {/* <FunctionDropdown /> */}
            <SidebarGroupLabel>{groupTitle}</SidebarGroupLabel>
            <SidebarMenu>
                {/* <SidebarMenuItem>
                    <SidebarMenuButton asChild>
                        <a
                            onClick={() => push(`${prodPath}/xe/foundation/hire`)}
                            className="cursor-pointer"
                        >
                            <Bot />
                            <span>新進人員流程</span>
                        </a>
                    </SidebarMenuButton>
                </SidebarMenuItem> */}
                {items.map((item) => (
                    <Collapsible
                        key={item.title}
                        asChild
                        defaultOpen={item.isActive}
                        className="group/collapsible"
                    >
                        <SidebarMenuItem>
                            <CollapsibleTrigger asChild>
                                <SidebarMenuButton tooltip={item.title}>
                                    {item.icon && <item.icon />}
                                    <span>{item.title}</span>
                                    <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                                </SidebarMenuButton>
                            </CollapsibleTrigger>
                            <CollapsibleContent>
                                <SidebarMenuSub>
                                    {item.items?.map((subItem) => {
                                        if (!subItem) {
                                            return null;
                                        }
                                        return (
                                            <SidebarMenuSubItem key={subItem.title}>
                                                <SidebarMenuSubButton
                                                    asChild
                                                    className="cursor-pointer"
                                                >
                                                    <a onClick={() => router.push(subItem.url)}>
                                                        <span>{subItem.title}</span>
                                                    </a>
                                                </SidebarMenuSubButton>
                                            </SidebarMenuSubItem>
                                        );
                                    })}
                                </SidebarMenuSub>
                            </CollapsibleContent>
                        </SidebarMenuItem>
                    </Collapsible>
                ))}
            </SidebarMenu>
        </SidebarGroup>
    );
};

export default ProcessMain;
