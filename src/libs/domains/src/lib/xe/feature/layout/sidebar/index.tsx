import { SidebarInset, SidebarProvider } from '@mayo/mayo-ui-beta/v2';
import Head from 'next/head';

import { AppSidebar } from './app-sidebar';
import { SiteHeader } from './site-header';

function Sidebar({ children }: { children: React.ReactNode }) {
    return (
        <SidebarProvider className="">
            <Head>
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
                />
            </Head>
            <SiteHeader />
            <div className="flex flex-1">
                <AppSidebar />
                <SidebarInset>
                    <div className="h-[calc(100vh-55px)] bg-gray-50 p-6">
                        {children}

                        <footer className="py-4">
                            <p className="text-end text-sm text-gray-500">
                                Copyright © 2025 MAYO Human Capital Inc.{' '}
                                <span className="sm:inline hidden"> </span>
                                <span className="block sm:inline">All rights reserved.</span>
                            </p>
                        </footer>
                    </div>
                </SidebarInset>
            </div>
        </SidebarProvider>
    );
}

export default Sidebar;
