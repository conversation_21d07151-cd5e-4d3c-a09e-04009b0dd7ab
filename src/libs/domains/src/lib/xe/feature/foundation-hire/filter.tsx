import { useState } from 'react';
import {
    Button,
    Input,
    Popover,
    PopoverContent,
    PopoverTrigger,
    Select,
} from '@mayo/mayo-ui-beta/v2';
import { Settings2 } from 'lucide-react';

const Filter = () => {
    const [keywordType, setKeywordType] = useState<string | null>(null);

    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button size="md" variant="ghost" color="secondary">
                    <div className="flex items-center">
                        <Settings2 className="mr-2" height={16} width={16} />
                        <span className="whitespace-nowrap">篩選</span>
                    </div>
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-[320px] p-4">
                <div className="space-y-4">
                    <h3 className="text-base font-medium">篩選資料</h3>

                    <div className="space-y-4">
                        <div>
                            <label className="text-sm text-gray-600 mb-1.5 block">狀態</label>
                            <Select
                                options={[
                                    { label: '未報到', value: 'not_arrived' },
                                    { label: '已報到', value: 'arrived' },
                                    { label: '已取消', value: 'cancelled' },
                                ]}
                                placeholder="未報到"
                                className="w-full"
                            />
                        </div>

                        <div className="space-y-2">
                            <label className="text-sm text-gray-600 mb-1.5 block">關鍵字</label>
                            <Select
                                options={[
                                    { label: '姓名', value: 'name' },
                                    { label: '單位', value: 'department' },
                                    { label: '主管', value: 'supervisor' },
                                ]}
                                placeholder="請選擇關鍵字類別"
                                className="w-full"
                                onChange={(value: string | null) => setKeywordType(value)}
                            />
                            <Input
                                placeholder="請先選擇類別，再輸入關鍵字"
                                className="w-full bg-gray-50"
                                disabled={!keywordType}
                            />
                        </div>
                    </div>

                    <div className="flex justify-end gap-2 pt-2">
                        <Button
                            variant="outline"
                            size="sm"
                            className="min-w-[80px]"
                            onClick={() => {
                                // 處理清空
                            }}
                        >
                            清空
                        </Button>
                        <Button
                            size="sm"
                            className="min-w-[80px]"
                            onClick={() => {
                                // 處理搜尋
                            }}
                        >
                            搜尋
                        </Button>
                    </div>
                </div>
            </PopoverContent>
        </Popover>
    );
};

export default Filter;
