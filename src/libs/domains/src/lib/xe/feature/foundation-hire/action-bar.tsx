import {
    ActionBarCloseTrigger,
    ActionBarContent,
    ActionBarPositioner,
    ActionBarRoot,
    ActionBarSelectionTrigger,
    ActionBarSeparator,
    Button,
} from '@mayo/mayo-ui-beta/v2';
import { useAtomValue, useSetAtom } from 'jotai';
import { Download, UserCheck, X } from 'lucide-react';

import { selectedRowsActionsAtom, selectedRowsAtom } from './store';

const ActionBar = () => {
    const selectedRows = useAtomValue(selectedRowsAtom);
    const selectedRowsActions = useSetAtom(selectedRowsActionsAtom);
    const selectedCount = selectedRows.size;

    const handleClearSelection = () => {
        selectedRowsActions({ type: 'deselectAll' });
    };

    return (
        <ActionBarRoot open={selectedCount > 0}>
            <ActionBarPositioner dark>
                <ActionBarContent>
                    <ActionBarSelectionTrigger dark>
                        <div className="flex gap-1 whitespace-nowrap">
                            已勾選 <span className="font-bold text-blue-500">{selectedCount}</span>{' '}
                            筆資料
                        </div>
                    </ActionBarSelectionTrigger>
                    <div className="flex items-center gap-2">
                        <Button
                            variant="default"
                            size="sm"
                            color="primary"
                            className="text-primary-foreground flex items-center"
                        >
                            <Download className="h-4 w-4 mr-2" />
                            下載
                        </Button>
                        <Button
                            variant="default"
                            size="sm"
                            color="primary"
                            className="text-primary-foreground flex items-center"
                        >
                            <UserCheck className="h-4 w-4 mr-2" />
                            報到
                        </Button>
                        <ActionBarSeparator dark />
                        <ActionBarCloseTrigger dark onClick={handleClearSelection}>
                            <X className="h-4 w-4" />
                        </ActionBarCloseTrigger>
                    </div>
                </ActionBarContent>
            </ActionBarPositioner>
        </ActionBarRoot>
    );
};

export default ActionBar;
