import React from 'react';
import { Control, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
    Avatar,
    Button,
    Card,
    CardContent,
    DatePicker,
    DialogComponent,
    Form,
    FormControl,
    FormField,
    FormItemContent,
    Input,
    Select,
} from '@mayo/mayo-ui-beta/v2';
import { SearchIcon } from 'lucide-react';
import { useRouter } from 'nextjs-toploader/app';
import { z } from 'zod';

const NewHireSchema = z.object({
    employeeType: z.number().min(1, { message: '請選擇員工資料建立方式' }),
    arrivalDate: z.string().min(1, { message: '請選擇到職日期' }),
    lastName: z.string().min(1, { message: '請輸入姓氏' }),
    firstName: z.string().min(1, { message: '請輸入名字' }),
    gender: z.string().min(1, { message: '請選擇性別' }),
    employeeId: z.string().min(1, { message: '請輸入身分證字號' }),
});

type ManualNewHireProps = {
    control: Control<z.infer<typeof NewHireSchema>>;
};

const ManualNewHire = ({ control }: ManualNewHireProps) => {
    return (
        <>
            <div className="col-span-1">
                <FormItemContent
                    fieldName="lastName"
                    formLabel="姓"
                    required
                    fieldControl={control}
                    formItemClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="lastName"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            <div className="col-span-1">
                <FormItemContent
                    fieldName="firstName"
                    formLabel="名"
                    required
                    fieldControl={control}
                    formItemClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="firstName"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            <div className="col-span-1">
                <FormItemContent
                    fieldName="gender"
                    formLabel="性別"
                    required
                    fieldControl={control}
                    formItemClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="gender"
                            render={({ field }) => (
                                <FormControl>
                                    <Select
                                        placeholder="請選擇"
                                        value={
                                            [
                                                { value: 'male', label: '男' },
                                                {
                                                    value: 'female',
                                                    label: '女',
                                                },
                                            ].find((option) => option.value === field?.value) ||
                                            null
                                        }
                                        onChange={(option: any) => {
                                            console.log(option);
                                            field.onChange(option?.value || '');
                                        }}
                                        options={[
                                            { value: 'male', label: '男' },
                                            { value: 'female', label: '女' },
                                        ]}
                                        isPortal={true}
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>

            <div className="col-span-1">
                <FormItemContent
                    fieldName="employeeId"
                    formLabel="身分證件號碼"
                    required
                    fieldControl={control}
                    formItemClassName="w-full"
                    itemComponent={
                        <FormField
                            control={control}
                            name="employeeId"
                            render={({ field }) => (
                                <FormControl>
                                    <Input
                                        placeholder="請輸入"
                                        value={field.value}
                                        onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                                            field.onChange(e.target.value)
                                        }
                                    />
                                </FormControl>
                            )}
                        />
                    }
                />
            </div>
        </>
    );
};

type TalentPoolItemProps = {
    name: string;
    phone: string;
    email: string;
    education: {
        degree: string;
        school: string;
    };
    experience: {
        company: string;
        position: string;
        department: string;
        years: number;
    };
};

const TalentPoolItem = ({ name, phone, email, education, experience }: TalentPoolItemProps) => {
    return (
        <Card bordered className="col-span-1 hover:border-primary-lighter cursor-pointer">
            <CardContent>
                <div className="flex flex-col gap-2">
                    <div className="flex justify-between items-start">
                        <div>
                            <Avatar alt="avatar" src="https://i.pravatar.cc/300" size="lg" />
                        </div>
                        <div className="flex flex-col mt-1">
                            <div className="text-md font-medium">{name}</div>
                            <div className="text-sm text-gray-500">{phone}</div>
                            <div className="text-sm text-gray-500">{email}</div>
                        </div>
                        <div>
                            <Button variant="link" size="sm">
                                檢視履歷
                            </Button>
                        </div>
                    </div>
                    <Card className="bg-gray-50">
                        <CardContent>
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <div className="text-gray-500 text-sm">最高學歷</div>

                                    <div className="text-sm">{education.degree}</div>
                                </div>
                                <div className="space-y-2">
                                    <div className="text-gray-500 text-sm">學校名稱</div>

                                    <div className="text-sm">{education.school}</div>
                                </div>
                                <div className="space-y-2">
                                    <div className="text-gray-500 text-sm">最近公司工作</div>

                                    <div className="text-sm">{experience.company}</div>
                                </div>
                                <div className="space-y-2">
                                    <div className="text-gray-500 text-sm">最近公司職稱</div>

                                    <div className="text-sm">{experience.position}</div>
                                </div>
                                <div className="space-y-2">
                                    <div className="text-gray-500 text-sm">工作年資</div>

                                    <div className="text-sm">{experience.years} 年</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </CardContent>
        </Card>
    );
};

const TalentPool = () => {
    const mockData: TalentPoolItemProps[] = [
        {
            name: '林詩琪',
            phone: '0912345678',
            email: '<EMAIL>',
            education: {
                degree: '碩士',
                school: '國立臺灣科技大學',
            },
            experience: {
                company: '嘉晟工作公司',
                position: '資深專員',
                department: '嘉晟工作組',
                years: 7,
            },
        },
        {
            name: '王小予',
            phone: '0912123456',
            email: '<EMAIL>',
            education: {
                degree: '碩士',
                school: '國立中正大學',
            },
            experience: {
                company: '嘉晟工作公司',
                position: '專員',
                department: '嘉晟工作組',
                years: 2,
            },
        },
        {
            name: '陳柏凱',
            phone: '0987123456',
            email: '<EMAIL>',
            education: {
                degree: '學士',
                school: '國立臺灣大學',
            },
            experience: {
                company: '嘉晟工作公司',
                position: '專員',
                department: '嘉晟工作組',
                years: 3,
            },
        },
        {
            name: '吳若君',
            phone: '0934567890',
            email: '<EMAIL>',
            education: {
                degree: '學士',
                school: '國立政治大學',
            },
            experience: {
                company: '嘉晟工作公司',
                position: '專員',
                department: '嘉晟工作組',
                years: 1,
            },
        },
    ];

    return (
        <div className="col-span-2">
            <div className="space-y-4 max-h-[500px] scrollbar-none overflow-y-auto grid grid-cols-2 gap-4">
                <div className="col-span-1">
                    <Input
                        placeholder="搜尋身分證編號 / 姓名"
                        className="pl-10"
                        suffixIcon={<SearchIcon size={16} />}
                        showSuffixIcon
                    />
                </div>
                <div className="col-span-1" />

                {mockData.map((item, index) => (
                    <TalentPoolItem key={index} {...item} />
                ))}
            </div>
        </div>
    );
};

const NewHireButton = () => {
    const router = useRouter();
    const form = useForm<z.infer<typeof NewHireSchema>>({
        resolver: zodResolver(NewHireSchema),
        defaultValues: {
            employeeType: 1, // 1: 手動新增, 2: 從人才庫匯入
            arrivalDate: new Date().toDateString(),
            lastName: '',
            firstName: '',
            gender: '',
            employeeId: '',
        },
        mode: 'onChange',
        reValidateMode: 'onChange',
    });

    const values = form.watch();

    const onSubmit = (data: z.infer<typeof NewHireSchema>) => {
        console.log('submitData: ', data);
        router.push('/xe/foundation/hire/personalconfirm');
    };

    return (
        <DialogComponent
            size="lg"
            title="新增人員"
            confirmLabel="確定"
            cancelLabel="取消"
            onConfirmClick={() => {
                // 手動增新人員時才需要驗證表單
                if (values.employeeType === 2) {
                    form.trigger().then((isValid) => {
                        if (isValid) {
                            onSubmit(form.getValues());
                        }
                    });
                } else {
                    onSubmit(form.getValues());
                }
            }}
            onCancelClick={() => {
                form.reset();
                // close dialog
            }}
            triggerComponent={
                <Button variant="default" size="md">
                    新增
                </Button>
            }
        >
            <Form {...form}>
                <form
                    className="flex flex-col space-y-4 p-4 w-full"
                    onSubmit={form.handleSubmit(onSubmit)}
                >
                    <div className="grid grid-cols-2 gap-4">
                        <div className="col-span-1">
                            <FormItemContent
                                fieldName="arrivalDate"
                                formLabel="到職日"
                                required
                                fieldControl={form.control}
                                formItemClassName="w-full"
                                itemComponent={
                                    <FormField
                                        control={form.control}
                                        name="arrivalDate"
                                        render={({ field }) => (
                                            <FormControl>
                                                <DatePicker
                                                    className="w-full"
                                                    value={field.value}
                                                    onChange={(date) => field.onChange(date)}
                                                />
                                            </FormControl>
                                        )}
                                    />
                                }
                            />
                        </div>

                        {/* 選擇員工資料建立方式 */}
                        <div className="col-span-2">
                            <FormItemContent
                                fieldName="employeeType"
                                formLabel="請選擇員工資料建立方式"
                                required
                                fieldControl={form.control}
                                formItemClassName="w-full"
                                itemComponent={
                                    <FormField
                                        control={form.control}
                                        name="employeeType"
                                        render={({ field }) => (
                                            <div className="flex justify-center gap-4">
                                                <Button
                                                    variant="outline"
                                                    color={
                                                        values.employeeType === 1
                                                            ? 'primary'
                                                            : 'secondary'
                                                    }
                                                    className="w-1/2"
                                                    type="button"
                                                    size="lg"
                                                    onClick={() => {
                                                        form.reset();
                                                        field.onChange(1);
                                                    }}
                                                >
                                                    <span className="flex items-center">
                                                        自人才庫匯入
                                                    </span>
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    color={
                                                        values.employeeType === 2
                                                            ? 'primary'
                                                            : 'secondary'
                                                    }
                                                    className="w-1/2"
                                                    type="button"
                                                    size="lg"
                                                    onClick={() => {
                                                        form.reset();
                                                        field.onChange(2);
                                                    }}
                                                >
                                                    <span className="flex items-center">
                                                        手動新增人員
                                                    </span>
                                                </Button>
                                            </div>
                                        )}
                                    />
                                }
                            />
                        </div>

                        <hr className="col-span-2 my-4" />

                        {values.employeeType === 1 && <TalentPool />}

                        {values.employeeType === 2 && <ManualNewHire control={form.control} />}
                    </div>
                </form>
            </Form>
        </DialogComponent>
    );
};

export default NewHireButton;
