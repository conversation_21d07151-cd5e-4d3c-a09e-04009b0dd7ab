import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontent, DateRangePicker, toast } from '@mayo/mayo-ui-beta/v2';
import { BreadcrumbItem, Breadcrumbs } from 'domains/shared/components';
import { X } from 'lucide-react';

import Filter from './filter';
import HireTable from './hire-table';
import NewHireButton from './new-hire-button';

const breadcrumbItems: BreadcrumbItem[] = [
    { label: '流程專區', href: '' },
    { label: '人事異動', href: '' },
    { label: '新進人員', href: '/foundation/hire', isCurrent: true },
];

export function FoundationHire() {
    return (
        <div className="flex flex-col gap-4">
            <Breadcrumbs items={breadcrumbItems} />
            <div className="flex flex-col gap-4">
                <h1 className="text-xl font-bold">新進人員</h1>

                <Card>
                    <CardContent className="flex flex-col gap-4">
                        <div className="flex flex-col gap-4">
                            {/* 頂部操作列 */}
                            <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                    <DateRangePicker />

                                    <Filter />

                                    <div>
                                        <Badge color="grey" variant="default">
                                            <div className="flex items-center gap-2 whitespace-nowrap">
                                                <span>13 筆結果</span>
                                                <X className="cursor-pointer" size={14} />
                                            </div>
                                        </Badge>
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        size="md"
                                        onClick={() =>
                                            toast('即將推出', {
                                                position: 'bottom-right',
                                                icon: '🚀',
                                            })
                                        }
                                    >
                                        Excel 匯出下載
                                    </Button>

                                    <Button
                                        variant="outline"
                                        size="md"
                                        onClick={() =>
                                            toast('即將推出', {
                                                position: 'bottom-right',
                                                icon: '🚀',
                                            })
                                        }
                                    >
                                        批次匯入
                                    </Button>

                                    <NewHireButton />
                                </div>
                            </div>
                        </div>
                        <HireTable />
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}

export default FoundationHire;
