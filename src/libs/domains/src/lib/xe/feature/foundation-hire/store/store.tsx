import { atom } from 'jotai';

// 定義選中行的 atom
export const selectedRowsAtom = atom<Set<string>>(new Set<string>());

// 定義操作 selectedRows 的 atom
export const selectedRowsActionsAtom = atom(
    (get) => get(selectedRowsAtom),
    (
        get,
        set,
        action: {
            type: 'select' | 'deselect' | 'selectAll' | 'deselectAll';
            id?: string;
            ids?: string[];
        }
    ) => {
        const currentSelected = new Set(get(selectedRowsAtom));

        switch (action.type) {
            case 'select':
                if (action.id) {
                    currentSelected.add(action.id);
                }
                break;
            case 'deselect':
                if (action.id) {
                    currentSelected.delete(action.id);
                }
                break;
            case 'selectAll':
                if (action.ids) {
                    action.ids.forEach((id) => currentSelected.add(id));
                }
                break;
            case 'deselectAll':
                currentSelected.clear();
                break;
        }

        set(selectedRowsAtom, currentSelected);
    }
);
