import { useState } from 'react';
import { Button, Checkbox, ColumnDef, DataTable } from '@mayo/mayo-ui-beta/v2';
import { useAtomValue, useSetAtom } from 'jotai';
import { ArrowUpDown, Edit, Trash, User } from 'lucide-react';

import ActionBar from './action-bar';
import mock from './mock.json';
import { selectedRowsActionsAtom, selectedRowsAtom } from './store';

// 定義資料型別
interface Employee {
    id: string;
    date: string;
    name: string;
    department: string;
    supervisor: string;
    type: string;
    location: string;
    progress: number;
}

const HireTable = () => {
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const selectedRows = useAtomValue(selectedRowsAtom);
    const selectedRowsActions = useSetAtom(selectedRowsActionsAtom);

    const [hireData] = useState<Employee[]>(mock.hireData);

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            selectedRowsActions({ type: 'selectAll', ids: hireData.map((row) => row.id) });
        } else {
            selectedRowsActions({ type: 'deselectAll' });
        }
    };

    const handleSelectRow = (id: string, checked: boolean) => {
        if (checked) {
            selectedRowsActions({ type: 'select', id });
        } else {
            selectedRowsActions({ type: 'deselect', id });
        }
    };

    const columns: ColumnDef<Employee>[] = [
        {
            id: 'select',
            enableResizing: false,
            size: 40,
            header: () => (
                <Checkbox
                    checked={selectedRows.size === hireData.length}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                />
            ),
            cell: ({ row }) => (
                <Checkbox
                    checked={selectedRows.has(row.original.id)}
                    onCheckedChange={(checked: boolean) =>
                        handleSelectRow(row.original.id, checked)
                    }
                    aria-label="Select row"
                />
            ),
            enableSorting: false,
            enableHiding: false,
        },
        {
            accessorKey: 'date',
            enableResizing: false,
            size: 100,
            header: ({ column }) => {
                return (
                    <div
                        className="flex items-center cursor-pointer whitespace-nowrap"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    >
                        <span>報到日期</span>
                        <ArrowUpDown className="ml-1" size={15} />
                    </div>
                );
            },
            cell: ({ row }) => <div className="whitespace-nowrap">{row.getValue('date')}</div>,
        },
        {
            accessorKey: 'name',
            enableResizing: false,
            size: 100,
            header: ({ column }) => {
                return (
                    <div
                        className="flex items-center cursor-pointer whitespace-nowrap"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    >
                        <span>姓名</span>
                        <ArrowUpDown className="ml-1" size={15} />
                    </div>
                );
            },
            cell: ({ row }) => <div className="whitespace-nowrap">{row.getValue('name')}</div>,
        },
        {
            accessorKey: 'department',
            enableResizing: false,
            size: 150,
            header: ({ column }) => {
                return (
                    <div
                        className="flex items-center cursor-pointer whitespace-nowrap"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    >
                        <span>單位名稱</span>
                        <ArrowUpDown className="ml-1" size={15} />
                    </div>
                );
            },
            cell: ({ row }) => (
                <div className="whitespace-nowrap">{row.getValue('department')}</div>
            ),
        },
        {
            accessorKey: 'supervisor',
            enableResizing: false,
            size: 100,
            header: ({ column }) => {
                return (
                    <div
                        className="flex items-center cursor-pointer whitespace-nowrap"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    >
                        <span>直屬主管</span>
                        <ArrowUpDown className="ml-1" size={15} />
                    </div>
                );
            },
            cell: ({ row }) => (
                <div className="whitespace-nowrap">{row.getValue('supervisor')}</div>
            ),
        },
        {
            accessorKey: 'type',
            enableResizing: false,
            size: 100,
            header: ({ column }) => {
                return (
                    <div
                        className="flex items-center cursor-pointer whitespace-nowrap"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    >
                        <span>身份類別</span>
                        <ArrowUpDown className="ml-1" size={15} />
                    </div>
                );
            },
            cell: ({ row }) => <div className="whitespace-nowrap">{row.getValue('type')}</div>,
        },
        {
            accessorKey: 'location',
            enableResizing: false,
            size: 100,
            header: ({ column }) => {
                return (
                    <div
                        className="flex items-center cursor-pointer whitespace-nowrap"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    >
                        <span>地點</span>
                        <ArrowUpDown className="ml-1" size={15} />
                    </div>
                );
            },
            cell: ({ row }) => <div className="whitespace-nowrap">{row.getValue('location')}</div>,
        },
        {
            accessorKey: 'progress',
            enableResizing: false,
            size: 200,
            header: ({ column }) => {
                return (
                    <div
                        className="flex items-center cursor-pointer whitespace-nowrap"
                        onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
                    >
                        <span>資料完成度</span>
                        <ArrowUpDown className="ml-1" size={15} />
                    </div>
                );
            },
            cell: ({ row }) => (
                <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${row.getValue('progress')}%` }}
                    />
                </div>
            ),
        },
        {
            id: 'actions',
            header: () => <div className="whitespace-nowrap ml-2">功能</div>,
            enableResizing: false,
            size: 120,
            meta: {
                pin: 'right',
            },
            cell: () => (
                <div className="flex items-center gap-1">
                    <Button variant="ghost" color="secondary" size="sm">
                        <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" color="secondary" size="sm">
                        <Trash className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" color="secondary" size="sm">
                        <User className="h-4 w-4" />
                    </Button>
                </div>
            ),
        },
    ];

    return (
        <div className="w-full">
            <DataTable
                columns={columns}
                data={hireData}
                page={page}
                pageSize={pageSize}
                totalItems={hireData.length}
                onPageChange={setPage}
                onPageSizeChange={setPageSize}
            />

            <ActionBar />
        </div>
    );
};

export default HireTable;
