import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
    Button,
    Checkbox,
    DatePicker,
    Form,
    FormControl,
    FormField,
    FormItem,
    FormItemContent,
    Input,
    RadioGroupComponent as RadioGroup,
    Switch,
    Textarea,
} from '@mayo/mayo-ui-beta/v2';
import { z } from 'zod';

const CustomFormSchema = z.object({
    username: z.string().min(1, { message: 'You have to input correct username' }),
    birthday: z.string().min(1, { message: 'You have to choose birthday' }),
    gender: z.string().optional(),
    openProfile: z.boolean().optional(),
    hobby: z.array(z.string()).refine((value) => value.some((item) => item), {
        message: 'You have to select at least one item.',
    }),
    id: z.string().min(1, { message: 'You have to upload the id file' }),
    remark: z.string().optional(),
});

const DemoForm = () => {
    const form = useForm<z.infer<typeof CustomFormSchema>>({
        resolver: zodResolver(CustomFormSchema),
        defaultValues: {
            username: '',
            birthday: new Date().toDateString(),
            gender: 'male',
            openProfile: true,
            hobby: [],
            id: '',
        },
    });

    const hobbyItems = [
        {
            value: 'cooking',
            label: 'Cooking',
        },
        {
            value: 'singing',
            label: 'Singing',
        },
        {
            value: 'dancing',
            label: 'Dancing',
        },
        {
            value: 'baseball',
            label: 'Baseball',
        },
        {
            value: 'basketball',
            label: 'Basketball',
        },
        {
            value: 'boxing',
            label: 'Boxing',
        },
        {
            value: 'fishing',
            label: 'Fishing',
        },
        {
            value: 'hiking',
            label: 'Hiking',
        },
        {
            value: 'swimming',
            label: 'Swimming',
        },
        {
            value: 'surfing',
            label: 'Surfing',
        },
        {
            value: 'tennis',
            label: 'Tennis',
        },
        {
            value: 'volleyball',
            label: 'Volleyball',
        },
        {
            value: 'workout',
            label: 'Work Out',
        },
        {
            value: 'blogging',
            label: 'Blogging',
        },
        {
            value: 'photography',
            label: 'Photography',
        },
    ];

    const onSubmit = (data: z.infer<typeof CustomFormSchema>) => {
        console.log('submitData: ', data);
    };

    return (
        <Form {...form}>
            <form
                className="flex flex-col p-6 rounded-lg border border-border-checkbox"
                onSubmit={form.handleSubmit(onSubmit)}
            >
                <div className="flex justify-between pb-5">
                    <FormItemContent
                        fieldName="username"
                        formDescription="Enter your username to display in the custom form."
                        formItemClassName="min-w-[440px]"
                        formLabel="Username"
                        fieldControl={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="username"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            showMaxLength
                                            maxLength={30}
                                            value={field.value}
                                            onChange={(
                                                event: React.ChangeEvent<HTMLInputElement>
                                            ) => field.onChange(event.target.value)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        fieldName="birthday"
                        formDescription="Select your birthday to display in the custom form."
                        formItemClassName="min-w-[440px]"
                        formLabel="Birthday"
                        fieldControl={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="birthday"
                                render={({ field }) => (
                                    <FormControl>
                                        <DatePicker
                                            className="!min-w-[440px]"
                                            value={field.value}
                                            onChange={(date) => field.onChange(date)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div className="flex justify-between pb-5">
                    <FormItemContent
                        fieldName="gender"
                        formDescription="Pick your gender to display in the custom form."
                        formItemClassName="min-w-[440px]"
                        formLabel="Gender"
                        fieldControl={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="gender"
                                render={({ field }) => (
                                    <FormControl>
                                        <RadioGroup
                                            isHorizontal
                                            radioOptions={[
                                                {
                                                    value: 'male',
                                                    label: 'Male',
                                                },
                                                {
                                                    value: 'female',
                                                    label: 'Female',
                                                },
                                                {
                                                    value: 'unknown',
                                                    label: 'Prefer not to say',
                                                },
                                            ]}
                                            onValueChange={(value) => field.onChange(value)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                    <FormItemContent
                        fieldName="openProfile"
                        formItemClassName="min-w-[440px]"
                        formLabel="Open Profile"
                        fieldControl={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="openProfile"
                                render={({ field }) => (
                                    <FormControl>
                                        <Switch
                                            checked={field.value}
                                            onCheckedChange={(check) => field.onChange(check)}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div className="pb-5">
                    <FormItemContent
                        fieldName="hobby"
                        formDescription="Pick your hobby to display in the custom form."
                        formItemClassName="min-w-[880px]"
                        formLabel="Hobby"
                        fieldControl={form.control}
                        itemComponent={
                            <div className="flex flex-wrap gap-y-4">
                                {hobbyItems.map((item) => (
                                    <FormField
                                        key={item.value}
                                        control={form.control}
                                        name="hobby"
                                        render={({ field }) => {
                                            return (
                                                <FormItem
                                                    key={item.value}
                                                    className="flex items-center space-x-1 space-y-0 pr-8"
                                                >
                                                    <FormControl>
                                                        <Checkbox
                                                            checked={
                                                                field.value.length > 0 &&
                                                                field.value.includes(item.value)
                                                            }
                                                            label={item.label}
                                                            onValueChange={(checked) => {
                                                                return checked
                                                                    ? field.onChange([
                                                                          ...field.value,
                                                                          item.value,
                                                                      ])
                                                                    : field.onChange(
                                                                          field.value?.filter(
                                                                              (value) =>
                                                                                  value !==
                                                                                  item.value
                                                                          )
                                                                      );
                                                            }}
                                                        />
                                                    </FormControl>
                                                </FormItem>
                                            );
                                        }}
                                    />
                                ))}
                            </div>
                        }
                    />
                </div>
                <div className="pb-5">
                    <FormItemContent
                        fieldName="id"
                        formDescription="Select your id file to display in the custom form."
                        formItemClassName="min-w-[440px]"
                        formLabel="ID"
                        fieldControl={form.control}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="id"
                                render={({ field }) => (
                                    <FormControl>
                                        <Input
                                            accept=".jpg, .jpeg, .png, .gif"
                                            type="file"
                                            filename={field.value}
                                            onChange={(
                                                event: React.ChangeEvent<HTMLInputElement>
                                            ) => {
                                                const files = event.target.files;
                                                if (files && files.length > 0) {
                                                    field.onChange(files[0].name);
                                                } else {
                                                    field.onChange('');
                                                }
                                            }}
                                        />
                                    </FormControl>
                                )}
                            />
                        }
                    />
                </div>
                <div className="pb-5">
                    <FormItemContent
                        fieldName="remark"
                        formDescription="Enter the remark if you want to display in the custom form."
                        formItemClassName="min-w-[880px]"
                        formLabel="Remark"
                        fieldControl={form.control}
                        required={false}
                        itemComponent={
                            <FormField
                                control={form.control}
                                name="remark"
                                render={({ field }) => {
                                    return (
                                        <FormControl>
                                            <Textarea
                                                className="min-w-[880px]"
                                                value={field.value}
                                                onChange={(
                                                    event: React.ChangeEvent<HTMLTextAreaElement>
                                                ) => field.onChange(event.target.value)}
                                            />
                                        </FormControl>
                                    );
                                }}
                            />
                        }
                    />
                </div>
                <div className="flex justify-end mt-6">
                    <Button>Submit</Button>
                </div>
            </form>
        </Form>
    );
};

export default DemoForm;
