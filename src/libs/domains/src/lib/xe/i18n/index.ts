import { createInstance } from 'i18next';

import { COOKIE_NAMES, defaultLocale } from '../config';
// 導入語系檔案
import enUS from './en-us.json';
import zhCN from './zh-cn.json';
import zhTW from './zh-tw.json';

// 建立一個函數來獲取當前語系
const getCurrentLocale = () => {
    // 在客戶端使用普通的 document.cookie
    if (typeof window !== 'undefined') {
        const cookies = document.cookie.split(';');
        const localeCookie = cookies.find((cookie) =>
            cookie.trim().startsWith(`${COOKIE_NAMES.LOCALE}=`)
        );
        if (localeCookie) {
            const locale = localeCookie.split('=')[1];
            return locale;
        }
    }

    return defaultLocale;
};

const i18n = createInstance({
    fallbackLng: defaultLocale,
    debug: process.env.NODE_ENV === 'development',
    lng: getCurrentLocale(),
    interpolation: {
        escapeValue: false,
    },
    nonExplicitSupportedLngs: true,
    resources: {
        en: {
            translation: enUS,
        },
        'zh-CN': {
            translation: zhCN,
        },
        'zh-TW': {
            // 使用 zh-TW 作為語言代碼
            translation: zhTW,
        },
    },
});

// 提供切換語言的方法，同時更新 cookie
export const changeLanguage = async (locale: string) => {
    // 更新 i18n 的語言
    await i18n.changeLanguage(locale);

    // 更新 cookie
    if (typeof window !== 'undefined') {
        const date = new Date();
        date.setFullYear(date.getFullYear() + 1); // 設定 cookie 一年後過期

        document.cookie = `${COOKIE_NAMES.LOCALE}=${locale}; expires=${date.toUTCString()}; path=/`;
    }
};

i18n.init();

export default i18n;
