import prodPath from '../shared/utils/prodPath';

type Locale = (typeof locales)[number];

const locales = ['zh-tw', 'en-us'] as const;
const defaultLocale: Locale = 'zh-tw';

const COOKIE_NAMES = {
    LOCALE: 'locale',
    AUTH: 'auth',
    I18next: 'i18next',
    REFRESH_TOKEN: 'refreshToken',
    BACKEND_SERVER: 'backendServer',
    BPM_BACKEND_SERVER: 'bpmBackendServer',
};

const WHITE_LIST = [
    '/login',
    `${prodPath}/error/403`,
    `${prodPath}/error/404`,
    `${prodPath}/error/500`,
];

// TODO: asia 導入時， config 要改道 shared 中共用並根據 platform 導出對應的 config
export type { Locale };
export { locales, defaultLocale, COOKIE_NAMES, WHITE_LIST };
