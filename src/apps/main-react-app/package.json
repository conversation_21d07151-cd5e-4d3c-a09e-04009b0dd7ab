{"name": "main-react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "rm -rf dist && tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@mayo/mayo-ui": "^0.0.67", "@micro-zoe/micro-app": "1.0.0-rc.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-basic-ssl": "^1.0.1", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.15", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.29", "tailwindcss": "^3.3.3", "usehooks-ts": "3.1.0", "typescript": "^5.0.2", "vite": "^4.4.5"}}