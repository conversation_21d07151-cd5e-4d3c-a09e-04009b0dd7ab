import { lazy, Suspense } from 'react';
import { RouterProvider, createBrowserRouter } from 'react-router-dom';
import { Loading } from '@mayo/mayo-ui';
import Layout from './layout';

const ViteApp = lazy(() => import(/* ChunkName: "ViteApp" */ './packages/vite-app-page'));

const router = createBrowserRouter([
    {
        path: '/',
        element: <Layout />,
        children: [
            {
                path: 'vite-page/*',
                element: (
                    <Suspense fallback={<Loading />}>
                        <ViteApp />
                    </Suspense>
                ),
            },
        ],
    },
]);

function App() {
    return <RouterProvider router={router} />;
}

export default App;
