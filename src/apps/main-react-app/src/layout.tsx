import { Outlet, useNavigate } from 'react-router-dom';
import microApp from '@micro-zoe/micro-app';
import { Layout as LayoutComponent, SideBarComponent } from '@mayo/mayo-ui';
const { router } = microApp;
const data = [
    {
        items: [
            {
                title: '個人專區',
                matchPath: '/ta/personal/checkin',
                onMenuClick: () => console.log('個人專區'),
                items: [
                    {
                        title: 'Hire',
                        matchPath: '/ta/personal/checkin/checkinrecords/workrecords',
                        onMenuClick: () => console.log('打卡紀錄'),
                        items: [
                            {
                                title: 'Hire列表',
                                matchPath: '/ta/personal/checkin/checkinrecords/workrecords/detail',
                                onMenuClick: () => {
                                    router.push({ name: 'app1', path: '/vite-page/a_page' });
                                },
                            },
                        ],
                    },
                    {
                        title: 'People',
                        matchPath: '/ta/personal/checkin/checkinrecords/oddrecords',
                        onMenuClick: () => {
                            router.push({ name: 'app1', path: '/vite-page/b_page' });
                        },
                    },
                ],
            },
        ],
    },
];
// Layout.js
export default function Layout() {
    const navigate = useNavigate();
    return (
        <LayoutComponent
            onSearch={() => {}}
            navItems={[
                {
                    id: 1,
                    tab: '首頁',
                    onClick: () => {
                        navigate('/');
                    },
                },
                {
                    id: 2,
                    tab: 'FD模組',
                    onClick: () => {
                        navigate('/vite-page');
                    },
                },
            ]}
            menuItems={[]}
            sideBar={<SideBarComponent data={data} />}
        >
            <Outlet />
        </LayoutComponent>
    );
}
