{
    "plugins": ["simple-import-sort"],
    "extends": ["next", "next/core-web-vitals", "airbnb", "prettier"],
    "overrides": [
        // * 默認的 parser of ESLint，JS檔案 不使用 next/core-web-vitals，而是使用 espree 來避免錯誤
        // * https://github.com/vercel/next.js/issues/40687#issuecomment-1378845989
        {
            "files": ["*.js"],
            "parser": "espree",
            "parserOptions": {
                "ecmaVersion": 2020
            }
        }
    ],
    "rules": {
        "react/no-unused-prop-types": "off",
        "consistent-return": "off",
        "no-unreachable": "off",
        "no-console": "off",
        "react/jsx-props-no-spreading": "off",
        "camelcase": "off",
        "react/require-default-props": "off",
        "no-param-reassign": "off",
        "global-require": "off",
        "react/react-in-jsx-scope": "off",
        "no-use-before-define": "off",
        "default-param-last": "off",
        "simple-import-sort/imports": "warn",
        "simple-import-sort/exports": "warn",
        "import/first": "error",
        "import/newline-after-import": "error",
        "import/no-duplicates": "error",
        "import/extensions": "off",
        "import/no-unresolved": "off",
        "no-unused-vars": "off",
        "no-underscore-dangle": "off",
        "import/prefer-default-export": "off",
        "react/jsx-filename-extension": [1, { "extensions": [".js", ".jsx", ".ts", ".tsx"] }],
        "react/function-component-definition": "off",
        "no-shadow": "off",
        "@typescript-eslint/no-shadow": "off",
        "react-hooks/exhaustive-deps": "off",
        "jsx-a11y/label-has-associated-control": "off"
    }
}
