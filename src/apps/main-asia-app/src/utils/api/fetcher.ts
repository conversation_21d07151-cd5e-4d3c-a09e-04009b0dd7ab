import { COOKIE_NAMES, defaultLocale } from 'asia/utils/config';
import axios, { AxiosResponse } from 'axios';
import Cookies from 'js-cookie';

const axiosInstance = axios.create({
    withCredentials: true,
    responseType: 'json',
    headers: {
        'Accept-Language': Cookies.get(COOKIE_NAMES.LOCALE) || defaultLocale,
        'Content-Type': 'application/json',
    },
});

axiosInstance.defaults.headers.post['Content-Type'] = 'application/json';
axiosInstance.defaults.headers.put['Content-Type'] = 'application/json';
axiosInstance.defaults.headers.delete['Content-Type'] = 'application/json';

axiosInstance.interceptors.request.use((config) => {
    // doc: 在 client side run time 取得 backend server
    if (typeof window !== 'undefined' && !config.baseURL) {
        const clientBaseURL = Cookies.get(COOKIE_NAMES.BACKEND_SERVER) || undefined;
        config.baseURL = clientBaseURL;
    }

    return config;
});

export default axiosInstance;

export const get = async <T>(url: string): Promise<T> => {
    const response: AxiosResponse<T> = await axiosInstance.get<T>(url);
    return response?.data;
};
