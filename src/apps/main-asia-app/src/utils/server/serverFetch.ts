import { COOKIE_NAMES, defaultLocale } from 'asia/utils/config';
import { cookies, headers } from 'next/headers';

import { axiosInstance } from '../api';

export const getHeaders = () => {
    const _headers: Record<string, string> = {}; // 改為普通對象而不是 AxiosHeaders
    const cookie = headers().get('Cookie') || '';
    const locale = cookies().get(COOKIE_NAMES.LOCALE)?.value || defaultLocale;

    _headers['Accept-Language'] = locale;

    if (cookie) {
        _headers.Cookie = cookie;
    }
    return _headers;
};

export const serverFetch = async <T>(url: string, method?: 'GET'): Promise<T> => {
    const res = await axiosInstance.get(url, {
        headers: getHeaders(),
        method,
        baseURL: process.env.SERVER_ENV_BACKEND_SERVER,
    });

    return res.data;
};
