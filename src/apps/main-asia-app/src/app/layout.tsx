import './globals.css';
import '@mayo/mayo-ui-beta/dist/apollo.css';

import { JotaiProvider } from 'asia/lib/jotai/store';
import prodPath from 'asia/utils/prodPath';
import type { Metadata } from 'next';
import { Noto_Sans } from 'next/font/google';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';
import NextTopLoader from 'nextjs-toploader';
import React, { Suspense } from 'react';

import SetEnv from '@/components/SetEnv';
import { Envs } from '@/types/types/common';

import SystemRoot from '../system-root';
import Loading from './loading';

const notosans = Noto_Sans({ weight: '400', style: 'normal', subsets: ['latin'] });

export const metadata: Metadata = {
    title: 'Apollo HR Express Edition',
    description: '',
    icons: {
        icon: `${prodPath}/images/favicon.ico`,
    },
};

export default async function RootLayout({ children }: { children: React.ReactNode }) {
    const locale = await getLocale();
    const messages = await getMessages();

    // run time env variables
    const serverEnvs: Envs = {
        PLATFORM: process.env.PLATFORM,
        SERVER_ENV_DEVELOPMENT_ENV_VARIABLE: process.env.SERVER_ENV_DEVELOPMENT_ENV_VARIABLE,
        SERVER_ENV_BACKEND_SERVER: process.env.SERVER_ENV_BACKEND_SERVER,
        SERVER_ENV_WEB_SITE: process.env.SERVER_ENV_WEB_SITE,
        SERVER_ENV_AUTH_WEB_SITE: process.env.SERVER_ENV_AUTH_WEB_SITE,
        SERVER_ENV_TUBE_BACKEND_SERVER: process.env.SERVER_ENV_TUBE_BACKEND_SERVER,
        SERVER_ENV_WEBBFF_BACKEND_SERVER: process.env.SERVER_ENV_WEBBFF_BACKEND_SERVER,
        SERVER_ENV_BPM_BACKEND_SERVER: process.env.SERVER_ENV_BPM_BACKEND_SERVER,
    };

    return (
        <html lang={locale}>
            <body className={`${notosans.className} min-w-[1440px]`}>
                <NextTopLoader height={3} crawl zIndex={999999999} showSpinner={false} initialPosition={0.3} />

                <NextIntlClientProvider messages={messages}>
                    <JotaiProvider>
                        <SetEnv serverEnvs={serverEnvs}>
                            <Suspense fallback={<Loading />}>
                                <SystemRoot locale={locale}>{children}</SystemRoot>
                            </Suspense>
                        </SetEnv>
                    </JotaiProvider>
                </NextIntlClientProvider>
            </body>
        </html>
    );
}
