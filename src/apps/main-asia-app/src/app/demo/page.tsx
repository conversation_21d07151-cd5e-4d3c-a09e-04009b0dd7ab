'use client';

import { Button } from '@mayo/mayo-ui-beta/v2';
import Demo from 'asia/feature/demo/demo';
import useCountStore from 'asia/lib/zustand/count';
import Link from 'next/link';
import { useState } from 'react';

const Page = () => {
    // Zustand state
    const count = useCountStore((state: any) => state.count);
    const increment = useCountStore((state: any) => state.increment);
    const decrement = useCountStore((state: any) => state.decrement);

    // Local state
    const [localCount, setLocalCount] = useState(0);

    // Log state changes
    console.log('Global Count:', count);
    console.log('Local Count:', localCount);

    return (
        <div className="">
            {/* Header Section */}
            <header className="mb-8">
                {/* back to main */}
                <Link href="/">
                    <div className="text-2xl font-bold">Back to Main</div>
                </Link>
                <h1 className="text-3xl font-bold text-gray-900">ASIA LIB Demo Page</h1>
                <p className="mt-2 text-gray-600">This is a demonstration of various features and components</p>
            </header>

            {/* Main Content */}
            <main className="space-y-8">
                {/* Demo Component Section */}
                <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <h2 className="mb-4 text-xl font-semibold">Demo Component</h2>
                    <Demo />
                </section>

                {/* Zustand Counter Section */}
                <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <h2 className="mb-4 text-xl font-semibold">Zustand Counter</h2>
                    <div className="space-y-4">
                        <div className="flex items-center gap-4">
                            <p className="text-lg">Global Count: {count}</p>
                            <div className="space-x-2">
                                <Button variant="outline" onClick={() => increment()}>
                                    Increment
                                </Button>
                                <Button variant="outline" onClick={() => decrement()}>
                                    Decrement
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Local State Counter Section */}
                <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <h2 className="mb-4 text-xl font-semibold">Local State Counter</h2>
                    <div className="space-y-4">
                        <div className="flex items-center gap-4">
                            <p className="text-lg">Local Count: {localCount}</p>
                            <div className="space-x-2">
                                <Button variant="outline" onClick={() => setLocalCount((prev) => prev + 1)}>
                                    Increment
                                </Button>
                                <Button variant="outline" onClick={() => setLocalCount((prev) => prev - 1)}>
                                    Decrement
                                </Button>
                                <Button variant="outline" onClick={() => setLocalCount(0)}>
                                    Reset
                                </Button>
                            </div>
                        </div>
                    </div>
                </section>

                {/* Comparison Section */}
                <section className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
                    <h2 className="mb-4 text-xl font-semibold">State Comparison</h2>
                    <div className="space-y-2">
                        <p className="text-gray-600">Global Count (Zustand): {count}</p>
                        <p className="text-gray-600">Local Count (useState): {localCount}</p>
                        <p className="text-gray-600">Difference: {Math.abs(count - localCount)}</p>
                    </div>
                </section>
            </main>
        </div>
    );
};

export default Page;
