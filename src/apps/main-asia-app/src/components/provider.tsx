'use client';

import Layout from 'asia/feature/layout';
import { userAtom } from 'asia/lib/jotai/user';
import { WHITE_LIST } from 'asia/utils/config';
import prodPath from 'asia/utils/prodPath';
import { useGetPersonalPicture } from 'asia/utils/swr/layout/useGetPersonalPicture';
import { useGetUserInfo } from 'asia/utils/swr/layout/useGetUserInfo';
import { useGetUserPermission } from 'asia/utils/swr/layout/useGetUserPermission';
import { AxiosError } from 'axios';
import { useSetAtom } from 'jotai';
import Cookies from 'js-cookie';
import { useSearchParams } from 'next/navigation';
import { useRouter } from 'nextjs-toploader/app';
import React, { useEffect } from 'react';
import { useIsMounted } from 'usehooks-ts';

import { TOKEN_COOKIE } from '@/const';

export default function Providers({
    children,
    isLogin,
}: {
    children: React.ReactNode;
    locale: string;
    isLogin: boolean;
    persistData: {};
}) {
    const router = useRouter();

    const searchParams = useSearchParams();

    const original_target = searchParams.get('original_target');

    const setUser = useSetAtom(userAtom);

    const isMounted = useIsMounted();

    const isWhite = WHITE_LIST.some((path) => window.location.href.includes(path));

    /**
     * * auth 驗證機制倚仗於此 swr hook
     * * revalidateOnFocus: true, // 當頁面 focus 時重新請求，間隔為 2000 ms
     * * refreshInterval: 1000 * 60 * 15, // 15 分鐘重新請求，測試時縮短間隔
     * */
    const { data: userInfo = null, isLoading: isGetUserInfoLoading } = useGetUserInfo(isLogin, {
        shouldRetryOnError: false,
        refreshInterval: 1000 * 60 * 15,
        onError: (err) => {
            const status = (err as AxiosError).response?.status;
            if (/403|401/i.test(String(status))) {
                Cookies.remove(TOKEN_COOKIE, { path: '/', domain: '.mayohr.com' });

                // original_target 判斷需調整，需排除如果已經有 original_target 參數，或 url 為白名單 的情況
                // router.push(`${location.origin}${prodPath}/error/403?original_target=${encodeURIComponent(location.href)}`);
                if (!isWhite) {
                    const url = new URL(`${window.location.origin}${prodPath}/error/403`);
                    url.searchParams.set('original_target', window.location.href);
                    router.push(url.toString());
                }
            }
        },
        onSuccess: () => {
            // 如果是白名單路徑，如果成功且為白名單，應導航至首頁或 original_target 頁面
            if (isWhite) {
                router.push(`${prodPath}${original_target || '/'}`);
            }
        },
    });

    const { data: personalPicture = null, isLoading: isGetPersonalPictureLoading } = useGetPersonalPicture();
    const { data: permissionList = null, isLoading: isGetPermissionListLoading } = useGetUserPermission();

    const isLoading = isGetUserInfoLoading || isGetPermissionListLoading || isGetPersonalPictureLoading;

    useEffect(() => {
        if (!isMounted()) return;

        const initAsia = async () => {
            setUser((prev) => ({
                ...prev,
                loading: false,
                isLogin,
                userInfo,
                personalPicture,
                permissionList,
            }));
        };

        if (isLoading) {
            setUser((prev) => ({
                ...prev,
                loading: true,
            }));
        } else if (!isLoading && isLogin) {
            initAsia();
        }
    }, [isMounted, isLoading, isLogin]);

    return <Layout>{children}</Layout>;
}
