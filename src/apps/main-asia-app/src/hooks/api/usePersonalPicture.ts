import useSWRMutation, { SWRMutationResponse } from 'swr/mutation';

import { get as fetcher } from '@/utils/api';

interface PersonalPictureResponse {
    Data: string;
    Meta: {
        HttpStatusCode: string;
    };
}

const fetchPersonalPicture = async (url: string) => fetcher<PersonalPictureResponse>(url);

/**
 * [asia限定] 取得個人頭像
 */
export const usePersonalPicture = () => {
    const {
        data: personalPictureData,
        trigger: getPersonalPicture,
        isMutating: isPersonalPictureMutating,
    }: SWRMutationResponse<PersonalPictureResponse> = useSWRMutation(
        'fd/api/users/personalPicture',
        fetchPersonalPicture,
    );

    return { personalPictureData, getPersonalPicture, isPersonalPictureMutating };
};
