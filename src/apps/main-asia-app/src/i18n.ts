import enUS from 'asia/utils/i18n/en-us.json';
import zhCN from 'asia/utils/i18n/zh-cn.json';
import zhTW from 'asia/utils/i18n/zh-tw.json';
import { getUserLocale } from 'asia/utils/locale';
import { getRequestConfig } from 'next-intl/server';

const messages: {
    [key: string]: any;
} = {
    'en-us': enUS,
    'zh-tw': zhTW,
    'zh-cn': zhCN,
};

export default getRequestConfig(async () => {
    // Provide a static locale, fetch a user setting,
    // read from `cookies()`, `headers()`, etc.
    const locale = await getUserLocale();

    return {
        locale,
        messages: messages[locale as any],
    };
});
