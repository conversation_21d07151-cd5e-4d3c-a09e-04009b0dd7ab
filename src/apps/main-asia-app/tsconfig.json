{"extends": ["../../tsconfig.base.json"], "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./apps/main-asia-app/src/*"], "public/*": ["./apps/main-asia-app/public/*"], "@mayo/mayo-ui": ["src/node_modules/@mayo/mayo-ui-beta/dist/index.js"], "asia/*": ["libs/asia/src/lib/*"]}, "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["next-env.d.ts", "environment.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}