// This file configures the initialization of Sentry on the server.
// The config you add here will be used whenever the server handles a request.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

console.log('process.env.NODE_ENV:', process.env.NODE_ENV);
console.log('process.env.SERVER_ENV_SENTRY_DSN;', process.env.SERVER_ENV_SENTRY_DSN);
Sentry.init({
    dsn: process.env.SERVER_ENV_SENTRY_DSN,

    // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
    tracesSampleRate: 1,

    // Setting this option to true will print useful information to the console while you're setting up Sentry.
    debug: false,
});
