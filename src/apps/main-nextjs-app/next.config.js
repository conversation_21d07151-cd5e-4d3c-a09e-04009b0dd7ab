const createNextIntlPlugin = require('next-intl/plugin');
const { withSentryConfig } = require('@sentry/nextjs');
const crypto = require('crypto');

const nextBuildId = require('next-build-id');

const buildIdPromise = nextBuildId({ dir: __dirname });

const withNextIntl = createNextIntlPlugin();

const isProd = process.env.NODE_ENV === 'production';

/** @type {import('next').NextConfig} */

const nextConfig = {
    assetPrefix: '/v2',
    output: 'standalone',
    generateBuildId: () => nextBuildId({ dir: __dirname }),
    experimental: {
        instrumentationHook: true,
    },
    webpack(config) {
        // Grab the existing rule that handles SVG imports
        const fileLoaderRule = config.module.rules.find((rule) => rule.test?.test?.('.svg'));

        config.module.rules.push(
            // Reapply the existing rule, but only for svg imports ending in ?url
            {
                ...fileLoaderRule,
                test: /\.svg$/i,
                resourceQuery: /url/, // *.svg?url
            },
            // Convert all other *.svg imports to React components
            {
                test: /\.svg$/i,
                issuer: fileLoaderRule.issuer,
                resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] }, // exclude if *.svg?url
                use: ['@svgr/webpack'],
            },
        );

        // Modify the file loader rule to ignore *.svg, since we have it handled now.
        fileLoaderRule.exclude = /\.svg$/i;

        return config;
    },
};

const nonce = Buffer.from(crypto.randomUUID()).toString('base64');

// 靜態資源安全性政策
const cspHeader = `
    default-src 'self';
    connect-src 'self' https://*.mayohr.com;    
    script-src ${isProd ? `'self' 'nonce-${nonce}'` : `'self' 'unsafe-inline' 'unsafe-eval'`};
    style-src ${isProd ? `'self' 'nonce-${nonce}' https://fonts.googleapis.com` : `'self' 'unsafe-inline' https://fonts.googleapis.com`};
    img-src 'self' https://*.blob.core.windows.net blob: data:;
    font-src 'self' https://fonts.gstatic.com https://at.alicdn.com https://cdnjs.cloudflare.com;
    frame-src 'self' https://*.mayohr.com;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'self' https://*.mayohr.com;
    upgrade-insecure-requests;
`;

module.exports = withSentryConfig(
    withNextIntl({
        ...nextConfig,
        async headers() {
            const buildId = await buildIdPromise;

            return [
                {
                    source: '/(.*)',
                    headers: [
                        {
                            key: 'Content-Security-Policy',
                            value: cspHeader.replace(/\n/g, ''),
                        },
                        {
                            key: 'X-Frame-Options',
                            /**
                             * 根據 CSP 規則，新增 X-Frame-Options 標頭，以防止點擊劫持
                             */
                            value: 'SAMEORIGIN',
                        },
                        {
                            key: 'X-Build-SHA',
                            value: buildId,
                        },
                    ],
                },
            ];
        },
    }),
    {
        org: 'mayo',
        project: 'mayo-frontend-platform',

        // An auth token is required for uploading source maps.
        authToken: process.env.SENTRY_AUTH_TOKEN,

        silent: false, // Can be used to suppress logs
    },
);
