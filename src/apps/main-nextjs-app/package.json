{"name": "main-nextjs-app", "version": "0.1.0", "private": true, "scripts": {"predev": "node scripts/generate-env.js", "dev": "open https://dev.mayohr.com:3000 && next dev -H dev.mayohr.com --experimental-https -p 3000", "build": "rm -rf .next && next build", "prestart": "node scripts/generate-env.js", "start": "env-cmd -f .env next start", "lint": "next lint"}, "dependencies": {"@sentry/nextjs": "^8.44.0", "axios": "^1.7.2", "cross-env": "^7.0.3", "env-cmd": "^10.1.0", "immer": "^10.1.1", "jotai": "2.9.0", "js-cookie": "^3.0.5", "next": "~14.2.26", "next-build-id": "^3.0.0", "next-intl": "3.15.3", "nextjs-toploader": "^3.7.15", "npm-run-all": "^4.1.5", "post-robot": "^8.0.32", "react": "18.3.1", "react-dom": "18.3.1", "react-error-boundary": "^4.0.13", "swr": "^2.2.5", "tailwindcss": "^3.3.0", "usehooks-ts": "3.1.0", "utils": "workspace:*", "zod": "^3.23.8", "zustand": "4.5.2"}, "devDependencies": {"@next/eslint-plugin-next": "^15.2.4", "@nx/next": "^20.8.0", "@nx/react": "^20.8.0", "@reduxjs/toolkit": "^2.8.2", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.16", "@types/node": "^20.17.30", "@types/post-robot": "^10.0.6", "@types/react": "18.3.1", "@types/react-dom": "18.3.1", "autoprefixer": "^10.0.1", "eslint": "~8.57.0", "eslint-config-next": "^15.2.4", "postcss": "^8", "react-redux": "^9.2.0", "typescript": "~5.7.2"}, "files": [".next", "node_modules", "next.config.js", "public", "config", "scripts", ".env"]}