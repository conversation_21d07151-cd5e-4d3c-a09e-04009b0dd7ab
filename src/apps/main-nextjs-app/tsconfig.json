{"extends": ["../../tsconfig.common.json"], "compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./apps/main-nextjs-app/src/*"], "public/*": ["./apps/main-nextjs-app/public/*"], "@mayo/mayo-ui": ["./apps/main-nextjs-app/node_modules/@mayo/mayo-ui-beta/dist/index.js"], "xe/*": ["libs/xe/src/lib/*"]}, "typeRoots": ["./node_modules/@types", "./types"], "types": ["next", "node"]}, "include": ["next-env.d.ts", "environment.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}