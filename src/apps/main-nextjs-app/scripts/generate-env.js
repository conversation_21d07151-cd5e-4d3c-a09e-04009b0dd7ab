/* eslint-disable import/no-dynamic-require */

// scripts/generate-env.js
const fs = require('fs');
const path = require('path');

const platform = process.env.PLATFORM;
const env = process.env.ENV;

if (!platform) {
    console.error('Error: PLATFORM environment variable is not set.');
    process.exit(1);
}

if (!env) {
    console.error('Error: ENV environment variable is not set.');
    process.exit(1);
}

// 動態構建配置文件名
const commonConfigFileName = `${env}_common.js`;
const platformConfigFileName = `${env}_${platform}.js`;

// 構建配置文件路徑
// __dirname: 當前文件所在目錄
// configDir: 配置文件目錄
const configDir = path.join(__dirname, '..', 'config', 'env-config');
const commonConfigPath = path.join(configDir, commonConfigFileName);
const platformConfigPath = path.join(configDir, platformConfigFileName);

// 配置環境通用檔案
let commonConfig = {};
try {
    commonConfig = require(commonConfigPath);
} catch (error) {
    console.error(`Error loading common configuration for "${commonConfigFileName}":`, error);
    process.exit(1);
}

// 配置平台環境需要的特定檔案
let platformConfig = {};
try {
    platformConfig = require(platformConfigPath);
} catch (error) {
    console.error(`Error loading platform configuration for "${platformConfigFileName}":`, error);
    process.exit(1);
}
// 合併兩者的環境變數
const finalEnv = {
    ...commonConfig,
    ...platformConfig,
};

// 將環境變數寫入 .env 文件
const finalEnvFilePath = path.join(__dirname, '..', '.env');

// 將環境變數寫入 .env 文件
fs.writeFileSync(
    finalEnvFilePath,
    Object.entries(finalEnv)
        .map(([key, value]) => `${key}=${value}`)
        .join('\n'),
);

console.log(
    `Generated .env at "${finalEnvFilePath}" with variables for platform "${platform}" and environment "${env}".`,
);
