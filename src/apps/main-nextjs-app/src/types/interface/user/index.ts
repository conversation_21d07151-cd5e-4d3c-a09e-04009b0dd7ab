export interface UserInfo {
    Meta: {
        HttpStatusCode: number;
    };
    Data: {
        isVerify: boolean;
        userModule: string[];
        userName: string;
        userRole: string[];
        IsSupervisor: boolean;
        IsSecretary: boolean;
        PersonalPicture: string;
        EmployeeId: string;
        CompanyId: string;
        companyName: string;
        companyLogoUrl: string;
        companyLogoUpdatedTime: string;
        Language?: string;
    } | null;
}

export enum FunctionCodeEnum {
    PeopleSearch = 'PeopleSearch',
    GoogleCalendar = 'GoogleCalendar',
    MayoForm = 'MAYOForm',
}

export interface FunctionDisableData {
    functionCode: FunctionCodeEnum;
    functionName: string;
    moduleName: string;
    moduleCode: string | null;
    enable: boolean;
    displayOrder: number;
}

export interface FunctionDisableList {
    data: {
        functionDisableList: FunctionDisableData[];
    };
}
