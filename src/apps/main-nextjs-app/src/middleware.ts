import type { NextRequest } from 'next/server';
import { NextResponse } from 'next/server';
import { WHITE_LIST } from 'xe/utils/config';
import { TOKEN_COOKIE } from 'xe/utils/const';
import prodPath from 'xe/utils/prodPath';

const isProd = process.env.NODE_ENV === 'production';

// This function can be marked `async` if using `await` inside
export async function middleware(request: NextRequest) {
    const referer = request.headers.get('referer') || '';
    // * CSP nonce start
    const nonce = Buffer.from(crypto.randomUUID()).toString('base64');

    /**
     * 開發模式時暫時打開 script-src 'unsafe-eval'，因為 next.js 預設會使用 eval
     * 暫時打開 style-src 'unsafe-inline'，目前 nextjs-toploader、以及 custom-report 的 iframe 內容中使用到了 inline style
     */
    const cspHeader = `
       default-src 'self';
       connect-src 'self' https://*.mayohr.com;
       script-src ${isProd ? `'self' 'nonce-${nonce}'` : `'self' 'nonce-${nonce}' 'unsafe-eval'`};
       style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
       img-src 'self' https://*.blob.core.windows.net blob: data:;
       font-src 'self' https://fonts.gstatic.com https://at.alicdn.com https://cdnjs.cloudflare.com;
       frame-src 'self' https://*.mayohr.com;
       object-src 'none';
       base-uri 'self';
       form-action 'self';
       frame-ancestors 'self' https://*.mayohr.com;
       upgrade-insecure-requests;
   `;

    // Replace newline characters and spaces
    const contentSecurityPolicyHeaderValue = cspHeader.replace(/\s{2,}/g, ' ').trim();

    const requestHeaders = new Headers(request.headers);

    requestHeaders.set('x-nonce', nonce);

    requestHeaders.set('Content-Security-Policy', contentSecurityPolicyHeaderValue);

    const response = NextResponse.next({
        request: {
            headers: requestHeaders,
        },
    });

    response.headers.set('Content-Security-Policy', contentSecurityPolicyHeaderValue);

    return response;

    const isWhite = WHITE_LIST.some((path) => referer.includes(path));

    if (isWhite) {
        return response;
    }

    const token = request.cookies.get(TOKEN_COOKIE)?.value;

    if (!token) {
        // original_target 判斷需調整，需排除如果已經有 original_target 參數，或 url 為白名單 的情況，
        return NextResponse.redirect(
            new URL(`${prodPath}/error/403?original_target=${encodeURIComponent(request.url)}`, request.url),
        );
    }

    return response;
}

export const config = {
    // 靜態資源不需要進入 middleware 檢查，加入 .* 避免被額外路徑('/v2')影響導致跑進 middleware 中發生無預期錯誤
    matcher: ['/((?!api|.*_next/static|.*_next/image|favicon.ico|error|images).*)'],
};
