import * as Sentry from '@sentry/nextjs';
import { AxiosError } from 'axios';
import dynamic from 'next/dynamic';
import { cookies } from 'next/headers';
import React from 'react';
import { TOKEN_COOKIE } from 'xe/utils/const';

const Provider = dynamic(() => import('@/components/provider'), { ssr: false });
const Error403Page = dynamic(() => import('@/app/error/403/page'), { ssr: false });
const Error500Page = dynamic(() => import('@/app/error/500/page'), { ssr: false });

/**
 * * 存放需要提前在 server side 撈取的共用資料
 */
const SystemRoot = async ({ locale, children }: { locale: string; children: React.ReactNode }) => {
    try {
        const isLogin = Boolean(cookies().get(TOKEN_COOKIE)?.value);

        return (
            <Provider locale={locale} isLogin={isLogin} persistData={{}}>
                {children}
            </Provider>
        );
    } catch (error) {
        if (error instanceof AxiosError && error.response?.status === 403) {
            return <Error403Page />;
        }
        Sentry.captureException(error, {
            tags: {
                feature: 'serverError',
                severity: 'high',
                env: 'production',
            },
        });
        return <Error500Page />;
    }
};

export default SystemRoot;
