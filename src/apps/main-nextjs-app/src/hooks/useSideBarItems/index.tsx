import ReportCenterIcon from 'apps/main-nextjs-app/public/images/icon_report-center.svg';
import { useAtom } from 'jotai';
import { useTranslations } from 'next-intl';
import { useRouter } from 'nextjs-toploader/app';
import { useMemo } from 'react';
import { userAtom } from 'xe/utils/jotai/user';
import prodPath from 'xe/utils/prodPath';

import { ModulePathEnum, SideBarItems } from '@/types/interface/layout';

const useSideBarItems = (): {
    data: SideBarItems;
} => {
    const t = useTranslations();
    const router = useRouter();
    const [user] = useAtom(userAtom);

    const { enabledFunctions } = user;

    const data: SideBarItems = useMemo(() => {
        let items: SideBarItems = {};

        items = {
            [ModulePathEnum.自定義報表]: enabledFunctions['custom-report']
                ? [
                      {
                          items: [
                              {
                                  icon: <ReportCenterIcon className="mr-[5px] shrink-0" />,
                                  title: t('SH_MayoReport'),
                                  matchPath: '/custom-report',
                                  onMenuClick: () => {
                                      router.push(`${window.location.origin}${prodPath}/custom-report`);
                                  },
                              },
                          ],
                      },
                  ]
                : [],
        };

        return items;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [
        enabledFunctions['custom-report'],
        // loading
    ]);

    return {
        data,
    };
};

export default useSideBarItems;
