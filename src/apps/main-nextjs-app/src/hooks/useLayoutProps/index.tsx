import axios from 'axios';
import { useAtomValue } from 'jotai';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { useLocale, useTranslations } from 'next-intl';
import { useRouter } from 'nextjs-toploader/app';
import EarthIcon from 'public/images/icon_earth.svg';
import PowerOffIcon from 'public/images/icon_power-off.svg';
import { useMemo, useTransition } from 'react';
import { COOKIE_NAMES, Locale } from 'xe/utils/config';
import { deleteCookie, setCookie } from 'xe/utils/cookies';
import { envsAtom } from 'xe/utils/jotai/env';
import { userAtom } from 'xe/utils/jotai/user';
import prodPath from 'xe/utils/prodPath';

import revalidatePathAction from '@/actions/revalidatePathAction';
import { RoutesEnum } from '@/types/interface/common/route';

const anonymousPicPath = `${prodPath}/images/anonymous.svg`;

const useLayoutProps = () => {
    const t = useTranslations();

    const router = useRouter();

    const pathname = usePathname();

    const locale = useLocale();

    const envs = useAtomValue(envsAtom);

    const { loading, isLogin, userInfo, enabledFunctions } = useAtomValue(userAtom);

    const [, startTransition] = useTransition();

    const onLanguageChange = (newLocale: Locale) => {
        startTransition(() => {
            // 清除 form auth cookie
            deleteCookie(COOKIE_NAMES.AUTH, '.mayohr.com');
            deleteCookie(COOKIE_NAMES.REFRESH_TOKEN, '.mayohr.com');

            setCookie(COOKIE_NAMES.LOCALE, newLocale, '.mayohr.com');
            axios.defaults.headers.common['Accept-Language'] = newLocale;
            window.location.reload();
        });
    };

    const navItems = [
        enabledFunctions['mayo-form']
            ? {
                  id: '自定義表單',
                  tab: 'MAYO Form',
                  onClick: () => {
                      if (!pathname.includes(RoutesEnum.表單申請)) {
                          // 清除 form auth cookie
                          deleteCookie(COOKIE_NAMES.AUTH, '.mayohr.com');
                          deleteCookie(COOKIE_NAMES.REFRESH_TOKEN, '.mayohr.com');

                          revalidatePathAction(`${prodPath}/mayo-form/${RoutesEnum.表單申請}`);
                          router.push(`${window.location.origin}${prodPath}/mayo-form/${RoutesEnum.表單申請}`);
                      }
                  },
                  path: `/mayo-form`, // 標記路由
              }
            : null,
        enabledFunctions['custom-report']
            ? {
                  id: '自定義報表',
                  tab: t('SH_MayoReport'),
                  onClick: () => {
                      revalidatePathAction(`${prodPath}/custom-report`);
                      router.push(`${prodPath}/custom-report`);
                  },
                  path: `/custom-report`, // 標記路由
              }
            : null,
    ].filter(Boolean);

    const layoutProps = useMemo(() => {
        // 透過 標記路由 找到預設 navItem
        const defaultNavItem = navItems.find((item) => item && pathname.includes(item.path)) || undefined;
        const userPicUrl = !isLogin ? anonymousPicPath : userInfo?.Data?.PersonalPicture || anonymousPicPath;
        const userName = !isLogin ? t('SH_NotLogin') : t('SH_Welcome', { 0: userInfo?.Data?.userName });
        return {
            isSupportMobile: true,
            showHamburger: false,
            privacyText: t('SH_PrivacyPolicy'),
            privacyUrl: `${envs.SERVER_ENV_WEB_SITE}/privacy`,
            aboutUsUrl: 'https://www.mayohr.com/tw',
            logo: (
                <div className="flex w-[300px] items-center gap-[15px]">
                    <Image src={`${prodPath}/images/company.svg`} width={132} height={22} alt="MAYOHR" />
                    <div className="h-[18px] w-[1px] bg-white" />
                    <Image src={`${prodPath}/images/xe-logo.svg`} width={96} height={24} alt="Apollo XE" />
                </div>
            ),
            userPicUrl,
            userName,
            menuItems: [
                {
                    icon: <EarthIcon />,
                    name: t('SH_Language'),
                    subMenu: [
                        {
                            name: t('zh-tw'),
                            onClick: () => onLanguageChange('zh-tw'),
                            active: locale === 'zh-tw',
                        },
                        {
                            name: t('SH_English'),
                            onClick: () => onLanguageChange('en-us'),
                            active: locale === 'en-us',
                        },
                    ],
                },
                ...(!isLogin
                    ? []
                    : [
                          {
                              icon: <PowerOffIcon />,
                              name: t('SH_Logout'),
                              onClick: () => {
                                  window.location.href = `${envs.SERVER_ENV_AUTH_WEB_SITE}/Account/Login?original_target=${encodeURIComponent(window.location.href)}`;
                              },
                          },
                      ]),
            ],
            navItems,
            defaultNavItem,
        };
    }, [loading, isLogin, locale, t]);

    return layoutProps;
};

export default useLayoutProps;
