'use server';

import { ModulePathEnum } from '@/types/interface/layout';
import { FunctionCodeEnum, FunctionDisableList, UserInfo } from '@/types/interface/user';

import { serverFetch } from './serverFetch';

export interface EnabledFunctions {
    [ModulePathEnum.自定義報表]: boolean;
    [ModulePathEnum.自定義表單]: boolean;
}

/**
 * @description 取得各自平台啟用的功能
 */
const getEnabledFunctions = async ({
    userInfo,
    platform,
}: {
    userInfo: UserInfo;
    platform: string | undefined;
}): Promise<EnabledFunctions> => {
    const features: EnabledFunctions = {
        [ModulePathEnum.自定義報表]: false,
        [ModulePathEnum.自定義表單]: false,
    };

    const { userRole } = userInfo?.Data || {};

    const initXe = async () => {
        const getFunctionDisableList = async () => {
            const { data } = await serverFetch<FunctionDisableList>('webbff/api/FunctionDisableList');

            return data?.functionDisableList || [];
        };

        const functionDisableList = await getFunctionDisableList();

        const isCustomReportAdmin = userRole?.includes('CustomReportAdmin') ?? false;

        const isShowMAYOForm =
            functionDisableList?.find((item) => item.functionCode === FunctionCodeEnum.MayoForm)?.enable || false;

        features[ModulePathEnum.自定義報表] = isCustomReportAdmin;
        features[ModulePathEnum.自定義表單] = isShowMAYOForm;
    };

    switch (platform) {
        case 'xe':
            console.log('initiating XE features...');
            await initXe();

            break;

        case 'asia':
            console.log('initiating asia features...');
            break;

        default:
            break;
    }

    return features;
};

export default getEnabledFunctions;
