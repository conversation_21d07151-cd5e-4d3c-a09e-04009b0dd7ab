import axios from 'axios';
import Cookies from 'js-cookie';
import { COOKIE_NAMES } from 'xe/utils/config';
import prodPath from 'xe/utils/prodPath';

import { deleteCookie, setCookie } from '@/utils/common/cookies';

import { BpmAxiosResponse } from './type';

const bpmAxiosInstance = axios.create({
    withCredentials: true,
    responseType: 'json',
    headers: {
        // * bpm 語系決定方式為初次登入時的語系
        'Content-Type': 'application/json',
    },
});

bpmAxiosInstance.defaults.headers.post['Content-Type'] = 'application/json';
bpmAxiosInstance.defaults.headers.put['Content-Type'] = 'application/json';
bpmAxiosInstance.defaults.headers.delete['Content-Type'] = 'application/json';

const refreshToken = async () => {
    const res: BpmAxiosResponse<{
        data: {
            result: string;
            availableInSecond: number;
            remainAttempts: number;
            tenantNo: null;
            employeeCode: null;
            appUserId: number;
            email: null;
            jwtToken: string;
            isFirstTime: boolean;
            refreshToken: string;
        };
    }> = await bpmAxiosInstance.post('/api/auth/refresh-token', {
        data: {
            IsAutoRefresh: true,
        },
    });

    return res.data;
};

bpmAxiosInstance.interceptors.request.use((config) => {
    // doc: 在 client side run time 取得 backend server
    if (typeof window !== 'undefined' && !config.baseURL) {
        const clientBaseURL = Cookies.get(COOKIE_NAMES.BPM_BACKEND_SERVER) || undefined;
        config.baseURL = clientBaseURL;
    }

    // doc: run time 取得 auth
    config.headers.Authorization = `Bearer ${Cookies.get(COOKIE_NAMES.AUTH)}`;
    return config;
});

bpmAxiosInstance.interceptors.response.use(
    (response) => response,
    async (error) => {
        const status = error.response ? error.response.status : null;

        if (status === 401) {
            // doc: if it is a refresh-token request, do not retry
            if (error.response.config.url.match(/refresh-token/)) {
                // redirect to error page
                if (window.location.pathname !== `${prodPath}/error/403`) {
                    window.location.href = `${prodPath}/error/403`;
                }

                return Promise.reject(error);
            }
            try {
                const res = await refreshToken();
                if (res.data.jwtToken) {
                    setCookie('auth', res.data.jwtToken, '.mayohr.com');
                    error.config.headers.Authorization = `Bearer ${res.data.jwtToken}`;
                    return axios.request(error.config);
                }
            } catch (e) {
                // doc: if refresh-token failed, remove auth cookie
                deleteCookie('auth', '.mayohr.com');
            }
        }

        return Promise.reject(error);
    },
);

export default bpmAxiosInstance;
