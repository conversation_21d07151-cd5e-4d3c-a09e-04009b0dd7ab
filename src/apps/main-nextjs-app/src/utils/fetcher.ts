'use client';

import axios, { AxiosRequestConfig, Method } from 'axios';
import Cookies from 'js-cookie';
import { COOKIE_NAMES, defaultLocale } from 'xe/utils/config';

axios.defaults.baseURL = process.env.SERVER_ENV_TUBE_BACKEND_SERVER;
axios.defaults.withCredentials = true;
axios.defaults.responseType = 'json';
axios.defaults.headers.common['Accept-Language'] = Cookies.get(COOKIE_NAMES.LOCALE) || defaultLocale;
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';
axios.defaults.headers.delete['Content-Type'] = 'application/json';

const fetcher = async (url: string, method: Method = 'GET', data?: any, config: AxiosRequestConfig = {}) => {
    try {
        const response = await axios({
            url,
            method,
            data,
            ...config,
        });
        if (response?.status >= 200 && response?.status < 300) {
            return response?.data;
        }
        throw new Error(response?.statusText);
    } catch (error) {
        if (axios.isAxiosError(error) && error?.response) {
            // 如果 error 是 AxiosError，並且有 response，拋出具體的錯誤信息
            const _error = new Error(`Error: ${error.response.status} ${error.response.statusText}`) as any;
            _error.status = error.response.status;
            _error.data = error.response?.data;
            throw _error;
        } else {
            // 否則拋出一般錯誤
            throw new Error('An error occurred while fetching data.');
        }
    }
};

export default fetcher;
