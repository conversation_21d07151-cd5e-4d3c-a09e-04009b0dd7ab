import * as Sentry from '@sentry/nextjs';

export async function register() {
    console.log('process.env.SERVER_ENV:', process.env.SERVER_ENV);
    console.log('process.env.NODE_ENV:', process.env.NODE_ENV);
    console.log('process.env.NEXT_RUNTIME:', process.env.NEXT_RUNTIME);
    if (process.env.NODE_ENV === 'production' && ['prod'].includes(process.env.SERVER_ENV || '')) {
        console.log('Sentry Start Init');
        if (process.env.NEXT_RUNTIME === 'nodejs') {
            await import('../sentry.server.config');
        }

        if (process.env.NEXT_RUNTIME === 'edge') {
            await import('../sentry.edge.config');
        }
    }
}

export const onRequestError = Sentry.captureRequestError;
