'use client';

import { loadDayjsPlugin, Toaster } from '@mayo/mayo-ui-beta/v2';
import { AxiosError } from 'axios';
import { useAtomValue, useSetAtom } from 'jotai';
import Cookies from 'js-cookie';
import { usePathname, useSearchParams } from 'next/navigation';
import { useRouter } from 'nextjs-toploader/app';
import React, { useEffect } from 'react';
import useSWR from 'swr';
import { useIsMounted } from 'usehooks-ts';
import Layout from 'xe/feature/layout';
import AppProvider from 'xe/redux/AppProvider';
import { TEST_LIST, WHITE_LIST } from 'xe/utils/config';
import { TOKEN_COOKIE } from 'xe/utils/const';
import { envsAtom } from 'xe/utils/jotai/env';
import { FunctionCodeEnum, FunctionDisableList, ModulePathEnum, userAtom , UserInfo } from 'xe/utils/jotai/user';
import prodPath from 'xe/utils/prodPath';

import { get as fetcher } from '@/utils/api';

// 載入 dayjs plugin
loadDayjsPlugin();

export default function Providers({
    children,
    isLogin,
}: {
    children: React.ReactNode;
    locale: string;
    isLogin: boolean;
    persistData: object;
}) {
    const router = useRouter();

    const searchParams = useSearchParams();

    const original_target = searchParams.get('original_target');

    const pathname = usePathname();

    const envs = useAtomValue(envsAtom);

    const setUser = useSetAtom(userAtom);

    const isMounted = useIsMounted();

    const isWhite = WHITE_LIST.some((path) => window.location.href.includes(path));

    /**
     * * auth 驗證機制倚仗於此 swr hook
     * * revalidateOnFocus: true, // 當頁面 focus 時重新請求，間隔為 2000 ms
     * * refreshInterval: 1000 * 60 * 15, // 15 分鐘重新請求，測試時縮短間隔
     * */
    const { data: userInfo = null, isLoading: isGetUserInfoLoading } = useSWR<UserInfo>(
        TEST_LIST.some((path) => pathname.includes(path)) ? null : 'fd/api/userinfo',
        fetcher,
        {
            shouldRetryOnError: false,
            refreshInterval: 1000 * 60 * 15,
            revalidateOnFocus: false,
            revalidateOnReconnect: false,

            onError: (err) => {
                const status = (err as AxiosError).response?.status;
                if (/403|401/i.test(String(status))) {
                    Cookies.remove(TOKEN_COOKIE, { path: '/', domain: '.mayohr.com' });

                    // original_target 判斷需調整，需排除如果已經有 original_target 參數，或 url 為白名單 的情況
                    // router.push(`${location.origin}${prodPath}/error/403?original_target=${encodeURIComponent(location.href)}`);
                    if (!isWhite) {
                        const url = new URL(`${window.location.origin}${prodPath}/error/403`);
                        url.searchParams.set('original_target', window.location.href);
                        router.push(url.toString());
                    }
                }
            },
            onSuccess: () => {
                // 如果是白名單路徑，如果成功且為白名單，應導航至首頁或 original_target 頁面
                if (isWhite) {
                    router.push(`${prodPath}${original_target || '/'}`);
                }
            },
        },
    );

    const { data: { data: { functionDisableList = [] } = {} } = {}, isLoading: isGetFunctionDisableListLoading } =
        useSWR<FunctionDisableList>(
            envs.PLATFORM === 'xe' && isLogin ? `webbff/api/FunctionDisableList` : null,
            fetcher,
            {
                shouldRetryOnError: false,
                revalidateOnFocus: false, // 防止頁面獲得焦點時重新獲取數據
                revalidateIfStale: false, // 防止過時數據自動重新獲取
                dedupingInterval: 60000, // 一分鐘內重複請求會被去重
            },
        );

    const isLoading = isGetFunctionDisableListLoading || isGetUserInfoLoading;

    useEffect(() => {
        if (!isMounted()) return;

        const initXe = async () => {
            const isCustomReportAdmin = userInfo?.Data?.userRole?.includes('CustomReportAdmin') ?? false;

            const isShowMAYOForm =
                functionDisableList?.find((item) => item.functionCode === FunctionCodeEnum.MayoForm)?.enable || false;

            setUser((prev) => ({
                ...prev,
                loading: false,
                userInfo,
                isLogin,
                functionDisableList,

                enabledFunctions: {
                    [ModulePathEnum.自定義報表]: isCustomReportAdmin,
                    [ModulePathEnum.自定義表單]: isShowMAYOForm,
                },
            }));
        };

        if (isLoading) {
            setUser((prev) => ({
                ...prev,
                loading: true,
            }));
        } else if (!isLoading && isLogin) {
            switch (envs.PLATFORM) {
                case 'xe':
                    console.log('initiating XE features...');
                    initXe();

                    break;

                default:
                    break;
            }
        }
    }, [isMounted, isLoading, isLogin]);

    return (
        <AppProvider>
            <Layout>
                {children}

                <Toaster position="top-center" />
            </Layout>
        </AppProvider>
    );
}
