'use client';

import { useSetAtom } from 'jotai';
import Cookies from 'js-cookie';
import { ReactNode, useEffect, useState } from 'react';
import { COOKIE_NAMES } from 'xe/utils/config';
import { envsAtom } from 'xe/utils/jotai/env';
import { jotaiStore } from 'xe/utils/jotai/store';
import prodPath from 'xe/utils/prodPath';

import { Envs } from '@/types/types/common';

function SetEnv({ serverEnvs, children }: { serverEnvs: Envs; children: ReactNode }) {
    const setEnvs = useSetAtom(envsAtom);
    const setSharedEnvs = useSetAtom(envsAtom, { store: jotaiStore });

    const [loaded, setLoaded] = useState(false);

    useEffect(() => {
        setEnvs(serverEnvs);
        setSharedEnvs(serverEnvs);
        // * 揭露在使用者端的環境變數並使用 prodPath (/v2) 與 HRM 系統做隔離
        Cookies.set(COOKIE_NAMES.BACKEND_SERVER, serverEnvs.SERVER_ENV_BACKEND_SERVER || '', {
            path: prodPath,
        });
        Cookies.set(COOKIE_NAMES.BPM_BACKEND_SERVER, serverEnvs.SERVER_ENV_BPM_BACKEND_SERVER || '', {
            path: prodPath,
        });

        setLoaded(true);
    }, [serverEnvs]);

    // 確保環境變數設定完成後再渲染子元件
    if (!loaded) {
        return null;
    }

    return <div>{children}</div>;
}

export default SetEnv;
