'use client';

import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

// TODO: Linkup 頁面
const Page = () => {
    const searchParams = useSearchParams();

    const error = searchParams.get('error');

    useEffect(() => {
        if (error) {
            // 說明：測試無預期錯誤進入自定義錯誤頁面
            ('' as any).map((item: any) => item);
        }
    }, [error]);
    return <div />;
};

export default Page;
