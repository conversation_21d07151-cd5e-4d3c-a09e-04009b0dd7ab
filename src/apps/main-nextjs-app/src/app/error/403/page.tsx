'use client';

import { Button } from '@mayo/mayo-ui-beta/v2';
import { useAtomValue } from 'jotai';
import { useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { envsAtom } from 'xe/utils/jotai/env';
import prodPath from 'xe/utils/prodPath';

export default function Page() {
    const t = useTranslations();
    const envs = useAtomValue(envsAtom);
    const searchParams = useSearchParams();
    const originalTarget = searchParams?.get('original_target') || '';

    const handleRedirectLoginPage = () => {
        const href = `${envs.SERVER_ENV_AUTH_WEB_SITE}/Account/Login?original_target=${encodeURIComponent(window.location.origin + prodPath + originalTarget)}`;
        window.location.href = href;
    };

    return (
        <div className="flex min-h-[calc(100vh-109px)] w-screen items-center justify-center p-4">
            <div className="flex w-full max-w-[800px] flex-col items-center gap-8 md:flex-row md:gap-[120px]">
                <div className="flex flex-col items-center text-center md:items-start md:text-left">
                    <img
                        src={`${prodPath}/images/xe-logo-blue.svg`}
                        width={180}
                        height={45}
                        alt="ApolloXE"
                        className="mb-8 mt-4 md:mb-[40px] md:mt-[50px]"
                    />
                    <img
                        src={`${prodPath}/images/unauthorized.svg`}
                        width={360}
                        height={360}
                        alt="unauthorized"
                        className="w-full max-w-[360px] md:w-[360px]"
                    />
                    <div className="mb-3 text-2xl font-medium leading-tight md:mb-[12px] md:text-[30px] md:leading-[30px]">
                        {t('SH_NotAuthorized')}
                    </div>
                    <div className="border-primary mb-6 border-l-2 pl-3 md:mb-[30px] md:pl-[12px]">
                        {t('SH_PlsLoginAgain')}
                    </div>

                    <div>
                        <Button variant="outline" colorSchema="primary" onClick={handleRedirectLoginPage}>
                            {t('SH_Confirm')}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    );
}
