'use client';

import { Button } from '@mayo/mayo-ui-beta/v2';
import { useAtomValue } from 'jotai';
import { useTranslations } from 'next-intl';
import { envsAtom } from 'xe/utils/jotai/env';
import prodPath from 'xe/utils/prodPath';

export default function Page() {
    const t = useTranslations();
    const envs = useAtomValue(envsAtom);

    const handleRedirectV1HomePage = () => {
        const href = `${envs.SERVER_ENV_WEB_SITE}/tube`;

        window.location.href = href;
    };

    return (
        <div className="flex h-[calc(100vh-109px)] w-screen items-center justify-center">
            <div className="flex h-[360px] gap-[120px]">
                <div className="flex flex-col">
                    <img
                        src={`${prodPath}/images/xe-logo-blue.svg`}
                        width={180}
                        height={45}
                        alt="ApolloXE"
                        className="mb-[40px] mt-[50px]"
                    />
                    <div className="mb-[12px] text-[30px] font-medium leading-[30px]">{t('SH_PageNotFound')}</div>
                    <div className="border-primary mb-[30px] border-l-[2px] pl-[12px]">
                        {t('SH_ServerPageNotFound')}
                    </div>
                    <div>
                        <Button variant="outline" colorSchema="primary" onClick={handleRedirectV1HomePage}>
                            {t('SH_Confirm')}
                        </Button>
                    </div>
                </div>
                <img src={`${prodPath}/images/404.svg`} width={360} height={360} alt="unauthorized" />
            </div>
        </div>
    );
}
