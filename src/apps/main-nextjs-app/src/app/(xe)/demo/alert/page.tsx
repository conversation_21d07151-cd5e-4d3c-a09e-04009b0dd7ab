'use client';

import { But<PERSON>, Dialog, DialogContent,DialogTrigger } from '@mayo/mayo-ui-beta/v2';
import { useEffect, useState } from 'react';
import {
    alertApp,
    getAppToken,
    getAppUser,
    logConsole,
    logTrack,
    refresh,
    routerBack,
    routerGoto,
} from 'xe/utils/appFunctions';

// global.d.ts

// 定義 Flutter WebView 的類型
declare global {
    interface Window {
        flutter_inappwebview?: {
            callHandler: (handlerName: string, ...args: any[]) => Promise<any>;
            registerHandler: (handlerName: string, handler: (...args: any[]) => any) => void;
        };
    }
}

// 定義路由列表
const APP_ROUTES = [
    'Attendance',
    'InformationCenter',
    'UnusedLeave',
    'FormRecords',
    'InformationCenter',
    'Leave',
    'ForgotCheckIn',
    'OverTime',
    'CheckIn',
    'CheckInIP',
    'SpecialLeave',
    'Trip',
    'TeamAttendance',
    'QRCode',
    'Worktable',
    'Assignment',
    'PostNote',
    'Salary',
    'CheckInPhoto',
    'ApprovalCenter',
    'MyTeam',
    'DashBoard',
    'StayFun',
    'WebBankOpen',
    'WebBankAuth',
    'WebBank',
    'CompanyRule',
    'SearchEmployee',
    'ShortcutSetUp',
    'Mart',
    'InternalLink',
    'MayoForm',
] as const;

type AppRoute = (typeof APP_ROUTES)[number];

const AlertPage = () => {
    const [userName, setUserName] = useState<string>('');
    const [alertResult, setAlertResult] = useState<string>('');
    const [appToken, setAppToken] = useState<string>('');
    const [showRouteList, setShowRouteList] = useState<boolean>(false);

    // 註冊 Web 端函式給 App 呼叫
    useEffect(() => {
        // 記錄日誌
        logConsole('頁面已載入', 'page_load');
        logTrack('用戶訪問頁面', 'page_view');
    }, []);

    // 獲取用戶信息
    const handleGetUser = async () => {
        setUserName('正在取得用戶資訊...');
        try {
            const user = await getAppUser();
            setUserName(user);
        } catch (error) {
            console.error('Failed to get user:', error);
        }
    };

    // 獲取 App Token
    const handleGetToken = async () => {
        setAppToken('正在取得 App Token...');
        try {
            const token = await getAppToken();
            setAppToken(token);
        } catch (error) {
            console.error('Failed to get token:', error);
        }
    };

    // 顯示彈窗
    const handleAlert = async () => {
        try {
            const buttonActions = ['確定', '取消'];
            const buttonIndex = await alertApp('測試標題', '測試內容', buttonActions);
            setAlertResult(`用戶點擊了: ${buttonIndex >= 0 ? buttonActions?.[buttonIndex] : '取消'}`);
        } catch (error) {
            console.error('Failed to show alert:', error);
        }
    };

    // 顯示路由列表
    const handleShowRouteList = () => {
        setShowRouteList(true);
    };

    // 選擇路由並導航
    const handleSelectRoute = async (route: AppRoute) => {
        setShowRouteList(false);
        try {
            await routerGoto(route, 'push');
        } catch (error) {
            console.error('Failed to navigate:', error);
        }
    };

    // 返回
    const handleBack = async () => {
        try {
            await routerBack('page');
        } catch (error) {
            console.error('Failed to go back:', error);
        }
    };

    // 刷新
    const handleRefresh = async () => {
        try {
            await refresh();
        } catch (error) {
            console.error('Failed to refresh:', error);
        }
    };

    // 在組件掛載時檢查 Flutter WebView 是否可用
    useEffect(() => {
        if (window.flutter_inappwebview) {
            console.log('Flutter WebView is available');
        } else {
            console.warn('Flutter WebView is not available');
        }
    }, []);

    return (
        <div className="p-4">
            <h1 className="mb-4 text-2xl font-bold">App Bridge Demo</h1>

            <div className="space-y-4">
                <div>
                    <p className="mb-2">用戶名:</p>
                    <div className="rounded border bg-gray-50 p-2">{userName || '尚未設定'}</div>
                </div>

                <div>
                    <p className="mb-2">App Token:</p>
                    <div className="rounded border bg-gray-50 p-2">{appToken || '尚未取得'}</div>
                </div>

                {alertResult && (
                    <div>
                        <p className="mb-2">彈窗結果:</p>
                        <div className="rounded border bg-gray-50 p-2">{alertResult}</div>
                    </div>
                )}

                <div className="grid grid-cols-2 gap-2">
                    <Button onClick={handleGetUser} color="primary">
                        獲取用戶
                    </Button>
                    <Button onClick={handleGetToken} color="primary">
                        獲取 Token
                    </Button>
                    <Button onClick={handleAlert} color="primary">
                        顯示彈窗
                    </Button>
                    {/* 路由列表彈窗 */}
                    <Dialog open={showRouteList} onOpenChange={setShowRouteList}>
                        <DialogTrigger asChild>
                            <Button onClick={handleShowRouteList} color="secondary">
                                路由導向
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl p-2">
                            <h2 className="mb-4 text-xl font-bold">選擇路由</h2>
                            <div className="max-h-96 overflow-y-auto">
                                <div className="grid grid-cols-2 gap-2">
                                    {APP_ROUTES.map((route) => (
                                        <Button
                                            key={route}
                                            onClick={() => handleSelectRoute(route)}
                                            color="primary"
                                            className="w-full"
                                        >
                                            {route}
                                        </Button>
                                    ))}
                                </div>
                            </div>
                        </DialogContent>
                    </Dialog>
                    <Button onClick={handleBack} color="secondary">
                        返回
                    </Button>
                    <Button onClick={handleRefresh} color="secondary">
                        刷新
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default AlertPage;
