'use client';

import * as Sen<PERSON> from '@sentry/nextjs';
import { useEffect } from 'react';

import Error500Page from '@/app/error/500/page';

export default function Error({ error }: { error?: Error & { digest?: string } }) {
    useEffect(() => {
        // Log the error to an error reporting service
        // eslint-disable-next-line no-console
        console.error(error);
        Sentry.captureException(error);
    }, [error]);

    return <Error500Page />;
}
