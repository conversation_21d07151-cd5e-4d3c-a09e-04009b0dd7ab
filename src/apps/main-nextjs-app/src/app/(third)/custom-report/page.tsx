'use client';

import { MaskLoading } from '@mayo/mayo-ui-beta';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { reportIframeRefreshKeyAtom } from 'xe/utils/jotai/iframeKey';
import { useGetRedirectURL } from 'xe/utils/swr/webbff';

export default function CustomReportPage() {
    const { data, isLoading } = useGetRedirectURL();
    const [isLoaded, setIsLoaded] = useState(false);
    const [tempUrl, setTempUrl] = useState('');
    const [iframeKey] = useAtom(reportIframeRefreshKeyAtom);

    useEffect(() => {
        if (!isLoaded) {
            const _url = data?.data || '';
            setTempUrl(_url);
        }
    }, [data, isLoaded]);

    useEffect(() => {
        if (isLoaded) {
            setIsLoaded(false);
        }
    }, [iframeKey]);

    return (
        <div className="h-full w-full p-4">
            {(!isLoaded || isLoading) && <MaskLoading />}
            <iframe title="custom-report" className="h-full w-full" src={tempUrl} onLoad={() => setIsLoaded(true)} />
            <footer className="z-50 mx-4 flex h-12 items-center justify-end bg-[rgb(240_240_241)] py-4">
                <p className="break-words text-end text-sm text-gray-500">
                    Copyright © 2025 MAYO Human Capital Inc. All rights reserved.
                </p>
            </footer>
        </div>
    );
}
