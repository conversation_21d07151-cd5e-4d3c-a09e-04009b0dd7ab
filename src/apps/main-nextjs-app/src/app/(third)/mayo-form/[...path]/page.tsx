import * as Sentry from '@sentry/nextjs';
import { notFound } from 'next/navigation';
import { getLocale } from 'next-intl/server';

import { serverFetch } from '@/utils/server/serverFetch';

import Iframe from './Iframe';

const getMAYOFormRedirectUrl = async ({ redirectUrl, locale }: { redirectUrl?: string; locale: string }) => {
    const { data } = await serverFetch<{
        data: string;
        code: number;
        message: string;
    }>(`webbff/api/MAYOForm/RedirectUrl?language=${locale}&redirectUrl=${redirectUrl}`);

    return data;
};

const page = async ({
    params,
    searchParams,
}: {
    params: { path: string[] };
    searchParams: { [key: string]: string };
}) => {
    try {
        const locale = await getLocale();

        // * 組合 redirectUrl 直接送給表單團隊 api 拿到表單網址
        const redirectUrl =
            params.path.join('/') +
            (Object.values(searchParams).length === 0 ? '' : `?${new URLSearchParams(searchParams).toString()}`);

        const url = await getMAYOFormRedirectUrl({ redirectUrl, locale });

        const redirectURL = `${url}&isIframeMode=true`;

        return <Iframe redirectURL={redirectURL} />;
    } catch (error) {
        Sentry.captureException(error, {
            tags: {
                feature: 'serverError',
                severity: 'high',
                env: 'production',
            },
        });
        notFound();
    }
};

export default page;
