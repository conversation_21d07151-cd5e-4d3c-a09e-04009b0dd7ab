'use client';

import { MaskLoading } from '@mayo/mayo-ui-beta';
import { useAtom } from 'jotai';
import Cookies from 'js-cookie';
import { useEffect, useRef, useState } from 'react';
import { useInterval } from 'usehooks-ts';
import { COOKIE_NAMES } from 'xe/utils/config';
import { IFRAME_KEY } from 'xe/utils/const';
import { reportIframeRefreshKeyAtom } from 'xe/utils/jotai/iframeKey';
import { setupListener } from 'xe/utils/postRobot';
import prodPath from 'xe/utils/prodPath';

interface routerChangeListener {
    cancel: () => void;
}

const Iframe = ({ redirectURL }: { redirectURL: string }) => {
    const [iframeKey] = useAtom(reportIframeRefreshKeyAtom);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const authCountRef = useRef(0);
    const [isLoadedAuth, setIsLoadedAuth] = useState(false);
    const [isLoaded, setIsLoaded] = useState(false);

    // * side bar 內容交還給 iframe (表單團隊) 處理

    /**
     * * 說明：
     * * auth、i18n cookie 是在 iframe 載入後由 iframe 內部設定的
     * * 每秒檢查是否有 auth cookie，有的話設定為已載入並停止檢查
     */
    useInterval(
        () => {
            authCountRef.current += 1;
            const auth = Cookies.get(COOKIE_NAMES.AUTH);

            if (authCountRef.current > 10) {
                setIsLoadedAuth(true);
                return;
            }
            // * 拿到 auth 之後，再去取得表單需要的資訊
            if (isLoaded && auth) {
                setIsLoadedAuth(true);
            }
        },
        isLoadedAuth ? null : 1000,
    );

    // * setupListener 用於接收表單團隊的路徑導轉資訊
    useEffect(() => {
        let routerChangeListener: routerChangeListener | null = null;

        // 初始化監聽器
        const initializeListener = () => {
            routerChangeListener = setupListener('routerChange', (data) => {
                window.history.replaceState(null, '', `${window.location.origin}${prodPath}/mayo-form/${data}`);
            });
        };

        // 如果 iframe 已加載，立即初始化監聽器
        if (isLoaded && isLoadedAuth) {
            initializeListener();
        }
        return () => {
            if (routerChangeListener) {
                routerChangeListener.cancel();
            }
        };
    }, [isLoaded && isLoadedAuth]);

    return (
        <div className="relative h-full w-full">
            {(!isLoaded || !isLoadedAuth) && <MaskLoading />}

            <iframe
                ref={iframeRef}
                id={IFRAME_KEY.MAYO_FORM}
                name={IFRAME_KEY.MAYO_FORM}
                key={iframeKey}
                title="Mayo Form Application"
                className="h-full w-full"
                src={redirectURL}
                onLoad={() => {
                    setIsLoaded(true);
                }}
            />
        </div>
    );
};

export default Iframe;
