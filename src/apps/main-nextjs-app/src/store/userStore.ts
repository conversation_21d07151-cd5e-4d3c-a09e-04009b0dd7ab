import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface User {
    name: string;
    email: string;
}

interface UserState {
    user: User | null;
    setUser: (user: User) => void;
    clearUser: () => void;
}

const useUserStore = create(
    immer<UserState>((set) => ({
        user: null,
        setUser: (user: User) =>
            set((state) => {
                state.user = user;
            }),
        clearUser: () =>
            set((state) => {
                state.user = null;
            }),
    })),
);

export default useUserStore;
