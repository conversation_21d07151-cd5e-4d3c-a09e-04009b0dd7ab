'use server';

import { cookies } from 'next/headers';
import { COOKIE_NAMES, defaultLocale, Locale, locales } from 'xe/utils/config';

// In this example the locale is read from a cookie. You could alternatively
// also read it from a database, backend service, or any other source.

export async function getUserLocale() {
    const locale = cookies().get(COOKIE_NAMES.LOCALE)?.value;
    const isSupported = locales.includes(locale as Locale);
    return isSupported ? locale : defaultLocale;
}

export async function setUserLocale(locale: Locale) {
    cookies().set(COOKIE_NAMES.LOCALE, locale);
}
