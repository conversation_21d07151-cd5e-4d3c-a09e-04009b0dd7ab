import { getRequestConfig } from 'next-intl/server';
import enUS from 'xe/utils/i18n/en-us.json';
import zhCN from 'xe/utils/i18n/zh-cn.json';
import zhTW from 'xe/utils/i18n/zh-tw.json';

import { getUserLocale } from './services/locale';

const platform = process.env.PLATFORM;

const messages: {
    [key: string]: any;
} = {
    xe: {
        'en-us': enUS,
        'zh-tw': zhTW,
        'zh-cn': zhCN,
    },
    asia: {
        'en-us': enUS,
        'zh-tw': zhTW,
        'zh-cn': zhCN,
    },
};

export default getRequestConfig(async () => {
    // Provide a static locale, fetch a user setting,
    // read from `cookies()`, `headers()`, etc.
    const locale = await getUserLocale();

    return {
        locale,
        messages: messages[platform as any][locale as any],
    };
});
