This is a [Next.js](https://nextjs.org/) project bootstrapped with [`create-next-app`](https://github.com/vercel/next.js/tree/canary/packages/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/basic-features/font-optimization) to automatically optimize and load Inter, a custom Google Font.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js/) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/deployment) for more details.

## Using SWR, Zustand, and Immer for State Management

### Create a Zustand store and use Immer for immutable state management:

```typescript
// store/userStore.ts
import create from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface User {
  name: string;
  email: string;
}

interface UserState {
  user: User | null;
  setUser: (user: User) => void;
  clearUser: () => void;
}

const useUserStore = create<UserState>(
  immer<UserState>((set) => ({
    user: null,
    setUser: (user: User) => set((state) => {
      state.user = user;
    }),
    clearUser: () => set((state) => {
      state.user = null;
    })),
  }))
);

export default useUserStore;
```

## Using SWR for Data Fetching

### In the Next.js project, we can use SWR to fetch data and store it in the Zustand store:

```typescript
// hooks/useUser.ts
import useSWR from 'swr';
import axios from 'axios';
import useUserStore from '../store/userStore';

const fetcher = (url: string) => axios.get(url).then((res) => res.data);

export const useUser = () => {
    const { setUser } = useUserStore();
    const { data, error } = useSWR('/api/user', fetcher, {
        onSuccess: (data) => {
            setUser(data);
        },
    });

    return {
        user: data,
        isLoading: !error && !data,
        isError: error,
    };
};
```

## Using in a Component

### Now you can use the useUser hook in your pages or components to fetch and display user data:

```typescript
// pages/index.tsx
import { useUser } from '../hooks/useUser';

const HomePage = () => {
  const { user, isLoading, isError } = useUser();

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error loading user data</div>;

  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      <p>Your email is {user.email}</p>
    </div>
  );
};

export default HomePage;
```
