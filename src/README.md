# 專案結構

以下是「mayo-projects」專案的檔案結構示意圖及其各部分的描述：

```plaintext
|-- mayo-projects
    |-- .dockerignore          # Docker忽略檔案，列出不包括在Docker映像中的檔案或目錄
    |-- .gitignore             # Git忽略檔案，指定不納入版本控制的檔案或目錄
    |-- .npmrc                 # NPM配置檔案，用於管理npm的行為和配置
    |-- README.md              # 專案說明檔案，通常包含專案描述、安裝和使用說明
    |-- nx.json                # Nx工具的配置檔案
    |-- package.json           # NPM包管理檔案，定義了專案的依賴和腳本
    |-- pnpm-lock.yaml         # PNPM鎖檔案，確保依賴的一致性
    |-- pnpm-workspace.yaml    # PNPM工作區配置檔案
    |-- tsconfig.common.json   # TypeScript共用配置檔案
    |-- apps                   # 應用程序目錄
        |-- fd-react-app       # FD React應用目錄
        |   |-- [配置檔案如 .eslintrc.cjs, Dockerfile, README.md 等]
        |   |-- dist           # 編譯後的檔案和目錄
        |   |-- public         # 公共資源檔案
        |   |-- src            # 源代碼檔案和目錄
        |-- main-nextjs-app    # 主要的Next.js應用目錄
        |   |-- [配置檔案如 .eslintrc.json, README.md 等]
        |   |-- .next          # Next.js構建檔案和目錄
        |   |-- public         # 公共資源檔案
        |   |-- src            # 源代碼檔案和目錄
        |-- main-asia-app    # 主要的Next.js應用目錄
        |   |-- [配置檔案如 .eslintrc.json, README.md 等]
        |   |-- .next          # Next.js構建檔案和目錄
        |   |-- public         # 公共資源檔案
        |   |-- src            # 源代碼檔案和目錄
        |-- main-react-app     # 主要的React應用目錄
            |-- [配置檔案如 .eslintrc.cjs, Dockerfile, README.md 等]
            |-- dist           # 編譯後的檔案和目錄
            |-- public         # 公共資源檔案
            |-- src            # 源代碼檔案和目錄
    |-- assets                 # 資源目錄，通常包含靜態檔案如樣式表
    |   |-- styles             # 樣式檔案
    |-- config                 # 配置檔案目錄
    |   |-- frontend-job-env-config.yml   # 前端作業環境配置檔案
    |   |-- nginx.conf         # Nginx配置檔案
    |   |-- url.js             # URL配置腳本
    |   |-- docker             # Docker配置目錄
    |   |   |-- main           # 主要Docker配置
    |   |   |   |-- Dockerfile # Docker構建檔案
    |   |   |-- nextjs         # Next.js專用Docker配置
    |   |       |-- Dockerfile # Docker構建檔案
    |   |-- env                # 環境配置目錄
    |-- packages               # 包目錄，用於管理專案的獨立模組或庫
        |-- utils              # 實用工具包目錄
            |-- [配置檔案如 .eslintrc.cjs, Dockerfile, README.md 等]
            |-- dist           # 編譯後的檔案和目錄
            |-- public         # 公共資源檔案
            |-- src            # 源代碼檔案和目錄
```

## 開始之前

在開始使用本專案之前，請確保您的開發環境符合以下要求。

### 預先要求

- Node.js 版本 >= 20.x
- pnpm 版本 9.x

### 設置開發環境

#### 安裝 Node.js

本專案需要 Node.js 20.x。如果您還沒有安裝 Node.js，可以從 [Node.js 官網](https://nodejs.org/) 下載並安裝。

您可以使用以下命令來檢查 Node.js 的版本：

```bash
node --version
```

#### 安裝 pnpm

Corepack 是作為 Node.js 的一個實驗性功能，在 Node.js 16.9.0 以上版本中引入，執行以下指令，可以切換安裝包管理器版本

```bash
corepack enable
corepack prepare pnpm@8 --activate
```

#### 啟動本機(根目錄)

啟動微前端架構應用

```bash
pnpm run start
```

啟動特定前端應用
packageName是指定專案的package.json name屬性

```bash
pnpm --filter={packageName} dev
```

# NextJS項目Docker打包說明

本教學將指導您如何將專案資源、建置 Docker 映像，並在 Docker 容器中執行您的應用程式。

[![N|Solid](https://cldup.com/dTxpPi9lDf.thumb.png)](https://nodesource.com/products/nsolid)

## 步骤 0: 依賴安裝

首先，在專案根目錄下安裝依賴套件，執行以下命令：
pnpm 會自動檢測到 pnpm-workspace.yaml 文件並安裝所有子包的依賴。

切換到 src 目錄

```bash
cd src/
```

```bash
pnpm install
```

根據pnpm-lock.yaml

```bash
pnpm install --frozen-lockfile
```

## 步驟 1: Docker構建鏡像前打包專案

### build command options 說明：

APP_PATH = <project_name>,  
RELEASE_TYPE = <target_env>,
PLATFORM= <platform_name>, ex: xe, asia

### deploy command options 說明：

- 使用 `--filter=<project_name>` 過濾,使用指定專案
- 使用 `pnpm deploy` 指令，根據 `package.json` 中的 `files` 欄位，將目標專案複製到目標目錄。
- 使用 `--prod` 指令，將會過濾並複製僅供 `prod` 環境使用的 `node_modules`。
- `./prod/app` 代表使用者所定義的目標位置。

### docker 打包&部署到Docker預備用工作區(執行)

PLATFORM 可依照平台更改 xe 或 asia

```bash
APP_PATH=main-nextjs-app pnpm run prepare-docker-resources
```

上述指令，已經包含了以下兩個指令

```bash
"build-app": "nx build $APP_PATH",

"deploy-app": "rm -rf prod && pnpm deploy --filter=$APP_PATH --prod ./prod/app",
```

## 步驟 2: 確認blobenv 是否存在

### 沒有blobenv 檔案, 則需要建立blobenv 資料夾, 並在blobenv 資料夾下建立env.sh 檔案

### 建立blobenv 資料夾

```bash
mkdir -p ./prod/app/blobenv
```

### 建立env.sh 檔案

```bash
touch ./prod/app/blobenv/env.sh
```

### env.sh 檔案寫入內容 PLATFORM=平台(xe or asia),RELEASE_TYPE=環境(dev、tst、uat、prod)

```bash
echo "PLATFORM=xe" > ./prod/app/blobenv/env.sh
echo "RELEASE_TYPE=tst" >> ./prod/app/blobenv/env.sh
```

## 步骤 3: 構建 Docker Image

### 返回根目錄

```bash
cd ../
```

### build Docker Image:

請先確保docker desktop 是開啟狀態

```bash
docker build -f ./build/Dockerfile  -t main-nextjs-v1.0.3-beta.26  .
```

在這一步，我們使用 Docker 命令構建映像，指定 Dockerfile 的路徑和目標映像的版本號。  
如果需要指定版本號，則需要使用 `-t` 參數指定版本號

## 步驟 4: 設定反向代理(local啟動docker https 才需要)

### [caddy](https://hub.docker.com/_/caddy) 介紹:

作為反向代理、靜態文件伺服器，提供 SSL 加密功能。它的一個顯著特點是內建自動取得和管理免費的 Let's Encrypt SSL 憑證。docker預設內建，零配置因此特別適合需要快速配置 SSL 的場景。

### 創立 Docker network(如果有創立過可忽略)

```bash
docker network create mayohr-network
```

想知道創立過的network: `docker network ls`

### 創立caddy 設定

可直接參照 tools/caddy/Caddyfile

## 步骤 5: 啟動 Docker 容器

### 啟動(視情況用其一):

啟動非local docker的container

```bash
docker run -d -p 80:3000 -t main-nextjs-v1.0.3-beta.26:latest
```

啟動local docker的container

```bash
docker run -d --name mayohr-app --network mayohr-network main-nextjs-v1.0.3-beta.26:latest
```

在這一步，我們使用 Docker 命令運行容器，指定端口映射和目標映像的版本號。  
使用 `docker ps` 查看容器是否啟動運行,及使用 `docker ps -a` 查看所有容器。
複製ps print出來ID,下`docker logs <container_id>` 就可以看到docker運行log

## 步骤 6: 啟動反向代理(local啟動docker https 才需要)

### 啟動caddy

```bash
docker run -d --name mayohr-caddy --network=mayohr-network -p 80:80 -p 443:443 -v $(pwd)/tools/caddy/Caddyfile:/etc/caddy/Caddyfile -v caddy_data:/data -v caddy_config:/config caddy:2
```

- 如果失敗,請將${pwd} 替換為專案根目錄
- 本地啟動記得確認hosts 是否有設定 `127.0.0.1 mayohr.com`

# 想刪除或停止容器、網路代理

```bash
docker stop mayohr-app
docker rm mayohr-app mayohr-caddy
docker network rm mayohr-network
```

# 關於 CI/CD

用於**前端開發**的持續集成 (CI) 和持續部署 (CD) 流程。使用特定於前端的共用 Pipeline 腳本來實現這一流程，以確保代碼質量，並自動化部署過程。

## 前端 CI/CD 腳本

專門的前端 CI/CD 腳本項目。這個項目包含了執行 CI/CD 所需的所有腳本和配置文件。

專案項目位置及其相關文檔：

- [前端 Pipeline 模板](https://mayohr.visualstudio.com/ApolloXE/_git/frontend-pipeline-template)
- [NextJS主應用 pipelines](https://mayohr.visualstudio.com/ApolloXE/_build?definitionId=879) `tst-xe-main-nextjs-frontend-v2`
- [React主應用 pipelines](https://mayohr.visualstudio.com/ApolloXE/_build?definitionId=854) `tst-xe-main-frontend-v2`
- [React子應用 pipelines](https://mayohr.visualstudio.com/ApolloXE/_build?definitionId=855) `tst-xe-fd-frontend-v2`
- [NextJS子應用 pipelines](https://mayohr.visualstudio.com/ApolloXE/_build?definitionId=891) `tst-xe-fd-nextjs-frontend-v2`
