# Frontend-Platform

此專案為前端平台的跨產品共用功能代碼庫，專案的目錄結構遵循[文件](<https://dev.azure.com/mayohr/Apollo-Deploy/_wiki/wikis/Apollo-Deploy.wiki/882/Deploy-to-App-Service-Container-(NET6)>)規範。

## 目錄結構

```
|-- .husky/                   # 包含 Git Hooks 設定檔，用於在提交代碼前進行自動化任務（如格式化、測試等）
|-- build/                    # 用於構建專案的配置檔案和腳本，如 Dockerfile 等
|-- config/                   # 存放專案的環境變數配置文件及其他相關配置
|-- src/                      # 專案的源代碼目錄，包含所有應用的核心邏輯和功能
|-- tools/                    # 開發工具或腳本，用於輔助專案開發、構建或運行
|-- .dockerignore             # 定義在構建 Docker 映像時需要忽略的文件或目錄
|-- .gitignore                # 定義 Git 版本控制系統中需要忽略的文件或目錄
```

## Technical Stack

![img](./docs/Frontend-Tech-Stack.png)

## 專案位置

[Frontend-Platform](https://dev.azure.com/mayohr/MAYO-Frontend/_git/Frontend-Platform)

![](./docs//code.png)

## 開發

本專案使用 [@nx/next](https://nx.dev/technologies/react/next/api) app 與 libs 將程式碼 「模量化」

- apps/main-nextjs-app (xe)：作為 xe 的主專案，以模組的方式引用 `libs/xe` 的內容
- apps/main-asia-app (asia)：作為 asia 的主專案，以模組的方式引用 `libs/asia` 的內容
- libs/xe：作為 xe 的模組 lib，對應到 `main-nextjs-app (xe)` 的所有模組功能
- libs/asia：作為 asia 的模組 lib，對應到 `main-asia-app (asia)` 的所有模組功能
- libs/\*/feature：以 feature-based 作為功能頁面分類
- libs/\*/utils：共用於 feature 資料夾

![](./docs/nx-app-libs-distribution.avif)

> 將 90 以上的程式碼歸納在 Libs 中

## Ref

- [Our primary recommendation for applications: the “modulith”
  ](https://nx.dev/blog/virtuous-cycle-of-workspace-structure#our-primary-recommendation-for-applications-the-modulith)
- [Breaking up the Monolith - How to identify where boundaries are](https://nx.dev/blog/architecting-angular-applications#breaking-up-the-monolith-how-to-identify-where-boundaries-are)
- [Pizza-boxes, Onions and Hexagons - How to organize code](https://nx.dev/blog/architecting-angular-applications#breaking-up-the-monolith-how-to-identify-where-boundaries-are)

## 以業務邏輯拆分程式碼

![](./docs/videoframe_5187.png)

[ref](https://nx.dev/blog/architecting-angular-applications#breaking-up-the-monolith-how-to-identify-where-boundaries-are)

> 根據業務邏輯構建程式碼，而不是按技術類型（元件、服務、指令）來組織代碼


### 1. App 層（apps/main-nextjs-app、apps/main-asia-app）

- 只負責**路由、組態、組裝 UI**，不放具體業務邏輯
- 主要工作是「組合」來自 `libs/xe` 或 `libs/asia` 的功能

### 2. Libs 層（libs/xe、libs/asia）

- **業務邏輯、API 呼叫、資料模型、hooks、UI component** 都應放在對應的 lib
  - xe 相關 → `libs/xe`
  - asia 相關 → `libs/asia`
- 若兩者有共用邏輯，可抽到 `libs/shared` 或 `libs/shared-xxx`，再由 `libs/xe`、`libs/asia` 引用

### 3. 何時需要拆分成兩個 lib？

- 當 xe 與 asia 的業務邏輯、需求、流程差異大時，應分別維護於 `libs/xe`、`libs/asia`
- 若僅有少部分差異，可在同一個 lib 內用條件分支或參數處理，或以資料夾區分（如 `libs/payroll/src/xe/`、`libs/payroll/src/asia/`）

### 4. 共用邏輯的抽象

- 若發現 `libs/xe` 與 `libs/asia` 有大量重複，可抽象到 `libs/shared-xxx`
- 例如：
  ```
  libs/
    shared-payroll/
    xe/
      payroll/
    asia/
      payroll/
  ```
- 這樣 `xe`、`asia` 各自維護專屬邏輯，同時共用底層通用邏輯

---

## 實作建議

- **初期**：不確定差異時，先將邏輯放在 `libs/xe` 或 `libs/asia`
- **後期**：若發現共用度高，再抽象到 `libs/shared`

---

## 範例結構

```
apps/
  main-nextjs-app/   # xe 專案，僅組裝 UI
  main-asia-app/     # asia 專案，僅組裝 UI
libs/
  xe/                # xe 專屬業務邏輯
    payroll/
    attendance/
    ...
  asia/              # asia 專屬業務邏輯
    payroll/
    attendance/
    ...
  shared/            # 共用邏輯（如有需要）
    utils/
    api/
```

---

## 結論

- **業務邏輯請盡量拆分到對應的 libs/xe 或 libs/asia**
- **共用邏輯再抽象到 shared**
- **App 層只做組裝，不放業務邏輯**
- 這樣能讓專案結構清晰、維護彈性高、多人協作不易衝突
---


