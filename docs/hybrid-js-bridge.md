# Hybrid App 技術規劃交接文件

> 作者：Luke Lin
> 日期：2025-06
> 文件目的：交接 Hybrid App 架構與 WebView 溝通機制，協助後續工程師理解並擴充 App 與 Web 的互動基礎。

---

## 一、背景與目標

為解決人力不足與多平台重複開發的問題，公司選擇以 Hybrid App 架構進行整合開發。Web 採用 React + Vite 實作，透過 WebView 嵌入至 Flutter App 中，雙方透過 JS Bridge 溝通。

此文件記錄了 JS Bridge 的實作方式、溝通協定與擴充建議，未來 App 與 Web 的功能對接皆可遵循本規劃。

---

## 二、整體架構

1. Web 端（React + Vite）嵌入於 Flutter WebView 中
2. Web 呼叫 App 功能：`window.flutter_inappwebview`
3. 雙方溝通格式為：函式名稱 + 參數（Object） + callback（選擇性）

---

## 三、全域型別定義（global.d.ts）

```ts
declare global {
  interface Window {
    flutter_inappwebview?: {
      callHandler: (handlerName: string, ...args: any[]) => Promise<any>;
      registerHandler: (
        handlerName: string,
        handler: (...args: any[]) => any
      ) => void;
    };
  }
}
```

---

## 四、Bridge Service 實作說明（appBridge.ts）

### 呼叫 App 端

```ts
export const callAppFunction = async (
  fnName: string,
  args?: any[],
  callback?: (res: any) => void
): Promise<void> => {
  if (
    typeof window !== "undefined" &&
    window.flutter_inappwebview?.callHandler
  ) {
    try {
      const result = await window.flutter_inappwebview.callHandler(
        fnName,
        ...(args || [])
      );
      callback?.(result);
    } catch (error) {
      callback?.(error);
    }
  } else {
    // Mock 實作 (web 環境下沒有原生 App 時使用)
    console.warn(`[Mock] callHandler: ${fnName}`, args);
    setTimeout(() => {
      callback?.(`Mock response for ${fnName}`);
    }, 500);
  }
};
```

---

## 五、Web 端 API Function 封裝（appFunctions.ts）

### 功能對照表

| 功能             | 方法                              | 說明                        |
| ---------------- | --------------------------------- | --------------------------- |
| 取得使用者資訊   | getAppUser()                      | 呼叫 App 提供的登入資訊     |
| 導頁功能         | routerGoto(url, presentType)      | 進入內部頁面                |
| 返回功能         | routerBack(backType, count)       | 支援多層返回                |
| 設定導覽列(暫緩) | setNavbar(name, tools)            | 控制 navBar 標題與按鈕      |
| Log Console      | logConsole(message, category)     | 開發 log 傳送到 App console |
| Log Tracking     | logTrack(message, category)       | 埋點追蹤記錄傳送            |
| 顯示 alert       | alertApp(title, content, actions) | 顯示 App 端 alert 對話框    |
| 手動刷新         | refresh()                         | 通知 App 重繪頁面或資料     |
| 取得 App Token   | getAppToken()                     | 取得 App 端授權 Token       |

### 主要 API 封裝範例

```ts
import { callAppFunction } from "./appBridge";

// 1. 取得 App 使用者資訊
export function getAppUser(): Promise<any> {
  return new Promise((resolve) => {
    callAppFunction("get_app_user", [], (res: any) => {
      console.log("getAppUser", res);
      resolve(res);
    });
  });
}

// 2. 路由導向
export function routerGoto(
  url: string,
  presentType: "pop" | "push" = "push"
): Promise<any> {
  return new Promise((resolve) => {
    callAppFunction("router_goto", [url, presentType], (res: any) => {
      console.log("routerGoto", res);
      resolve(res);
    });
  });
}

// 3. 返回
export function routerBack(
  backType: "page" | "back_to_home" = "page",
  count?: number
): Promise<any> {
  return new Promise((resolve) => {
    callAppFunction("router_back", [backType, count], (res: any) => {
      console.log("routerBack", res);
      resolve(res);
    });
  });
}

// 5. log_console
export function logConsole(message: string, consoleCategory?: string): void {
  console.log("logConsole", message, consoleCategory);
  callAppFunction("log_console", [message, consoleCategory]);
}

// 6. log_track
export function logTrack(message: string, consoleCategory?: string): void {
  console.log("logTrack", message, consoleCategory);
  callAppFunction("log_track", [message, consoleCategory]);
}

// 7. alert
export function alertApp(
  title: string,
  content: string,
  buttonActions: string[] = []
): Promise<any> {
  return new Promise((resolve) => {
    callAppFunction(
      "alert_app",
      [title, content, ...buttonActions],
      (buttonIndex: number | null) => {
        console.log("buttonIndex", buttonIndex);
        resolve(buttonIndex);
      }
    );
  });
}

// 8. refresh
export function refresh(): Promise<any> {
  return new Promise((resolve) => {
    callAppFunction("refresh", [], (res: any) => {
      console.log("refresh", res);
      resolve(res);
    });
  });
}

// 9. 取得 App Token
export function getAppToken(): Promise<string> {
  return new Promise((resolve) => {
    callAppFunction("get_app_token", [], (res: any) => {
      console.log("getAppToken", res);
      resolve(res);
    });
  });
}
```

---

### App Route Name 清單

| Route Name | Route Name | Route Name  |
|--------------|--------------|--------------|
| Attendance | InformationCenter | UnusedLeave |
| FormRecords | Leave | ForgotCheckIn |
| OverTime | CheckIn | CheckInIP |
| SpecialLeave | Trip | TeamAttendance |
| QRCode | Worktable | Assignment |
| PostNote | Salary | CheckInPhoto |
| ApprovalCenter | MyTeam | DashBoard |
| StayFun | WebBankOpen | WebBankAuth |
| WebBank | CompanyRule | SearchEmployee |
| ShortcutSetUp | Mart | InternalLink |
| MayoForm | | |

---

## 如何在 App 測試 Webvie

<img src="./js-bridge-0.jpeg" width="240" alt="App logo">

> 進入到 uat 測試 app 點擊 logo

<img src="./js-bridge-2.jpg" width="240" alt="">

> 選擇環境(tst、uat、prd)，可以先選擇 tst

<img src="./js-bridge-4.PNG" width="240" alt="">

> 輸入密碼登入

<img src="./js-bridge-5.PNG" width="240" alt="">

> 選擇公司後登入

<img src="./js-bridge-7.PNG" width="240" alt="">

> 點擊 logo 打開 webview url input

<img src="./js-bridge-8.PNG" width="240" alt="">

> 輸入測試網網：https://tst-apolloxe.mayohr.com/v2/demo/alert?isIframemode=true

<img src="./js-bridge-9.PNG" width="240" alt="">

> 畫面應成功看到測試範例用以測試 [主要 API 封裝範例](#主要-api-封裝範例)

<img src="./js-bridge-10.PNG" width="240" alt="">

> 點擊「路由導向」後打開彈窗可以測試由 webview 請手機端導向指定頁面，參考路徑為 [App Route Name 清單](#app-route-name-清單)

---

## 七、XE API List (157)

> app 團隊整理的手機版有在用的 api endpoint ，web 用不到但整理在一起方便參閱

| 名稱                                  | EndPoint                                                                                                                                                                                 |
| ------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| deletePostNoteReceive                 | "$tubeUrl/api/PostNote/Received/$postnoteId"                                                                                                                                             |
| deletePostNoteSended                  | "$tubeUrl/api/PostNote/Sended/$postnoteId"                                                                                                                                               |
| getAssignmentToMe                     | "$tubeUrl/api/assignment/tome"                                                                                                                                                           |
| getMessageCenterUnreadCount           | "$tubeUrl/api/messagecenter/unread"                                                                                                                                                      |
| getPayrollNoticesMessageCount         | "$pyUrl/api/PayrollNotices/MessageCount"                                                                                                                                                 |
| getPostNoteReceiveCount               | "$tubeUrl/api/postnote/received/unread"                                                                                                                                                  |
| getReviewWaitApprovedNumbers          | "$tubeUrl/api/ReviewWaitApproved/GetCount?Language=$languageHeader"                                                                                                                      |
| getUserInfoAPP                        | "$tubeUrl/api/userInfo/app?language=$languageHeader"                                                                                                                                     |
| getUserInfo                           | "$tubeUrl/api/userInfo"                                                                                                                                                                  |
| checkTicket                           | "$versionUrl/auth/checkticket?code=code&response_type=responseType &CompanyId=$companyId"                                                                                                |
| getCarouselSeconds                    | "$portalUrl/api/company/COMP_CompanySetting/GetCarouselSeconds"                                                                                                                          |
| getTubeBanner                         | "$portalUrl/api/company/Tube_Banner/GetOn?language=zh-tw&bannerType=APP"                                                                                                                 |
| login                                 | "$authUrl${getURLFromSHA(time, "/Token")}&\_sd=HRM"                                                                                                                                      |
| ssoLoginExternalADLogin               | "$authUrl/api/app/AccountApi/ExternalADLogin"                                                                                                                                            |
| postLogout                            | "$bffUrl/api/app/account/logout"                                                                                                                                                         |
| resetPassword                         | "$authUrl/api/AccountApi/ActiveHRM"                                                                                                                                                      |
| getAppVersion                         | "$bffUrl/api/app/version?appId=$packageName&os=$platform"                                                                                                                                |
| getCountwithunderdepts                | "$bffUrl/api/departments/attendances/exceptions/countwithunderdepts"                                                                                                                     |
| getFeedBackUrl                        | "$bffUrl/api/app/external/link"                                                                                                                                                          |
| getFunctionDisableList                | "$bffUrl/api/functiondisablelist?companyId=$companyId&employeeId=$employeeId"                                                                                                            |
| getMayoFormNotifiedUnreadCount        | "$bffUrl/api/app/notifications/mayoform/count"                                                                                                                                           |
| readMayoFormNotification              | "$bffUrl/api/app/notifications/mayoform/$formId/read"                                                                                                                                    |
| getMayoFormUnreadList                 | "$bffUrl/api/app/notifications/mayoform"                                                                                                                                                 |
| getMayoFormList                       | "$bffUrl/api/MAYOForm/LinkList"                                                                                                                                                          |
| getMayoFormRedirectUrl                | "$bffUrl/api/MAYOForm/RedirectUrl?action=$actionName&language=$languageHeader"                                                                                                           |
| getMayoMartList                       | "$bffUrl/api/app/mart/servicelist"                                                                                                                                                       |
| checkMayoMartPermission               | "$bffUrl/api/app/mart/appurl?apiPath=$serviceInfoApiPath"                                                                                                                                |
| getSidebarNotificationCount           | "$bffUrl/api/Sidebar/NotificationCount"                                                                                                                                                  |
| getShiftSchedule                      | "$bffUrl/api/employees/calendars/shift-schedule/$date/worktime"                                                                                                                          |
| getDeptTree                           | "$dashboardUrl/api/deptTree"                                                                                                                                                             |
| getDismissRate                        | "$dashboardUrl/api/dismissionRate/$deptId$jobGroupIdStr$employeeGroupIdStr"                                                                                                              |
| getIdentityCategory                   | "$dashboardUrl/api/params/option?type=IdentityCategory"                                                                                                                                  |
| getJobGroupOption                     | "$dashboardUrl/api/jobGroup/option"                                                                                                                                                      |
| getJobGroup                           | "$dashboardUrl/api/JobGroup/$deptId$jobGroupIdStr$employeeGroupIdStr"                                                                                                                    |
| getLeaveUsage                         | "$dashboardUrl/api/LeaveUsage/$deptId$jobGroupIdStr$employeeGroupIdStr"                                                                                                                  |
| getNumberOfCompany                    | "$dashboardUrl/api/NumberOfCompany"                                                                                                                                                      |
| getPermissionAPP                      | "$dashboardUrl/api/Permission/App"                                                                                                                                                       |
| getPersonCost                         | "$dashboardUrl/api/PersonalCost/$deptId$jobGroupIdStr$employeeGroupIdStr"                                                                                                                |
| getExternalADLogin                    | "$authUrl/api/accountapi/ExternalADLogin?returnUrl=&domain=$domain"                                                                                                                      |
| getInternalLink                       | "$portalUrl/api/company/COMP_InternalSystemLink/Get"                                                                                                                                     |
| getQProcessResult                     | "$authUrl/api/trigger/GetQProcessResult/$code"                                                                                                                                           |
| doRefreshToken                        | "$versionUrl/auth/refreshtoken?response_type=id_token"                                                                                                                                   |
| deleteHistoryWithAttendanceHistoryId  | "$ptUrl/api/checkIn/$attendanceHistoryId?checkInDate=$deleteDay${employeeId == "" ? "" : "&employeeId=$employeeId"}"                                                                     |
| deleteTripRequestForm                 | "$ptUrl/api/TripRequestForm/Delete/$formId"                                                                                                                                              |
| getCheckInExtendWorkHourRule          | "$ptUrl/api/CheckInExtendWorkHourRule/GetWithReason"                                                                                                                                     |
| getCheckInPhoto                       | "$ptUrl/api/checkIn/GetPhoto/v2?employeeNumber=$employeeNumber"                                                                                                                          |
| getCheckInRecordsByDepartments        | "$ptUrl/api/checkinRecords/Department?departmentId=$departmentId&date=$date&subordinateSelectType=$selectType"                                                                           |
| getCheckInRecords                     | "$ptUrl/api/checkinRecords/?attendanceDateEnd=${Uri.encodeComponent(attendanceDateEnd)}&attendanceDateStart=${Uri.encodeComponent(attendanceDateStart)}&employeeId=$employeeId"          |
| getCommonSystDate                     | "$ptUrl/api/common/sysDate"                                                                                                                                                              |
| getDepartmentBehalf                   | "$ptUrl/api/departments/behalf${formType == "" ? "" : "?formType=$formType"}"                                                                                                            |
| getDepartmentSchedulingWithEmployee   | "$ptUrl/api/departments/schedulingWithEmployees"                                                                                                                                         |
| getEmployeeAvailableMonthLeave        | "$ptUrl/api/IndividualMonthLeaveTypes/AvailableMonthLeave?startDate=$startDate&endDate=$endDate&employeeId=$employeeID"                                                                  |
| getEmployeeCalendarsDeptScheduling    | "$ptUrl/api/EmployeeCalendars/deptScheduling/V2?StartDate=$startDate&EndDate=$endDate&DepartmentId=$departmentId"                                                                        |
| getEmployeeCalendarsScheduling        | "$ptUrl/api/EmployeeCalendars/scheduling/V2?year=$year&month=${int.parse(month)}&employeeId=$employeeID"                                                                                 |
| getEmployeeCalendarsWorkMinutes       | "$ptUrl/api/EmployeeCalendars/WorkMinutes?StartDate=${Uri.encodeComponent(startDate)}&EndDate=${Uri.encodeComponent(endDate)}${(employeeId == "") ? "" : "&employeeId=$employeeId"}"     |
| getEmployeeCalendars                  | "$ptUrl/api/EmployeeCalendars/WorkTime?employeeId=${(employeeId == "") ? "" : employeeId}&workDay=${Uri.encodeComponent(workday)}"                                                       |
| getGlobalConfigs                      | "$ptUrl/api/GlobalConfigs"                                                                                                                                                               |
| getIndividualLeaveTypes               | "$ptUrl/api/IndividualLeaveTypes/AvailableLeave?StartTime=${Uri.encodeComponent(startTime)}&EndTime=${Uri.encodeComponent(endTime)}${employeeId == "" ? "" : "&employeeId=$employeeId"}" |
| getIsManagerDeleteCheckInRight        | "$ptUrl/api/CheckInOtherRules"                                                                                                                                                           |
| getLeaveRequestForms                  | "$ptUrl/api/LeaveRequestForms/$formId"                                                                                                                                                   |
| getLocationAppEnableList              | "$ptUrl/api/locations/AppEnableList?language=$languageHeader"                                                                                                                            |
| getLocationEnableList                 | "$ptUrl/api/locations/EnableList?language=$languageHeader"                                                                                                                               |
| getOvertimeRequestFormsStartOvertime  | "$ptUrl/api/OvertimeRequestForms/StartOverTime?StartOverDatetime=${Uri.encodeComponent(startOverDatetime)}&overtimeRequestFormId=$formId"                                                |
| getOvertimeRequestForms               | "$ptUrl/api/OvertimeRequestForms/$formId"                                                                                                                                                |
| getOvertimeRequest                    | "$ptUrl/api/OvertimeRequestForms/StartOverTime?StartOverDatetime=${Uri.encodeComponent(startOverTime)}${selectedEmployeeId == "" ? "" : "&employeeId=$selectedEmployeeId"}"              |
| getPunchType                          | "$ptUrl/api/checkin/punchedType"                                                                                                                                                         |
| getReCheckInApproval                  | "$ptUrl/api/reCheckInApproval/$formId?partitionKey=$partitionKey"                                                                                                                        |
| getRequestFormsRecord                 | "$ptUrl/api/RequestForms${(isBehalf) ? "/Behalf" : ""}?Year=$year&Month=$month"                                                                                                          |
| getSpecialLeaveByFormID               | "$ptUrl/api/SpecialLeaveRequestForms/$formId"                                                                                                                                            |
| getSpecialLeave                       | "$ptUrl/api/SpecialLeaveRequestForms/SpecialLeave${employeeId == "" ? "" : "?employeeId=$employeeId"}"                                                                                   |
| getTimeScale                          | "$ptUrl/api/timeScales"                                                                                                                                                                  |
| getTripRequestForms                   | "$ptUrl/api/TripRequestForm/$formId"                                                                                                                                                     |
| getTripRequestFormsBeforeAgent        | "$ptUrl/api/TripRequestForm/BeforeAgent?employeeId=$employeeId"                                                                                                                          |
| getUnderDepartment                    | "$ptUrl/api//checkinRecords/underDepartment"                                                                                                                                             |
| getUnusedLeaves                       | "$ptUrl/api/IndividualLeaveTypes/Unused${employeeId == "" ? "" : "/Under?departmendId=&employeeId=$employeeId&leaveCode="                                                                |
| postLeaveRequestForms                 | "$ptUrl/api/LeaveRequestForms/"                                                                                                                                                          |
| postOvertimeRequestForms              | "$ptUrl/api/OvertimeRequestForms/"                                                                                                                                                       |
| postPunchAppExtend                    | "$ptUrl/api/checkin/punch/app"                                                                                                                                                           |
| postPunchApp                          | "$ptUrl/api/checkin/punch/app?language=zh-tw"                                                                                                                                            |
| postPunchIPExtend                     | "$ptUrl/api/checkin/punch/app-ip"                                                                                                                                                        |
| postPunchIP                           | "$ptUrl/api/checkin/punch/app-ip"                                                                                                                                                        |
| postPunchLocateExtend                 | "$ptUrl/api/checkin/punch/locate"                                                                                                                                                        |
| postPunchLocate                       | "$ptUrl/api/checkin/punch/locate"                                                                                                                                                        |
| postPunchTravel                       | "$ptUrl/api/checkin/punch/travel"                                                                                                                                                        |
| postReCheckInApproval                 | "$ptUrl/api/reCheckInApproval/"                                                                                                                                                          |
| postSpecialLeaveRequestFormsAddFile   | "$ptUrl/api/SpecialLeaveRequestForms/AddFile?SpecialLeaveRequestFormId=$formID"                                                                                                          |
| postSpecialLeaveRequestForms          | "$ptUrl/api/SpecialLeaveRequestForms/"                                                                                                                                                   |
| postTripRequestForms                  | "$ptUrl/api/TripRequestForm"                                                                                                                                                             |
| postUploadPhoto                       | "$ptUrl/api/checkIn/punch/tablet/uploadPhoto?employeeId=$employeeId"                                                                                                                     |
| postUploadProfilePhoto                | "$tubeUrl/api/users/personalPicture"                                                                                                                                                     |
| confirmOvertimeRequestForms           | "$ptUrl/api/OvertimeRequestForms/Confirm/"                                                                                                                                               |
| deleteLeaveRequestForms               | "$ptUrl/api/LeaveRequestForms/Delete/$formID"                                                                                                                                            |
| deleteOvertimeRequestForms            | "$ptUrl/api/OvertimeRequestForms/Delete/$formID"                                                                                                                                         |
| deleteSpecialLeaveRequestForms        | "$ptUrl/api/SpecialLeaveRequestForms/Delete/$formID"                                                                                                                                     |
| putEmployeeCalendarsworkTime          | "$ptUrl/api/EmployeeCalendars/workTime"                                                                                                                                                  |
| putLeaveRequestForms                  | "$ptUrl/api/LeaveRequestForms"                                                                                                                                                           |
| deleteReCheckInRequestForms           | "$ptUrl/api/reCheckInApproval/$formID?date=$date&employeeId=$employeeId"                                                                                                                 |
| returnLeaveRequestForms               | "$ptUrl/api/LeaveRequestForms/Return/"                                                                                                                                                   |
| returnOvertimeRequestForms            | "$ptUrl/api/OvertimeRequestForms/Return/"                                                                                                                                                |
| returnTripRequestForms                | "$ptUrl/api/TripRequestForm/Return/"                                                                                                                                                     |
| revokeLeaveRequestForms               | "$ptUrl/api/LeaveRequestForms/Cancel/"                                                                                                                                                   |
| revokeOvertimeRequestForms            | "$ptUrl/api/OvertimeRequestForms/Cancel/"                                                                                                                                                |
| revokeTripRequestForms                | "$ptUrl/api/TripRequestForm/Cancel/"                                                                                                                                                     |
| putShiftSchedule                      | "$ptUrl/api/EmployeeCalendars/shiftSchedule"                                                                                                                                             |
| putSpecialLeaveRequestFormsDeleteFile | "$ptUrl/api/SpecialLeaveRequestForms/DeleteFile?SpecialLeaveRequestFormId=$formID&UploadFileId=$uploadFileId"                                                                            |
| putSpecialLeaveRequestForms           | "$ptUrl/api/SpecialLeaveRequestForms/"                                                                                                                                                   |
| putTripRequestForms                   | "$ptUrl/api/TripRequestForm"                                                                                                                                                             |
| getEmployeeAttendanceSettings         | "$ptUrl/api/EmployeeAttendanceSettings?GetDayStartTime=true"                                                                                                                             |
| getModuleRoleEmployees                | "$ptUrl/api/ModuleRoleEmployees/"                                                                                                                                                        |
| getPayrollNoticeViewAll               | "$pyUrl/api/PayrollNotices/ViewAll"                                                                                                                                                      |
| getPayrollNotice                      | "$pyUrl/api/PayrollNotices"                                                                                                                                                              |
| postPayrollDetailPassword             | "$pyUrl/api/PayrollNotices/Detail/Password"                                                                                                                                              |
| getAssignment                         | "$tubeUrl/api/assignment/${isFromMe ? "GetFromMe" : "GetToMe"}"                                                                                                                          |
| getAssignmentDetail                   | "$tubeUrl/api/assignment/${isFromMe ? "GetFromMe" : "GetToMe"}/$assignmentId"                                                                                                            |
| deleteAnnouncementOrdering            | "$tubeUrl/api/Ordering/$orderingId/Cancel"                                                                                                                                               |
| deleteAnnouncementErollment           | "$tubeUrl/api/NewsCompany/Enrollment?AnnouncementId=$announcementId"                                                                                                                     |
| getAnnouceOrdering                    | "$tubeUrl/api/Ordering/$orderingID"                                                                                                                                                      |
| getAnnoucementEnrollment              | "$tubeUrl/api/NewsDepartment/Enrollment/?AnnouncementId=$announcementID"                                                                                                                 |
| getAnnoucement                        | "$tubeUrl/api/NewsCompany/Announcement/$announcementID"                                                                                                                                  |
| getCompanyRuleDetail                  | "$tubeUrl/api/Tube_Regulations/Regulation/$crID"                                                                                                                                         |
| getCompanyRule                        | "$tubeUrl/api/Tube_Regulations/Regulation/TreeView"                                                                                                                                      |
| getDirectMember                       | "$tubeUrl/api/departments/directmemebers"                                                                                                                                                |
| getEnrollmentMyBasicData              | "$tubeUrl/api/NewsCompany/Enrollment/GetMyDataBasic"                                                                                                                                     |
| getInterviewResultDetail              | "$tubeUrl/api/ReviewWaitApproved?Language=zh-tw&WaitApprovedId=$waitApprovedId"                                                                                                          |
| getMember                             | "$tubeUrl/api/departments/members/$memberId"                                                                                                                                             |
| getMessageCenter                      | "$tubeUrl/api/messagecenter/datas"                                                                                                                                                       |
| getMyTeamFieldSetting                 | "$tubeUrl/api/fieldSettings/myteam?CurrentLanguageOnly=True"                                                                                                                             |
| getOrgTree                            | "$tubeUrl/api/departments/orgTree"                                                                                                                                                       |
| getPostNoteSended                     | "$tubeUrl/api/PostNote/Sended"                                                                                                                                                           |
| getPostNoteReceived                   | "$tubeUrl/api/PostNote/Received"                                                                                                                                                         |
| getPushStatus                         | "$tubeUrl/api/AppNotification/Status"                                                                                                                                                    |
| getReviewPendingData                  | "$tubeUrl/api/ReviewPendingData?CategoryCode=$categoryCode&Language=zh-tw&PageNumber=1&PageSize=1000"                                                                                    |
| getInterviewResultData                | "$tubeUrl/api/ReviewPendingData?CategoryCode=$categoryCode&Language=zh-tw&PageNumber=1&PageSize=1000"                                                                                    |
| getReviewWaitApprovedDetailData       | "$tubeUrl/api/ReviewWaitApproved?WaitApprovedId=$waitApprovedId&Language=zh-tw"                                                                                                          |
| getReviewProcessDetail                | "$tubeUrl/api/ReviewProgressQuery?Language=zh-tw&ProgressQueryId=$progressQueryId"                                                                                                       |
| getReviewProcess                      | "$tubeUrl/api/ReviewProgressQuery"                                                                                                                                                       |
| getReviewWaitApprovedDetail           | "$tubeUrl/api/ReviewWaitApproved?Language=zh-tw&WaitApprovedId=$waitApprovedId"                                                                                                          |
| getReviewWaitApproved                 | "$tubeUrl/api/ReviewWaitApproved"                                                                                                                                                        |
| getUserCertificate                    | "$tubeUrl/api/users/certificates"                                                                                                                                                        |
| getUserEducation                      | "$tubeUrl/api/users/education"                                                                                                                                                           |
| getWebBankStatus                      | "$tubeUrl/api/WebBanking/Status"                                                                                                                                                         |
| getPeopleSearch                       | "$tubeUrl/api/employees/peoplesearch?keyword=$keyword"                                                                                                                                   |
| getPeopleSearchByID                   | "$tubeUrl/api/employees/peoplesearch/$idStr"                                                                                                                                             |
| postAssignmentGetFromMe               | "$tubeUrl/api/Assignment/GetFromMe"                                                                                                                                                      |
| postAssignment                        | "$tubeUrl/api/Assignment/$assignmentId/Message"                                                                                                                                          |
| postPushRegistration                  | "$tubeUrl/api/AppNotification/Register"                                                                                                                                                  |
| postSendPostNote                      | "$tubeUrl/api/PostNote/Sended"                                                                                                                                                           |
| postUserContent                       | "$tubeUrl/api/users/content"                                                                                                                                                             |
| putAccountVerify                      | "$tubeUrl/api/users/account/verify"                                                                                                                                                      |
| putAnnouncementErollment              | "$tubeUrl/api/NewsCompany/Enrollment?AnnouncementId=$announcementId"                                                                                                                     |
| putAnnouncementOrdering               | "$tubeUrl/api/Ordering/$orderingId"                                                                                                                                                      |
| putAssignmentGetFromMe                | "$tubeUrl/api/Assignment/GetFromMe/$assignmentId/$status"                                                                                                                                |
| putAssignmentGetToMe                  | "$tubeUrl/api/Assignment/GetToMe/$assignmentId/$status"                                                                                                                                  |
| putBatchApproval                      | "$ptUrl/api/WaitApproveds/BatchApprove/app"                                                                                                                                              |
| putInterviewRecordSignOff             | "$tubeUrl/api/ReviewWaitApproved/SignOff?Language=zh-tw"                                                                                                                                 |
| putInterviewResuleSignOff             | "$tubeUrl/api/ReviewWaitApproved/SignOff"                                                                                                                                                |
| putMessageCenterReadAll               | "$tubeUrl/api/messagecenter/readall"                                                                                                                                                     |
| putPostNoteRead                       | "$tubeUrl/api/PostNote/Received/$postNoteID/Read"                                                                                                                                        |
| putReviewWaitApprovedSignOff          | "$tubeUrl/api/ReviewWaitApproved/SignOff"                                                                                                                                                |

---

若需進一步協助，可聯繫前任負責人（Luke Lin）
